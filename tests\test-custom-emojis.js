require('dotenv').config();

const { Client, GatewayIntentBits } = require('discord.js');

async function testCustomEmojis() {
    console.log('🧪 Testing Custom Emoji Management System...\n');

    const client = new Client({ intents: [GatewayIntentBits.Guilds] });
    await client.login(process.env.TOKEN);

    const testUserId = process.env.OWNER;
    let testsPassed = 0;
    let totalTests = 0;

    // Test 1: Build custom emojis container
    totalTests++;
    try {
        console.log('1️⃣ Testing custom emojis container building...');
        
        const { buildCustomEmojisContainer } = require('../commands/utility/owner-emojis.js');
        const container = await buildCustomEmojisContainer({
            page: 0,
            client: client,
            showBackButton: true
        });
        
        if (container && container.data) {
            console.log('   ✅ Custom emojis container built successfully');
            testsPassed++;
        } else {
            console.log('   ❌ Custom emojis container building failed');
            console.log('   📊 Container result:', container);
        }
    } catch (error) {
        console.log('   ❌ Custom emojis container error:', error.message);
    }

    // Test 2: Test emoji state management
    totalTests++;
    try {
        console.log('2️⃣ Testing emoji state management...');
        
        const { storeEmojiState, getEmojiState, clearEmojiState } = require('../commands/utility/owner-emojis.js');
        
        // Store test state
        const testState = {
            emojiName: 'test_emoji',
            emojiDescription: 'Test emoji description',
            emojiEmote: '<:test:123456789>',
            emojiEmoteId: '123456789'
        };
        
        storeEmojiState(testUserId, testState);
        const retrievedState = getEmojiState(testUserId);
        
        if (retrievedState && retrievedState.emojiName === 'test_emoji') {
            console.log('   ✅ Emoji state management working');
            testsPassed++;
        } else {
            console.log('   ❌ Emoji state management failed');
            console.log('   📊 Retrieved state:', retrievedState);
        }
        
        // Clean up
        clearEmojiState(testUserId);
    } catch (error) {
        console.log('   ❌ Emoji state management error:', error.message);
    }

    // Test 3: Test emoji editor container
    totalTests++;
    try {
        console.log('3️⃣ Testing emoji editor container...');
        
        const { buildEmojiEditorContainer } = require('../commands/utility/owner-emojis.js');
        const container = await buildEmojiEditorContainer(testUserId, client, 'create');
        
        if (container && container.data) {
            console.log('   ✅ Emoji editor container built successfully');
            testsPassed++;
        } else {
            console.log('   ❌ Emoji editor container building failed');
        }
    } catch (error) {
        console.log('   ❌ Emoji editor container error:', error.message);
    }

    // Test 4: Test emoji preview container
    totalTests++;
    try {
        console.log('4️⃣ Testing emoji preview container...');
        
        const { buildEmojiPreviewContainer } = require('../commands/utility/owner-emojis.js');
        const testEmoji = {
            id: 'test_emoji_id',
            name: 'test_emoji',
            description: 'Test emoji for preview',
            emote: '<:test:123456789>',
            emoteId: '123456789',
            createdAt: new Date(),
            createdBy: testUserId
        };
        
        const container = buildEmojiPreviewContainer(testEmoji);
        
        if (container && container.data) {
            console.log('   ✅ Emoji preview container built successfully');
            testsPassed++;
        } else {
            console.log('   ❌ Emoji preview container building failed');
        }
    } catch (error) {
        console.log('   ❌ Emoji preview container error:', error.message);
    }

    // Test 5: Test emoji database operations (without actually creating)
    totalTests++;
    try {
        console.log('5️⃣ Testing emoji database operations...');
        
        const { getEmojisForDisplay } = require('../commands/utility/owner-emojis.js');
        const emojiData = await getEmojisForDisplay(0, 10);
        
        if (emojiData && typeof emojiData.totalCount === 'number') {
            console.log(`   ✅ Emoji database operations working (found ${emojiData.totalCount} emojis)`);
            testsPassed++;
        } else {
            console.log('   ❌ Emoji database operations failed');
            console.log('   📊 Emoji data:', emojiData);
        }
    } catch (error) {
        console.log('   ❌ Emoji database operations error:', error.message);
    }

    // Test 6: Test owner features menu integration
    totalTests++;
    try {
        console.log('6️⃣ Testing owner features menu integration...');
        
        const owner = require('../commands/utility/owner.js');
        const ownerCode = require('fs').readFileSync('commands/utility/owner.js', 'utf8');
        
        if (ownerCode.includes('custom_emojis') && ownerCode.includes('custom emojis')) {
            console.log('   ✅ Custom emojis option found in owner features menu');
            testsPassed++;
        } else {
            console.log('   ❌ Custom emojis option not found in owner features menu');
        }
    } catch (error) {
        console.log('   ❌ Owner features menu integration error:', error.message);
    }

    // Test 7: Test interaction routing
    totalTests++;
    try {
        console.log('7️⃣ Testing interaction routing...');
        
        const interactionCode = require('fs').readFileSync('events/interactionCreate.js', 'utf8');
        
        const hasSelectRouting = interactionCode.includes('custom-emojis-select');
        const hasButtonRouting = interactionCode.includes('custom-emojis-back');
        const hasModalRouting = interactionCode.includes('emoji-') && interactionCode.includes('-modal');
        
        if (hasSelectRouting && hasButtonRouting && hasModalRouting) {
            console.log('   ✅ All emoji interaction routing found');
            testsPassed++;
        } else {
            console.log('   ❌ Some emoji interaction routing missing');
            console.log(`   📊 Select: ${hasSelectRouting}, Button: ${hasButtonRouting}, Modal: ${hasModalRouting}`);
        }
    } catch (error) {
        console.log('   ❌ Interaction routing test error:', error.message);
    }

    console.log(`\n📊 Test Results: ${testsPassed}/${totalTests} tests passed`);
    
    if (testsPassed === totalTests) {
        console.log('🎉 Custom emoji management system is working correctly!');
        console.log('💡 You can now access it through owner features > custom emojis');
        process.exit(0);
    } else {
        console.log('❌ Some tests failed. The custom emoji system may have issues.');
        process.exit(1);
    }
}

testCustomEmojis().catch(console.error);
