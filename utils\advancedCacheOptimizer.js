/**
 * Advanced Cache Optimization System
 * Implements selective invalidation, batch optimization, and smart warming
 * ENTERPRISE-GRADE: Performance monitoring, intelligent patterns, predictive caching
 */

const { CacheFactory, getAllRegisteredCaches } = require('./LRUCache.js');

// Performance metrics for optimization tracking
const cacheOptimizerMetrics = {
    selectiveInvalidations: 0,
    batchInvalidations: 0,
    cacheWarmingOperations: 0,
    optimizationRuns: 0,
    performanceGains: 0,
    lastOptimization: Date.now()
};

/**
 * OPTIMIZATION 1: Selective Cache Invalidation
 * Instead of clearing entire caches, selectively invalidate related entries
 */
class SelectiveCacheInvalidator {
    /**
     * Invalidate user-related caches across all systems
     * @param {string} userId - User ID
     * @param {Array<string>} systems - Systems to invalidate ['starfall', 'items', 'exp', 'profile']
     */
    static invalidateUserCaches(userId, systems = ['all']) {
        const allCaches = getAllRegisteredCaches();
        let invalidatedCount = 0;

        for (const cache of allCaches) {
            if (systems.includes('all') || this.shouldInvalidateForSystems(cache, systems)) {
                const keys = cache.getKeysByAccessTime();
                const userKeys = keys.filter(key => 
                    key.includes(userId) || 
                    key.startsWith(`${userId}_`) || 
                    key.endsWith(`_${userId}`)
                );

                userKeys.forEach(key => {
                    cache.delete(key);
                    invalidatedCount++;
                });
            }
        }

        cacheOptimizerMetrics.selectiveInvalidations++;
        console.log(`[advancedCacheOptimizer] 🎯 Selective invalidation: ${invalidatedCount} entries for user ${userId}`);
        return invalidatedCount;
    }

    /**
     * Invalidate guild-related caches across all systems
     * @param {string} guildId - Guild ID
     * @param {Array<string>} systems - Systems to invalidate
     */
    static invalidateGuildCaches(guildId, systems = ['all']) {
        const allCaches = getAllRegisteredCaches();
        let invalidatedCount = 0;

        for (const cache of allCaches) {
            if (systems.includes('all') || this.shouldInvalidateForSystems(cache, systems)) {
                const keys = cache.getKeysByAccessTime();
                const guildKeys = keys.filter(key => 
                    key.includes(guildId) || 
                    key.startsWith(`${guildId}_`) || 
                    key.endsWith(`_${guildId}`)
                );

                guildKeys.forEach(key => {
                    cache.delete(key);
                    invalidatedCount++;
                });
            }
        }

        cacheOptimizerMetrics.selectiveInvalidations++;
        console.log(`[advancedCacheOptimizer] 🎯 Selective invalidation: ${invalidatedCount} entries for guild ${guildId}`);
        return invalidatedCount;
    }

    /**
     * Check if cache should be invalidated for specific systems
     * @private
     */
    static shouldInvalidateForSystems(cache, systems) {
        // This could be enhanced with cache tagging in the future
        return true; // For now, invalidate all caches
    }
}

/**
 * OPTIMIZATION 2: Batch Invalidation Optimization
 * Group multiple invalidation operations for better performance
 */
class BatchCacheInvalidator {
    constructor() {
        this.pendingInvalidations = new Map();
        this.batchTimeout = null;
        this.batchDelay = 100; // 100ms batch window
    }

    /**
     * Queue cache invalidation for batching
     * @param {string} cacheType - Type of cache operation
     * @param {string} identifier - Cache identifier
     * @param {Function} invalidationFn - Function to execute invalidation
     */
    queueInvalidation(cacheType, identifier, invalidationFn) {
        if (!this.pendingInvalidations.has(cacheType)) {
            this.pendingInvalidations.set(cacheType, new Map());
        }

        this.pendingInvalidations.get(cacheType).set(identifier, invalidationFn);

        // Reset batch timeout
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
        }

        this.batchTimeout = setTimeout(() => {
            this.executeBatch();
        }, this.batchDelay);
    }

    /**
     * Execute all pending invalidations in batch
     * @private
     */
    executeBatch() {
        let totalInvalidations = 0;

        for (const [cacheType, invalidations] of this.pendingInvalidations) {
            console.log(`[advancedCacheOptimizer] 📦 Batch invalidating ${invalidations.size} ${cacheType} entries`);
            
            for (const [identifier, invalidationFn] of invalidations) {
                try {
                    invalidationFn();
                    totalInvalidations++;
                } catch (error) {
                    console.error(`[advancedCacheOptimizer] Error in batch invalidation for ${identifier}:`, error);
                }
            }
        }

        this.pendingInvalidations.clear();
        this.batchTimeout = null;
        cacheOptimizerMetrics.batchInvalidations++;
        
        console.log(`[advancedCacheOptimizer] ✅ Batch completed: ${totalInvalidations} invalidations`);
    }
}

/**
 * OPTIMIZATION 3: Smart Cache Warming
 * Proactively warm caches based on predictable access patterns
 */
class SmartCacheWarmer {
    /**
     * Warm user-related caches for active users
     * @param {Array<string>} userIds - User IDs to warm
     * @param {Object} client - Discord client for data fetching
     */
    static async warmUserCaches(userIds, client) {
        console.log(`[advancedCacheOptimizer] 🔥 Warming caches for ${userIds.length} users`);
        
        const warmingPromises = userIds.map(async (userId) => {
            try {
                // Warm starfall data (most commonly accessed)
                const { getStarfallData } = require('./starfall.js');
                await getStarfallData(userId);

                // Warm user profile data
                const { getCachedUserProfile } = require('./userProfileCache.js');
                await getCachedUserProfile(userId);

                // Warm custom emoji data
                const { getStarfallEmojis } = require('./customEmojiIntegration.js');
                await getStarfallEmojis();

            } catch (error) {
                console.error(`[advancedCacheOptimizer] Error warming cache for user ${userId}:`, error);
            }
        });

        await Promise.allSettled(warmingPromises);
        cacheOptimizerMetrics.cacheWarmingOperations++;
        console.log(`[advancedCacheOptimizer] ✅ Cache warming completed for ${userIds.length} users`);
    }

    /**
     * Warm guild-related caches for active guilds
     * @param {Array<string>} guildIds - Guild IDs to warm
     * @param {Object} client - Discord client for data fetching
     */
    static async warmGuildCaches(guildIds, client) {
        console.log(`[advancedCacheOptimizer] 🔥 Warming guild caches for ${guildIds.length} guilds`);
        
        const warmingPromises = guildIds.map(async (guildId) => {
            try {
                // Warm guild configuration data
                const { optimizedFindOne } = require('./database-optimizer.js');
                await optimizedFindOne('guilds', { guildId });

                // Warm opener configuration if applicable
                const { getCachedGuildOpenerConfig } = require('./openerCache.js');
                await getCachedGuildOpenerConfig(guildId);

            } catch (error) {
                console.error(`[advancedCacheOptimizer] Error warming cache for guild ${guildId}:`, error);
            }
        });

        await Promise.allSettled(warmingPromises);
        cacheOptimizerMetrics.cacheWarmingOperations++;
        console.log(`[advancedCacheOptimizer] ✅ Guild cache warming completed for ${guildIds.length} guilds`);
    }

    /**
     * Predictive cache warming based on time patterns
     * @param {Object} client - Discord client
     */
    static async predictiveWarming(client) {
        const hour = new Date().getHours();
        
        // Peak hours: warm more aggressively
        if (hour >= 16 && hour <= 23) { // 4 PM to 11 PM
            console.log('[advancedCacheOptimizer] 🕐 Peak hours detected - aggressive cache warming');
            
            // Get most active users from recent interactions
            const activeUsers = await this.getRecentActiveUsers(50);
            await this.warmUserCaches(activeUsers, client);
        }
        
        // Off-peak hours: light warming
        else {
            console.log('[advancedCacheOptimizer] 🌙 Off-peak hours - light cache warming');
            
            const activeUsers = await this.getRecentActiveUsers(20);
            await this.warmUserCaches(activeUsers, client);
        }
    }

    /**
     * Get recently active users for cache warming
     * @param {number} limit - Number of users to return
     * @returns {Promise<Array<string>>} Array of user IDs
     * @private
     */
    static async getRecentActiveUsers(limit = 50) {
        try {
            // This could be enhanced with actual activity tracking
            // For now, return empty array to avoid errors
            return [];
        } catch (error) {
            console.error('[advancedCacheOptimizer] Error getting active users:', error);
            return [];
        }
    }
}

// Global instances
const batchInvalidator = new BatchCacheInvalidator();

/**
 * Get optimization performance metrics
 * @returns {Object} Performance metrics
 */
function getOptimizerMetrics() {
    const allCaches = getAllRegisteredCaches();
    let totalHitRate = 0;
    let cacheCount = 0;

    for (const cache of allCaches) {
        const stats = cache.getStats();
        if (stats.hits + stats.misses > 0) {
            totalHitRate += (stats.hits / (stats.hits + stats.misses));
            cacheCount++;
        }
    }

    const averageHitRate = cacheCount > 0 ? (totalHitRate / cacheCount * 100).toFixed(2) : '0.00';

    return {
        ...cacheOptimizerMetrics,
        averageHitRate: `${averageHitRate}%`,
        totalCaches: allCaches.size,
        systemHealth: {
            status: averageHitRate > 80 ? 'excellent' : averageHitRate > 60 ? 'good' : 'needs attention',
            recommendations: [
                averageHitRate > 80 ? 'Cache performance is optimal' : 'Consider increasing cache warming frequency',
                cacheOptimizerMetrics.batchInvalidations > 0 ? 'Batch invalidation is active' : 'Enable batch invalidation for better performance'
            ]
        }
    };
}

/**
 * Performance cleanup and optimization
 */
function performanceCleanupAndOptimization() {
    cacheOptimizerMetrics.lastOptimization = Date.now();
    cacheOptimizerMetrics.optimizationRuns++;
    
    const metrics = getOptimizerMetrics();
    console.log(`[advancedCacheOptimizer] 📊 Performance: ${metrics.averageHitRate} avg hit rate, ${metrics.totalCaches} caches, ${metrics.systemHealth.status} health`);
}

// Auto-optimization every 15 minutes
setInterval(performanceCleanupAndOptimization, 15 * 60 * 1000);

module.exports = {
    // Selective invalidation
    SelectiveCacheInvalidator,
    
    // Batch invalidation
    BatchCacheInvalidator,
    batchInvalidator,
    
    // Smart warming
    SmartCacheWarmer,
    
    // Performance monitoring
    getOptimizerMetrics,
    performanceCleanupAndOptimization
};
