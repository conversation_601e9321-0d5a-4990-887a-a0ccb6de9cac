/**
 * Transcription Feature Handlers for /you Command
 * Extracted from you.js to improve code organization and maintainability
 * Handles all transcription-specific interactions and state management
 */

const { ContainerBuilder, TextDisplayBuilder } = require('discord.js');
const { buildTranscriptionContainer, processTranscription, checkTranscriptionQuota, invalidateVoiceMessageCache } = require('./transcription.js');
const { OPERATION_COLORS } = require('./colors.js');

/**
 * Transcription State Management
 * Manages selected voice message state for transcription processing
 */
class TranscriptionStateManager {
    constructor() {
        this.transcriptionStates = new Map();
    }

    /**
     * Store transcription state for a user
     * @param {string} userId - User ID
     * @param {Object} state - State object containing selectedMessageId and selectedAttachmentId
     */
    store(userId, state) {
        this.transcriptionStates.set(userId, state);
    }

    /**
     * Get transcription state for a user
     * @param {string} userId - User ID
     * @returns {Object|null} State object or null if not found
     */
    get(userId) {
        return this.transcriptionStates.get(userId) || null;
    }

    /**
     * Clear transcription state for a user
     * @param {string} userId - User ID
     */
    clear(userId) {
        this.transcriptionStates.delete(userId);
    }

    /**
     * Check if user has a valid selection
     * @param {string} userId - User ID
     * @returns {boolean} True if user has valid selection
     */
    hasValidSelection(userId) {
        const state = this.get(userId);
        return state && state.selectedMessageId && state.selectedAttachmentId;
    }
}

// Global state manager instance
const stateManager = new TranscriptionStateManager();

/**
 * Build transcription hub menu option
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Menu option object
 */
async function buildTranscriptionMenuOption(userId) {
    try {
        const quota = await checkTranscriptionQuota(userId);
        const transcriptionDescription = quota.isOwner ? 
            'unlimited voice transcriptions' : 
            `${quota.remaining} transcriptions left`;

        return {
            label: 'transcribe',
            value: 'transcribe',
            description: transcriptionDescription,
            emoji: '🎤'
        };
    } catch (error) {
        console.error('[transcriptionHandlers] Error building menu option:', error);
        return {
            label: 'transcribe',
            value: 'transcribe',
            description: 'voice message transcription',
            emoji: '🎤'
        };
    }
}

/**
 * Show transcription interface
 * @param {Object} interaction - Discord interaction
 * @param {Object} user - Discord user
 * @param {Function} buildYouHubMenu - Hub menu builder function
 * @param {Object} lastTranscription - Last transcription result (optional)
 * @returns {Promise<Array>} Array of components
 */
async function showTranscription(interaction, user, buildYouHubMenu, lastTranscription = null, clearState = true) {
    try {
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe', true);

        // Clear state on fresh load (when user selects transcribe from hub menu)
        // but keep it during transcription process updates
        if (clearState) {
            stateManager.clear(user.id);
            // Also invalidate voice message cache to ensure fresh results
            invalidateVoiceMessageCache(user.id, interaction.channel.id);
        }

        const selectedState = stateManager.get(user.id);
        const transcriptionContainers = await buildTranscriptionContainer(user.id, interaction, lastTranscription, selectedState);

        return [hubMenu, ...transcriptionContainers];
    } catch (error) {
        console.error('[transcriptionHandlers] Error showing transcription:', error);
        
        // Return error state
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe', true);
        const errorContainer = new ContainerBuilder()
            .setAccentColor(OPERATION_COLORS.DELETE)
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('**status:** ❌ Error loading transcription interface. Please try again.')
            );
        
        return [hubMenu, errorContainer];
    }
}

/**
 * Handle transcription voice message selection
 * @param {Object} interaction - Discord interaction
 * @param {Object} user - Discord user
 * @param {Function} buildYouHubMenu - Hub menu builder function
 * @returns {Promise<Array>} Array of components
 */
async function handleTranscriptionVoiceSelect(interaction, user, buildYouHubMenu) {
    try {
        console.log(`[transcriptionHandlers] 🎤 Voice select handler called for user ${user.id}`);
        const selectedValue = interaction.values[0];
        console.log(`[transcriptionHandlers] 📝 Selected value: ${selectedValue}`);

        // Handle "no-voice-messages" selection - just do nothing (following imageUploader pattern)
        if (selectedValue === 'no-voice-messages') {
            console.log(`[transcriptionHandlers] ❌ No voice messages selected`);
            // Clear any existing state and rebuild interface (clearState = true)
            return await showTranscription(interaction, user, buildYouHubMenu, null, true);
        }

        const [messageId, attachmentId] = selectedValue.split(':');
        console.log(`[transcriptionHandlers] 🎯 Parsed: messageId=${messageId}, attachmentId=${attachmentId}`);

        // Store the selected voice message in state
        stateManager.store(user.id, {
            selectedMessageId: messageId,
            selectedAttachmentId: attachmentId
        });
        console.log(`[transcriptionHandlers] 💾 State stored for user ${user.id}`);

        // Rebuild transcription container with enabled button (don't clear state)
        console.log(`[transcriptionHandlers] 🔄 Rebuilding transcription interface...`);
        const result = await showTranscription(interaction, user, buildYouHubMenu, null, false);
        console.log(`[transcriptionHandlers] ✅ Voice select completed successfully`);
        return result;

    } catch (error) {
        console.error('[transcriptionHandlers] ❌ Error handling voice select:', error);

        const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');
        const errorContainer = new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Error selecting voice message. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);

        return [hubMenu, errorContainer];
    }
}

/**
 * Handle transcription processing
 * @param {Object} interaction - Discord interaction
 * @param {Object} user - Discord user
 * @param {Function} buildYouHubMenu - Hub menu builder function
 * @returns {Promise<Array>} Array of components
 */
async function handleTranscriptionProcess(interaction, user, buildYouHubMenu) {
    try {
        // Get the selected voice message from state
        const state = stateManager.get(user.id);
        if (!state || !state.selectedMessageId || !state.selectedAttachmentId) {
            const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ No voice message selected. Please select a message first.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            
            return [hubMenu, errorContainer];
        }
        
        // Start background transcription process
        startBackgroundTranscription(interaction, user, state, buildYouHubMenu);

        // Return immediate "processing" response using the proper container structure
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');
        const containers = await buildTranscriptionContainer(user.id, interaction, null, state, '🎤 downloading voice message and starting transcription...');

        return [hubMenu, ...containers];
        
    } catch (error) {
        console.error('[transcriptionHandlers] Error processing transcription:', error);
        
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');
        const errorContainer = new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Error processing transcription. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
        
        return [hubMenu, errorContainer];
    }
}

/**
 * Start background transcription process
 * @param {Object} interaction - Discord interaction
 * @param {Object} user - Discord user
 * @param {Object} state - Selected voice message state
 * @param {Function} buildYouHubMenu - Hub menu builder function
 */
async function startBackgroundTranscription(interaction, user, state, buildYouHubMenu) {
    try {
        console.log(`[transcriptionHandlers] 🎤 Starting background transcription for user ${user.id}`);

        // Update status to "processing"
        setTimeout(async () => {
            try {
                const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');
                const containers = await buildTranscriptionContainer(user.id, interaction, null, state, '🔄 transcribing audio with whisper AI...');

                await interaction.editReply({
                    components: [hubMenu, ...containers],
                    flags: interaction.message.flags
                });
            } catch (error) {
                console.error('[transcriptionHandlers] Failed to update progress:', error);
            }
        }, 2000); // Update after 2 seconds

        // Process the transcription in background
        const result = await processTranscription(interaction, state.selectedMessageId, state.selectedAttachmentId);

        // Keep the state so user can transcribe more without accidentally repeating
        // stateManager.clear(user.id); // Commented out to maintain selection

        // Build result containers
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');

        if (result.success) {
            console.log(`[transcriptionHandlers] ✅ Background transcription completed successfully`);

            // Keep state so the transcribed message stays selected (prevents accidental re-transcription)
            // Build containers with transcription result (pass state to keep selection visible, disable button)
            const containers = await buildTranscriptionContainer(user.id, interaction, result, state, '✅ transcription completed successfully!', true);

            // Update the original interaction with results
            await interaction.editReply({
                components: [hubMenu, ...containers],
                flags: interaction.message.flags
            });

        } else {
            console.log(`[transcriptionHandlers] ❌ Background transcription failed: ${result.message}`);

            // Keep state on error so user can try again with same selection
            // Build containers with error status (pass state to maintain selection)
            const containers = await buildTranscriptionContainer(user.id, interaction, null, state, `❌ ${result.message}`);

            // Update the original interaction with error
            await interaction.editReply({
                components: [hubMenu, ...containers],
                flags: interaction.message.flags
            });
        }

    } catch (error) {
        console.error('[transcriptionHandlers] ❌ Background transcription error:', error);

        try {
            // Build error container for unexpected errors
            const hubMenu = await buildYouHubMenu(user.username, user.id, 'transcribe');
            const containers = await buildTranscriptionContainer(user.id, interaction, null, null, `❌ unexpected error: ${error.message}`);

            // Update the original interaction with error
            await interaction.editReply({
                components: [hubMenu, ...containers],
                flags: interaction.message.flags
            });

        } catch (editError) {
            console.error('[transcriptionHandlers] ❌ Failed to update interaction with error:', editError);
        }
    }
}

/**
 * Check if interaction is transcription-related
 * @param {string} customId - Interaction custom ID
 * @returns {boolean} True if transcription-related
 */
function isTranscriptionInteraction(customId) {
    return customId === 'transcription-voice-select' || customId === 'transcription-process';
}

/**
 * Route transcription interactions
 * @param {Object} interaction - Discord interaction
 * @param {Object} user - Discord user
 * @param {Function} buildYouHubMenu - Hub menu builder function
 * @returns {Promise<Array>} Array of components
 */
async function routeTranscriptionInteraction(interaction, user, buildYouHubMenu) {
    console.log(`[transcriptionHandlers] 🚦 Routing transcription interaction: ${interaction.customId}`);
    switch (interaction.customId) {
        case 'transcription-voice-select':
            console.log(`[transcriptionHandlers] 🎤 Routing to voice select handler`);
            return await handleTranscriptionVoiceSelect(interaction, user, buildYouHubMenu);
        case 'transcription-process':
            console.log(`[transcriptionHandlers] ⚙️ Routing to process handler`);
            return await handleTranscriptionProcess(interaction, user, buildYouHubMenu);
        default:
            console.error(`[transcriptionHandlers] ❌ Unknown transcription interaction: ${interaction.customId}`);
            throw new Error(`Unknown transcription interaction: ${interaction.customId}`);
    }
}

module.exports = {
    // State management
    stateManager,

    // Main functions
    buildTranscriptionMenuOption,
    showTranscription,
    handleTranscriptionVoiceSelect,
    handleTranscriptionProcess,
    startBackgroundTranscription,

    // Routing helpers
    isTranscriptionInteraction,
    routeTranscriptionInteraction
};
