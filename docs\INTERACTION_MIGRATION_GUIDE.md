# Universal Interaction Manager Migration Guide

## Overview
This guide shows how to migrate existing commands to use the new Universal Interaction Manager, which prevents all timeout and acknowledgment errors.

## Migration Patterns

### Pattern 1: Simple Command Migration

**BEFORE:**
```javascript
async execute(interaction) {
    try {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        
        // Do work
        const result = await doSomeWork();
        
        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [result]
        });
    } catch (error) {
        // Complex error handling...
    }
}
```

**AFTER:**
```javascript
const { handleUIOperation } = require('../../utils/interactionManager.js');

async execute(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // Do work
        const result = await doSomeWork();
        
        // Return components - manager handles the response
        return [result];
    }, {
        autoDefer: true,
        ephemeral: true,
        fallbackMessage: '❌ Something went wrong. Please try again.'
    });
}
```

### Pattern 2: Select Menu Handler Migration

**BEFORE:**
```javascript
async select(interaction) {
    try {
        const selectedValue = interaction.values[0];
        
        if (selectedValue === 'option1') {
            const container = await buildContainer();
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
        }
    } catch (error) {
        // Error handling...
    }
}
```

**AFTER:**
```javascript
const { handleUIOperation } = require('../../utils/interactionManager.js');

async select(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        const selectedValue = interaction.values[0];
        
        if (selectedValue === 'option1') {
            const container = await buildContainer();
            return [container];
        }
        
        return []; // Return empty array if no UI update needed
    });
}
```

### Pattern 3: Button Handler Migration

**BEFORE:**
```javascript
async buttons(interaction) {
    try {
        if (interaction.customId === 'save-item') {
            await interaction.deferUpdate();
            
            const result = await saveItem();
            const container = await buildResultContainer(result);
            
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
        }
    } catch (error) {
        // Error handling...
    }
}
```

**AFTER:**
```javascript
const { handleUIOperation } = require('../../utils/interactionManager.js');

async buttons(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        if (interaction.customId === 'save-item') {
            const result = await saveItem();
            const container = await buildResultContainer(result);
            return [container];
        }
        
        return []; // Return empty array if no UI update needed
    });
}
```

## Key Benefits

1. **Zero Timeout Errors**: Automatic deferring and timeout protection
2. **No Double Acknowledgment**: Intelligent state tracking prevents conflicts
3. **Consistent Error Handling**: Standardized error messages and fallbacks
4. **Preserved UI/UX**: All existing interface patterns maintained
5. **Clean Logs**: Only meaningful errors are logged

## Migration Checklist

For each command file:

- [ ] Import `handleUIOperation` from `interactionManager.js`
- [ ] Replace `execute()` function with new pattern
- [ ] Replace `select()` function with new pattern  
- [ ] Replace `buttons()` function with new pattern
- [ ] Remove old error handling code
- [ ] Remove direct `interaction.reply/update/editReply` calls
- [ ] Return components array instead of calling response methods
- [ ] Test thoroughly

## Priority Migration Order

1. **High Priority** (User-facing commands):
   - `/17` ✅ COMPLETED
   - `/items` (complex UI operations)
   - `/exp` (complex UI operations)
   - `/you` (complex UI operations)

2. **Medium Priority** (Admin commands):
   - `/owner` commands
   - Configuration commands

3. **Low Priority** (Simple commands):
   - Utility commands
   - Info commands

## Testing

After migration, verify:
- No timeout errors (10062)
- No acknowledgment errors (40060)
- UI functionality preserved
- Error messages appear correctly
- Performance maintained
