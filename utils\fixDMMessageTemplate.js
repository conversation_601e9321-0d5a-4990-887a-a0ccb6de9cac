/**
 * Fix DM message template format in database
 * Changes from: '🎁 You found {items}! Server: {server} | From: {location}'
 * To: 'You found {items} in **{server}**, dropped from {location}:'
 */


const { optimizedUpdateOne, optimizedUpdateMany, optimizedFindOne, optimizedCountDocuments } = require('./database-optimizer.js');

async function fixDMMessageTemplate() {
    try {
        console.log('[fixDMTemplate] Starting DM message template fix...');
        
        // Fix global owner config
        const ownerResult = await optimizedUpdateOne("item_notifications",
            {
                key: 'global',
                dmMessage: '🎁 You found {items}! Server: {server} | From: {location}'
            },
            {
                $set: {
                    dmMessage: 'You found {emoji} {items} in {server}, dropped from {location}:'
                }
            }
        );
        
        console.log(`[fixDMTemplate] Updated global owner config: ${ownerResult.modifiedCount} documents`);
        
        // Fix guild configs (if any exist with the old format)
        const guildResult = await optimizedUpdateMany("guilds",
            {
                'items.dmMessage': '🎁 You found {items}! Server: {server} | From: {location}'
            },
            {
                $set: {
                    'items.dmMessage': 'You found {emoji} {items} in {server}, dropped from {location}:'
                }
            }
        );
        
        console.log(`[fixDMTemplate] Updated guild configs: ${guildResult.modifiedCount} documents`);
        
        // Verify the fix
        const ownerConfig = await optimizedFindOne("item_notifications", { key: 'global' });
        const guildsWithOldFormat = await optimizedCountDocuments("guilds", {
            'items.dmMessage': '🎁 You found {items}! Server: {server} | From: {location}'
        });
        
        console.log('[fixDMTemplate] Verification:');
        console.log(`  - Global config message: "${ownerConfig?.dmMessage || 'not found'}"`);
        console.log(`  - Guilds with old format remaining: ${guildsWithOldFormat}`);
        
        if (guildsWithOldFormat === 0 && ownerConfig?.dmMessage === 'You found {emoji} {items} in {server}, dropped from {location}:') {
            console.log('[fixDMTemplate] ✅ DM message template successfully fixed!');
        } else {
            console.log('[fixDMTemplate] ⚠️  Some templates may still need fixing');
        }
        
        return {
            ownerConfigUpdated: ownerResult.modifiedCount,
            guildConfigsUpdated: guildResult.modifiedCount,
            remainingOldFormat: guildsWithOldFormat
        };
        
    } catch (error) {
        console.error('[fixDMTemplate] Error during fix:', error);
        throw error;
    }
}

// Export for use in other files
module.exports = {
    fixDMMessageTemplate
};

// Allow running directly with node
if (require.main === module) {
    const { connect } = require('../mongo/client.js');
    
    connect()
        .then(() => {
            console.log('[fixDMTemplate] Connected to database');
            return fixDMMessageTemplate();
        })
        .then((result) => {
            console.log('[fixDMTemplate] Fix completed:', result);
            process.exit(0);
        })
        .catch((error) => {
            console.error('[fixDMTemplate] Fix failed:', error);
            process.exit(1);
        });
}
