const { optimizedFind, optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require('./database-optimizer.js');
const { MessageFlags } = require('discord.js');

/**
 * SIMPLIFIED ITEM DROP SYSTEM
 * Clear separation between global and guild items with no cross-contamination
 */

/**
 * CORE PRINCIPLE: Items have TWO distinct contexts:
 * 1. GLOBAL ITEMS (guildId: null) - Only appear in DMs and notification center
 * 2. GUILD ITEMS (guildId: specific) - Only appear in that specific guild
 */

// ============================================================================
// SECTION 0: CORE DROP MECHANICS (Migrated from old system)
// ============================================================================

// Weight for "nothing" drops - controls overall item drop frequency
const NOTHING_WEIGHT = 8000;

/**
 * Perform single master roll to determine if any item drops and which one
 * @param {Array} droppableItems - Array of items that can drop
 * @param {number} dropChanceMultiplier - Global drop chance multiplier (default 1.0)
 * @returns {Object|null} Selected item or null if nothing drops
 */
function performMasterRoll(droppableItems, dropChanceMultiplier = 1.0) {
    if (droppableItems.length === 0) {
        return null;
    }

    // Get rarity definitions
    let RARITIES;
    try {
        ({ RARITIES } = require('../commands/utility/items.js'));
    } catch (error) {
        console.error('[itemDropsSimplified] Error loading RARITIES:', error);
        RARITIES = {};
    }

    // Calculate total weight including "nothing" weight
    const adjustedNothingWeight = Math.max(1, Math.floor(NOTHING_WEIGHT / dropChanceMultiplier));
    let totalWeight = adjustedNothingWeight;

    // Add item weights
    droppableItems.forEach(item => {
        // Handle both string rarity names and rarity objects
        let weight = 1; // Default weight
        if (typeof item.rarity === 'string') {
            const rarityData = RARITIES[item.rarity];
            weight = rarityData?.weight || 1;
        } else if (item.rarity?.weight) {
            weight = item.rarity.weight;
        }
        totalWeight += weight;
    });

    // Roll random number
    const roll = Math.floor(Math.random() * totalWeight) + 1;

    // Check if "nothing" was rolled
    if (roll <= adjustedNothingWeight) {
        return null; // Nothing drops
    }

    // Find which item was rolled
    let currentWeight = adjustedNothingWeight;
    for (const item of droppableItems) {
        // Handle both string rarity names and rarity objects
        let weight = 1; // Default weight
        if (typeof item.rarity === 'string') {
            const rarityData = RARITIES[item.rarity];
            weight = rarityData?.weight || 1;
        } else if (item.rarity?.weight) {
            weight = item.rarity.weight;
        }

        currentWeight += weight;
        if (roll <= currentWeight) {
            return item;
        }
    }

    return null; // Fallback
}

/**
 * Get all items that can drop from a specific location
 * @param {string} location - Drop location (TEXT, VOICE, etc.)
 * @param {string|null} guildId - Guild ID (null for global items)
 * @returns {Array} Array of items that can drop from this location
 */
async function getDroppableItems(location, guildId) {
    try {
        let query;
        if (guildId === null) {
            // Global context - only global items
            query = {
                dropLocations: location,
                disabled: { $ne: true },
                guildId: null
            };
        } else {
            // Guild context - guild items AND global items
            query = {
                dropLocations: location,
                disabled: { $ne: true },
                $or: [
                    { guildId: guildId },  // Guild-specific items
                    { guildId: null }      // Global items (can drop anywhere)
                ]
            };
        }

        const items = await optimizedFind("custom_items", query, {
            projection: {
                id: 1,
                name: 1,
                type: 1,
                rarity: 1,
                emote: 1,
                description: 1,
                parameters: 1,
                guildId: 1
            }
        });

        console.log(`[itemDropsSimplified] ✅ Found ${items.length} droppable items for ${location} in ${guildId ? 'guild' : 'global'} context`);
        return items;

    } catch (error) {
        console.error('[itemDropsSimplified] Error fetching droppable items:', error);
        return [];
    }
}

/**
 * Process item drops for EXP gain event (SIMPLIFIED)
 * @param {string} userId - User ID who gained EXP
 * @param {string} guildId - Guild ID where EXP was gained
 * @param {string} location - Drop location (TEXT, VOICE, etc.)
 * @param {number} expGained - Amount of EXP gained
 * @returns {Array} Array with single dropped item or empty array
 */
async function processItemDrops(userId, guildId, location, expGained) {
    try {
        // Validate EXP was actually gained
        if (!expGained || expGained <= 0) {
            console.log(`[itemDropsSimplified] ⚠️ No EXP gained (${expGained}), skipping item drops for ${userId}`);
            return [];
        }

        // Get droppable items and user boosters in parallel
        const [droppableItems, dropChanceMultiplier] = await Promise.allSettled([
            getDroppableItems(location, guildId),
            (async () => {
                try {
                    const { getUserBoosters } = require('./globalLevels.js');
                    const boosters = await getUserBoosters(userId);
                    return boosters.dropChanceMultiplier;
                } catch (boosterError) {
                    console.error('[itemDropsSimplified] Error getting drop chance booster:', boosterError);
                    return 1.0; // Default multiplier
                }
            })()
        ]);

        // Extract results
        const items = droppableItems.status === 'fulfilled' ? droppableItems.value : [];
        const multiplier = dropChanceMultiplier.status === 'fulfilled' ? dropChanceMultiplier.value : 1.0;

        if (items.length === 0) {
            return []; // No items configured for this location
        }

        // Perform master roll with booster applied
        const selectedItem = performMasterRoll(items, multiplier);

        if (!selectedItem) {
            return []; // Nothing dropped
        }

        // Add item to inventory
        const droppedItem = await addItemToInventory(userId, guildId, selectedItem, location);

        if (droppedItem) {
            console.log(`[itemDropsSimplified] ✅ ${userId} found ${selectedItem.name} from ${location}!`);

            // Send item drop log
            try {
                const { sendItemDropLog } = require('./sendLog.js');
                await sendItemDropLog(guildId, userId, droppedItem, location, expGained, false);
            } catch (logError) {
                console.error('[itemDropsSimplified] Error sending item drop log:', logError);
            }

            return [droppedItem];
        }

        return [];

    } catch (error) {
        console.error('[itemDropsSimplified] Error processing item drops:', error);
        return [];
    }
}

// ============================================================================
// SECTION 1: ITEM CREATION (Global vs Guild)
// ============================================================================

/**
 * Generate random parameters for caught items
 * @param {Object} itemData - Item data with parameters
 * @returns {Object} Randomized parameters
 */
function generateRandomParameters(itemData) {
    try {
        const { randomizeItemParameters } = require('../commands/utility/items.js');
        const randomizedItem = randomizeItemParameters(itemData);
        return randomizedItem.parameters || {};
    } catch (error) {
        console.error('[itemDropsSimplified] Error generating random parameters:', error);
        return {};
    }
}

/**
 * Add item to user's inventory (SIMPLIFIED)
 * @param {string} userId - User ID
 * @param {string|null} contextGuildId - Guild where item was found (null for global)
 * @param {Object} itemData - Item definition from database
 * @param {string} location - Drop location (TEXT, VOICE, LEVEL_UP, STARFALL)
 * @returns {Object|null} Created inventory item or null
 */
async function addItemToInventory(userId, contextGuildId, itemData, location) {
    try {
        // Create inventory item with clear context separation
        const inventoryItem = {
            id: `inv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            userId: userId,

            // CRITICAL: Item's inherent scope (global vs guild-specific)
            guildId: itemData.guildId, // null for global items, specific for guild items

            // CRITICAL: Context where item was found
            foundInGuild: contextGuildId, // null for global level-ups, specific for guild drops

            itemId: itemData.id,
            itemName: itemData.name,
            itemType: itemData.type,
            itemRarity: itemData.rarity,
            itemEmote: itemData.emote,
            itemDescription: itemData.description, // FIXED: Include description
            droppedAt: new Date(),
            droppedFrom: location,
            catchData: generateRandomParameters(itemData)
        };

        await optimizedInsertOne('user_inventory', inventoryItem);
        console.log(`[itemDropsSimplified] ✅ Added ${itemData.guildId ? 'GUILD' : 'GLOBAL'} item to inventory: ${itemData.name} (${location})`);
        
        return inventoryItem;
    } catch (error) {
        console.error('[itemDropsSimplified] Error adding item to inventory:', error);
        return null;
    }
}

// ============================================================================
// SECTION 2: NOTIFICATION ROUTING (Completely Separated)
// ============================================================================

/**
 * Process item drop notifications (SIMPLIFIED with clear separation)
 * @param {string} userId - User ID
 * @param {string|null} contextGuildId - Guild context (null for global)
 * @param {Object} droppedItem - Dropped item
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 */
async function processItemNotifications(userId, contextGuildId, droppedItem, location, client) {
    try {
        // RULE 1: Global items (guildId: null) = DMs only
        if (droppedItem.guildId === null) {
            console.log(`[itemDropsSimplified] 🌍 Processing GLOBAL item notification: ${droppedItem.itemName}`);
            
            // Add to notification center
            await addItemNotification(userId, null, droppedItem, location);
            
            // Send DM only
            await sendItemDM(userId, null, droppedItem, location, client);
            
            // NEVER send to guild channels
            return;
        }
        
        // RULE 2: Guild items (guildId: specific) = Guild notifications + DMs
        if (droppedItem.guildId && contextGuildId === droppedItem.guildId) {
            console.log(`[itemDropsSimplified] 🏰 Processing GUILD item notification: ${droppedItem.itemName} in guild ${contextGuildId}`);
            
            // Add to notification center
            await addItemNotification(userId, contextGuildId, droppedItem, location);
            
            // Send DM
            await sendItemDM(userId, contextGuildId, droppedItem, location, client);
            
            // Send guild channel notification
            await sendGuildNotification(userId, contextGuildId, droppedItem, location, client);
            
            return;
        }
        
        // RULE 3: Mismatched contexts = Error (should never happen)
        console.error(`[itemDropsSimplified] ❌ CONTEXT MISMATCH: Item guildId=${droppedItem.guildId}, context guildId=${contextGuildId}`);
        
    } catch (error) {
        console.error('[itemDropsSimplified] Error processing item notifications:', error);
    }
}

/**
 * Add item notification to queue (SIMPLIFIED)
 */
async function addItemNotification(userId, guildId, droppedItem, location) {
    try {
        const notification = {
            userId: userId,
            guildId: guildId, // null for global, specific for guild
            items: [droppedItem],
            location: location,
            createdAt: new Date(),
            viewed: false
        };

        await optimizedInsertOne("item_notifications_queue", notification);
        console.log(`[itemDropsSimplified] ✅ Added ${guildId ? 'GUILD' : 'GLOBAL'} notification for ${userId}`);
        
    } catch (error) {
        console.error('[itemDropsSimplified] Error adding notification:', error);
    }
}

/**
 * Send item DM (SIMPLIFIED)
 */
async function sendItemDM(userId, guildId, droppedItem, location, client) {
    try {
        // Check if DMs are enabled
        const dmEnabled = await isDMEnabled(userId);
        if (!dmEnabled) {
            console.log(`[itemDropsSimplified] DMs disabled for user ${userId}`);
            return false;
        }

        const user = await client.users.fetch(userId).catch(() => null);
        if (!user) {
            console.log(`[itemDropsSimplified] Could not fetch user ${userId}`);
            return false;
        }

        // Build DM message components (await the promise)
        const messageComponents = await buildItemDMMessage(droppedItem, guildId, location, client);
        const components = Array.isArray(messageComponents) ? messageComponents : [messageComponents];

        await user.send({
            flags: MessageFlags.IsComponentsV2,
            components: components
        });

        console.log(`[itemDropsSimplified] ✅ Sent ${guildId ? 'GUILD' : 'GLOBAL'} item DM to ${user.username}`);
        return true;

    } catch (error) {
        console.error('[itemDropsSimplified] Error sending DM:', error);
        return false;
    }
}

/**
 * Send guild channel notification (SIMPLIFIED - GUILD ONLY)
 */
async function sendGuildNotification(userId, guildId, droppedItem, location, client) {
    try {
        // SAFETY CHECK: Never send global items to guild channels
        if (guildId === null || droppedItem.guildId === null) {
            console.log(`[itemDropsSimplified] ❌ BLOCKED: Attempted to send global item to guild channel`);
            return false;
        }

        // Check if guild notifications are enabled
        const guildConfig = await getGuildDropConfig(guildId);
        if (!guildConfig.enabled || !guildConfig.channelId) {
            console.log(`[itemDropsSimplified] Guild notifications disabled for ${guildId}`);
            return false;
        }

        const guild = await client.guilds.fetch(guildId).catch(() => null);
        const channel = await client.channels.fetch(guildConfig.channelId).catch(() => null);
        
        if (!guild || !channel) {
            console.log(`[itemDropsSimplified] Could not fetch guild or channel`);
            return false;
        }

        // Build guild message (await the promise)
        const messageComponents = await buildItemGuildMessage(userId, droppedItem, location);
        const components = Array.isArray(messageComponents) ? messageComponents : [messageComponents];

        await channel.send({
            flags: MessageFlags.IsComponentsV2,
            allowedMentions: { parse: [] },
            components: components
        });
        
        console.log(`[itemDropsSimplified] ✅ Sent guild notification to #${channel.name}`);
        return true;
        
    } catch (error) {
        console.error('[itemDropsSimplified] Error sending guild notification:', error);
        return false;
    }
}

// ============================================================================
// SECTION 3: HELPER FUNCTIONS
// ============================================================================

/**
 * Generate random parameters for caught items
 * @param {Object} itemData - Item data with parameters
 * @returns {Object} Randomized parameters
 */
function generateRandomParameters(itemData) {
    try {
        const { randomizeItemParameters } = require('../commands/utility/items.js');
        const randomizedItem = randomizeItemParameters(itemData);
        return randomizedItem.parameters || {};
    } catch (error) {
        console.error('[itemDropsSimplified] Error generating random parameters:', error);
        return {};
    }
}

async function isDMEnabled(userId) {
    // Check user's DM preferences
    try {
        const userData = await optimizedFindOne('user_settings', { userId: userId });
        return userData?.itemDMNotificationsEnabled !== false; // Default to true
    } catch (error) {
        return true; // Default to enabled
    }
}

async function getGuildDropConfig(guildId) {
    try {
        const guildData = await optimizedFindOne('guilds', { id: guildId });
        return {
            enabled: guildData?.items?.dropNotificationsEnabled ?? false,
            channelId: guildData?.items?.dropChannel ?? null
        };
    } catch (error) {
        return { enabled: false, channelId: null };
    }
}

async function buildItemDMMessage(droppedItem, contextGuildId, location, client) {
    try {
        // Use static item container builder for DM notifications (no live totals needed)
        const { buildFoundItemContainer, DROP_LOCATIONS } = require('../commands/utility/items.js');

        // Convert location to display name
        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Get actual server name from context (where user found the item)
        let serverName = '**Server**'; // Fallback
        if (contextGuildId && client) {
            try {
                const guild = await client.guilds.fetch(contextGuildId);
                serverName = guild.name; // Don't add markdown here, template will handle it
            } catch (error) {
                console.error('[itemDropsSimplified] Could not fetch guild name:', error);
                serverName = 'Server';
            }
        } else if (!contextGuildId) {
            // This shouldn't happen for normal drops, but fallback to "Server"
            serverName = 'Server';
        }

        // Build context for DM (no server context since it's already in the message)
        const context = {
            user: 'You',
            server: null, // Don't show server in DM container - it's redundant
            location: locationText,
            timestamp: droppedItem.droppedAt
        };

        // Create item data structure
        const itemData = {
            itemName: droppedItem.itemName,
            itemType: droppedItem.itemType,
            rarity: droppedItem.itemRarity,
            emote: droppedItem.itemEmote,
            description: droppedItem.itemDescription,
            guildId: droppedItem.guildId
        };

        // Get custom DM message template
        let dmMessageTemplate;
        try {
            const { getCachedItemNotificationConfig } = require('./itemDropsHybrid.js');
            const ownerConfig = await getCachedItemNotificationConfig('global');
            dmMessageTemplate = ownerConfig?.dmMessage || 'You found {items} in {server}, dropped from {location}:';
        } catch (error) {
            console.error('[itemDropsSimplified] Error fetching DM template:', error);
            dmMessageTemplate = 'You found {items} in {server}, dropped from {location}:';
        }

        // Build contextual message using template
        const { TextDisplayBuilder } = require('discord.js');
        const { getArticle: getItemArticle } = require('../commands/utility/items.js');
        const article = getItemArticle(droppedItem.itemName);
        const itemText = `${article} ${droppedItem.itemEmote} **${droppedItem.itemName}**`;

        const contextMessage = dmMessageTemplate
            .replace('{items}', itemText)
            .replace('{server}', serverName)
            .replace('{location}', locationText);

        const contextComponent = new TextDisplayBuilder().setContent(contextMessage);

        // Build item container (separate component)
        const itemContainer = await buildFoundItemContainer(itemData, droppedItem.catchData || {}, context, droppedItem.leaderboardResults || null);

        // Return both components as array (like server notifications)
        return [contextComponent, itemContainer];
    } catch (error) {
        console.error('[itemDropsSimplified] ❌ Error building DM message:', error);

        // Fallback to simple message
        const { ContainerBuilder, TextDisplayBuilder } = require('discord.js');
        const content = guildId
            ? `You found ${droppedItem.itemEmote} **${droppedItem.itemName}** from ${location}!`
            : `You found ${droppedItem.itemEmote} **${droppedItem.itemName}** from global ${location}!`;

        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent(content));
    }
}

async function buildItemGuildMessage(userId, droppedItem, location) {
    try {
        // Use existing server item container builder for consistency
        const { buildServerFoundItemContainer, DROP_LOCATIONS } = require('../commands/utility/items.js');

        // Convert location to display name
        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Build context for guild notification
        const context = {
            user: `<@${userId}>`,
            server: 'Server',
            location: locationText,
            timestamp: droppedItem.droppedAt
        };

        // Create item data structure
        const itemData = {
            itemName: droppedItem.itemName,
            itemType: droppedItem.itemType,
            rarity: droppedItem.itemRarity,
            emote: droppedItem.itemEmote,
            description: droppedItem.itemDescription,
            guildId: droppedItem.guildId
        };

        // Use existing server container builder for consistency (await the promise)
        const [textComponent, itemContainer] = await buildServerFoundItemContainer(itemData, droppedItem.catchData || {}, context, null);
        return [textComponent, itemContainer];
    } catch (error) {
        console.error('[itemDropsSimplified] Error building guild message:', error);

        // Fallback to simple message
        const { ContainerBuilder, TextDisplayBuilder } = require('discord.js');
        const content = `<@${userId}> found ${droppedItem.itemEmote} **${droppedItem.itemName}** from ${location}!`;

        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent(content));
    }
}

// ============================================================================
// SECTION 4: INVENTORY MANAGEMENT (Migrated from old system)
// ============================================================================

/**
 * Get user's global inventory (items from all servers)
 * @param {string} userId - User ID
 * @returns {Array} Array of inventory items
 */
async function getUserGlobalInventory(userId) {
    try {
        const inventory = await optimizedFind('user_inventory',
            { userId: userId },
            { sort: { droppedAt: -1 } }
        );
        return inventory;
    } catch (error) {
        console.error('[itemDropsSimplified] Error getting global inventory:', error);
        return [];
    }
}

/**
 * Get user's guild-specific inventory
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Array} Array of inventory items
 */
async function getUserInventory(userId, guildId) {
    try {
        const inventory = await optimizedFind('user_inventory',
            { userId: userId, guildId: guildId },
            { sort: { droppedAt: -1 } }
        );
        return inventory;
    } catch (error) {
        console.error('[itemDropsSimplified] Error getting guild inventory:', error);
        return [];
    }
}

/**
 * Get user's item notifications
 * @param {string} userId - User ID
 * @param {string|null} guildId - Guild ID (null for global)
 * @returns {Array} Array of notifications
 */
async function getUserItemNotifications(userId, guildId = null) {
    try {
        const notifications = await optimizedFind('item_notifications_queue',
            { userId: userId, guildId: guildId, viewed: false },
            { sort: { createdAt: -1 } }
        );
        return notifications;
    } catch (error) {
        console.error('[itemDropsSimplified] Error getting notifications:', error);
        return [];
    }
}

/**
 * Dismiss item notification
 * @param {string} notificationId - Notification ID
 * @returns {boolean} Success status
 */
async function dismissItemNotification(notificationId) {
    try {
        const { ObjectId } = require('mongodb');

        // DEBUG: Log dismissal attempt
        if (process.env.NODE_ENV === 'development') {
            console.log(`[itemDropsSimplified] DEBUG: Attempting to dismiss notification ${notificationId}`);
        }

        const result = await optimizedUpdateOne('item_notifications_queue',
            { _id: new ObjectId(notificationId) },
            { $set: { viewed: true, viewedAt: new Date() } }
        );

        // DEBUG: Log dismissal result
        if (process.env.NODE_ENV === 'development') {
            console.log(`[itemDropsSimplified] DEBUG: Dismissal result:`, {
                matchedCount: result.matchedCount,
                modifiedCount: result.modifiedCount
            });
        }

        return result.modifiedCount > 0;
    } catch (error) {
        console.error('[itemDropsSimplified] Error dismissing notification:', error);
        return false;
    }
}

module.exports = {
    // Core drop mechanics
    performMasterRoll,
    getDroppableItems,
    processItemDrops,

    // Item management
    addItemToInventory,
    getUserGlobalInventory,
    getUserInventory,

    // Notification system
    processItemNotifications,
    addItemNotification,
    sendItemDM,
    sendGuildNotification,
    getUserItemNotifications,
    dismissItemNotification
};
