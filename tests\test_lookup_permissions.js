const { BotTestBase } = require('./shared/BotTestBase.js');

/**
 * Test suite for lookup command permission changes
 * Validates that the lookup command is now accessible to all users
 */
class LookupPermissionTester extends BotTestBase {
    constructor() {
        super('LookupPermissionTest');
    }

    /**
     * Test that regular users can execute the lookup command
     */
    async testRegularUserAccess() {
        const lookup = require('../commands/utility/lookup.js');
        
        // Create mock interaction for regular user (non-admin)
        const interaction = this.createMockInteraction(3, 'peep'); // Context menu type
        interaction.targetUser = {
            id: 'target-user-123',
            tag: 'TestUser#1234',
            displayAvatarURL: () => 'https://example.com/avatar.png',
            fetch: async () => {},
            createdTimestamp: Date.now() - 86400000, // 1 day ago
            fetchFlags: async () => ({ toArray: () => [] })
        };
        interaction.targetMember = {
            id: 'target-user-123',
            joinedTimestamp: Date.now() - 86400000,
            roles: { cache: new Map() },
            fetch: async () => {},
            presence: null
        };
        
        // Mock guild and user without admin permissions
        interaction.guild = { id: this.testGuild.id };
        interaction.user = { id: 'regular-user-456' }; // Different from target
        interaction.member = {
            permissions: { has: () => false } // No admin permissions
        };

        try {
            await lookup.execute(interaction);
            
            // Should succeed without permission errors
            console.log(`   Regular user access: ${interaction._responses.length} responses`);
            return interaction._responses.length > 0;
        } catch (error) {
            console.log(`   Regular user access error: ${error.message}`);
            return false;
        }
    }

    /**
     * Test that the command data doesn't require admin permissions
     */
    async testCommandDataPermissions() {
        const lookup = require('../commands/utility/lookup.js');
        
        // Check that defaultMemberPermissions is not set to Administrator
        const hasAdminRestriction = lookup.data.default_member_permissions !== null && 
                                   lookup.data.default_member_permissions !== undefined;
        
        console.log(`   Command has admin restriction: ${hasAdminRestriction}`);
        console.log(`   Command permissions: ${lookup.data.default_member_permissions || 'None (public)'}`);
        
        // Should return false (no admin restriction)
        return !hasAdminRestriction;
    }

    /**
     * Test self-lookup prevention still works
     */
    async testSelfLookupPrevention() {
        const lookup = require('../commands/utility/lookup.js');
        
        // Create mock interaction where user tries to lookup themselves
        const interaction = this.createMockInteraction(3, 'peep');
        const userId = 'same-user-123';
        
        interaction.targetUser = {
            id: userId,
            tag: 'SelfUser#1234',
            displayAvatarURL: () => 'https://example.com/avatar.png',
            fetch: async () => {},
            createdTimestamp: Date.now() - 86400000,
            fetchFlags: async () => ({ toArray: () => [] })
        };
        interaction.user = { id: userId }; // Same as target
        interaction.guild = { id: this.testGuild.id };

        try {
            await lookup.execute(interaction);

            // Should return error container with status message
            const hasErrorResponse = interaction._responses.length > 0 &&
                                    interaction._responses[0].components &&
                                    interaction._responses[0].components.some(component =>
                                        component.components && component.components.some(textComp =>
                                            textComp.data && textComp.data.content &&
                                            textComp.data.content.includes('no </you:')
                                        )
                                    );

            console.log(`   Self-lookup prevention works: ${hasErrorResponse}`);
            return hasErrorResponse;
        } catch (error) {
            console.log(`   Self-lookup prevention error: ${error.message}`);
            return false;
        }
    }

    /**
     * Test that command still works for users with admin permissions
     */
    async testAdminUserAccess() {
        const lookup = require('../commands/utility/lookup.js');
        
        // Create mock interaction for admin user
        const interaction = this.createMockInteraction(3, 'peep');
        interaction.targetUser = {
            id: 'target-user-789',
            tag: 'TargetUser#5678',
            displayAvatarURL: () => 'https://example.com/avatar.png',
            fetch: async () => {},
            createdTimestamp: Date.now() - 86400000,
            fetchFlags: async () => ({ toArray: () => [] })
        };
        interaction.targetMember = {
            id: 'target-user-789',
            joinedTimestamp: Date.now() - 86400000,
            roles: { cache: new Map() },
            fetch: async () => {},
            presence: null
        };
        
        interaction.guild = { id: this.testGuild.id };
        interaction.user = { id: 'admin-user-999' };
        interaction.member = {
            permissions: { has: () => true } // Has admin permissions
        };

        try {
            await lookup.execute(interaction);
            
            console.log(`   Admin user access: ${interaction._responses.length} responses`);
            return interaction._responses.length > 0;
        } catch (error) {
            console.log(`   Admin user access error: ${error.message}`);
            return false;
        }
    }

    /**
     * Run all lookup permission tests
     */
    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Command data permissions', test: () => this.testCommandDataPermissions() },
            { name: 'Regular user access', test: () => this.testRegularUserAccess() },
            { name: 'Admin user access', test: () => this.testAdminUserAccess() },
            { name: 'Self-lookup prevention', test: () => this.testSelfLookupPrevention() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
        const allPassed = this.results && this.results.length > 0 ? this.results.every(result => result.passed) : false;
        return allPassed;
    }
}

/**
 * Main execution function
 */
async function runLookupPermissionTest() {
    const tester = new LookupPermissionTester();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        
        console.log('\n📊 Lookup Permission Test Summary:');
        console.log(`✅ All tests passed: ${allPassed}`);
        console.log('🎯 Lookup command is now accessible to all users for social profile sharing!');
        
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ Lookup permission test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Export for use in other test suites
module.exports = { LookupPermissionTester };

// Run if called directly
if (require.main === module) {
    runLookupPermissionTest();
}
