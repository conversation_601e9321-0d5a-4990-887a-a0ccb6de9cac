# 🔧 Utility Features - Comprehensive Documentation

## Overview
The utility features provide essential server management and automation capabilities including logging, sticky roles/nicknames, dehoist functionality, and thread management. These features enhance server administration and user experience.

## Core Architecture

### Main Files
- `commands/utility/logs.js` - Event logging and audit trails
- `commands/utility/sticky.js` - Persistent nickname and role management
- `commands/utility/dehoist.js` - Username character filtering
- `commands/utility/opener.js` - Thread management and auto-bumping
- `commands/utility/clearData.js` - Data cleanup utilities
- `commands/utility/lookup.js` - Public user profile lookup for social sharing

## Logs System

### Overview
Comprehensive event logging system for server activities with configurable event types, channels, and formatting options.

### Key Functions

#### `execute(interaction)`
**Purpose**: Main logs configuration interface
**Features**:
- Event type selection and configuration
- Log channel assignment
- Message formatting options
- Bulk enable/disable operations

#### Event Types Supported
```javascript
const LOG_EVENTS = {
    // Message Events
    messageDelete: 'Message deletions',
    messageEdit: 'Message edits',
    messageBulkDelete: 'Bulk message deletions',
    
    // Member Events
    memberJoin: 'Member joins',
    memberLeave: 'Member leaves',
    memberUpdate: 'Member profile changes',
    
    // Role Events
    roleCreate: 'Role creation',
    roleDelete: 'Role deletion',
    roleUpdate: 'Role modifications',
    
    // Channel Events
    channelCreate: 'Channel creation',
    channelDelete: 'Channel deletion',
    channelUpdate: 'Channel modifications'
};
```

#### Configuration Structure
```javascript
{
    guildId: String,
    events: {
        [eventType]: {
            enabled: Boolean,
            channelId: String,
            format: String,
            includeAttachments: Boolean,
            pingRoles: [String]
        }
    },
    globalSettings: {
        defaultChannel: String,
        embedColor: Number,
        includeTimestamps: Boolean
    }
}
```

### Special Behaviors
- **Event Filtering**: Configurable event types with granular control
- **Channel Routing**: Different events can log to different channels
- **Message Formatting**: Customizable log message templates
- **Attachment Handling**: Optional attachment preservation for deleted messages

### Testing Requirements
- Event configuration interface testing
- Log message formatting validation
- Channel routing accuracy
- Performance with high-volume events

## Sticky System

### Overview
Maintains persistent nicknames and roles that survive user leaves/rejoins, ensuring consistent user identity and permissions.

### Key Functions

#### `execute(interaction)`
**Purpose**: Sticky system configuration and management
**Features**:
- Nickname persistence toggle
- Role persistence configuration
- Bulk user management
- Recovery statistics

#### Data Structure
```javascript
{
    guildId: String,
    userId: String,
    stickyData: {
        nickname: String,
        roles: [String],
        lastUpdated: Date,
        recoveryCount: Number
    },
    settings: {
        nicknameEnabled: Boolean,
        rolesEnabled: Boolean,
        excludedRoles: [String],
        autoCleanup: Boolean
    }
}
```

### Special Behaviors
- **Automatic Recovery**: Restores data when users rejoin
- **Role Filtering**: Excludes certain roles from persistence (e.g., temporary roles)
- **Cleanup System**: Removes old sticky data for inactive users
- **Bulk Operations**: Mass enable/disable for server management

### Internal Logic
```javascript
// On member leave - store data
async function storeStickyData(member) {
    const stickyData = {
        nickname: member.nickname,
        roles: member.roles.cache
            .filter(role => !EXCLUDED_ROLES.includes(role.id))
            .map(role => role.id),
        lastUpdated: new Date()
    };
    
    await stickyCol.updateOne(
        { guildId: member.guild.id, userId: member.id },
        { $set: stickyData },
        { upsert: true }
    );
}

// On member join - restore data
async function restoreStickyData(member) {
    const stickyData = await stickyCol.findOne({
        guildId: member.guild.id,
        userId: member.id
    });
    
    if (stickyData) {
        if (stickyData.nickname) {
            await member.setNickname(stickyData.nickname);
        }
        
        const rolesToAdd = stickyData.roles.filter(roleId => 
            member.guild.roles.cache.has(roleId)
        );
        
        if (rolesToAdd.length > 0) {
            await member.roles.add(rolesToAdd);
        }
    }
}
```

### Testing Requirements
- Nickname persistence across leave/rejoin
- Role recovery functionality
- Excluded role filtering
- Bulk operation performance

## Dehoist System

### Overview
Automatically removes special characters from usernames to prevent display manipulation and maintain alphabetical ordering.

### Key Functions

#### `execute(interaction)`
**Purpose**: Dehoist system configuration and bulk operations
**Features**:
- Character filtering configuration
- Bulk username scanning
- Whitelist management
- Statistics and reporting

#### Character Detection
```javascript
const HOIST_CHARACTERS = [
    '!', '@', '#', '$', '%', '^', '&', '*', '(', ')',
    '-', '_', '=', '+', '[', ']', '{', '}', '\\', '|',
    ';', ':', '"', "'", '<', '>', ',', '.', '?', '/',
    '~', '`', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
];

function isHoisted(username) {
    const firstChar = username.charAt(0);
    return HOIST_CHARACTERS.includes(firstChar);
}
```

### Special Behaviors
- **Automatic Scanning**: Periodic checks for hoisted usernames
- **Whitelist System**: Exclude specific users from dehoist actions
- **Bulk Processing**: Mass dehoist operations for server cleanup
- **Logging Integration**: Tracks dehoist actions for audit purposes

### Internal Logic
```javascript
async function dehoistUser(member) {
    const originalName = member.displayName;
    
    if (isHoisted(originalName)) {
        // Remove leading hoist characters
        const cleanName = originalName.replace(/^[^a-zA-Z]+/, '');
        const newName = cleanName || 'Dehoisted User';
        
        try {
            await member.setNickname(newName);
            
            // Log the action
            await logDehoistAction(member, originalName, newName);
            
            return { success: true, oldName: originalName, newName };
        } catch (error) {
            console.error('[dehoist] Failed to dehoist user:', error);
            return { success: false, error: error.message };
        }
    }
    
    return { success: false, reason: 'Not hoisted' };
}
```

### Testing Requirements
- Character detection accuracy
- Bulk processing performance
- Whitelist functionality
- Permission handling for nickname changes

## Opener System

### Overview
Thread management system that monitors thread activity and automatically bumps inactive threads to maintain visibility and engagement.

### Key Functions

#### `execute(interaction)`
**Purpose**: Thread opener configuration and management
**Features**:
- Thread monitoring setup
- Auto-bump configuration
- Activity threshold settings
- Thread statistics

#### Configuration Structure
```javascript
{
    guildId: String,
    channelId: String,
    settings: {
        enabled: Boolean,
        bumpInterval: Number,        // Minutes between bumps
        activityThreshold: Number,   // Messages needed to reset timer
        maxBumps: Number,           // Maximum bumps per thread
        bumpMessage: String,        // Custom bump message
        excludeChannels: [String]   // Channels to ignore
    },
    threads: [{
        threadId: String,
        lastActivity: Date,
        bumpCount: Number,
        nextBump: Date,
        isActive: Boolean
    }]
}
```

### Special Behaviors
- **Activity Monitoring**: Tracks message activity in threads
- **Smart Bumping**: Only bumps threads that need attention
- **Bump Limiting**: Prevents excessive bumping of inactive threads
- **Channel Exclusion**: Skip monitoring for specified channels

### Internal Logic
```javascript
// Monitor thread activity
async function checkThreadActivity(thread) {
    const lastMessage = await thread.messages.fetch({ limit: 1 });
    const timeSinceLastMessage = Date.now() - lastMessage.first()?.createdTimestamp;
    
    const config = await getOpenerConfig(thread.guildId, thread.parentId);
    const shouldBump = timeSinceLastMessage > (config.bumpInterval * 60 * 1000);
    
    if (shouldBump && config.enabled) {
        await bumpThread(thread, config);
    }
}

// Bump thread with activity check
async function bumpThread(thread, config) {
    const threadData = await getThreadData(thread.id);
    
    if (threadData.bumpCount >= config.maxBumps) {
        return { success: false, reason: 'Max bumps reached' };
    }
    
    try {
        await thread.send(config.bumpMessage || '⬆️ Bump');
        
        await updateThreadData(thread.id, {
            bumpCount: threadData.bumpCount + 1,
            lastBump: new Date(),
            nextBump: new Date(Date.now() + (config.bumpInterval * 60 * 1000))
        });
        
        return { success: true };
    } catch (error) {
        console.error('[opener] Failed to bump thread:', error);
        return { success: false, error: error.message };
    }
}
```

### Testing Requirements
- Thread detection and monitoring
- Bump timing accuracy
- Activity threshold validation
- Configuration interface testing

## Lookup System

### Overview
Public user profile lookup system that allows all users to view profiles, stats, and achievements for social sharing and comparison.

### Key Functions

#### `execute(interaction)`
**Purpose**: Context menu command for user profile lookup
**Features**:
- User information display (ID, creation date, badges)
- Server information (join date, roles, nickname, boost status)
- Bot-specific data (EXP, level, status, presence)
- Performance-optimized with enterprise-grade caching

#### Data Display Format
```javascript
// User Info Section
id: 123456789
name: User#1234
mention: @User
created: <t:timestamp:D>
badges: Verified Bot Developer, Early Supporter

// Server Info Section
joined: <t:timestamp:D>
roles: @Role1 @Role2 @Role3
nickname: CustomNick
boosting since: <t:timestamp:D>

// Bot Specifics Section
status: online
client: desktop, mobile
custom status: 🎮 Playing games
exp: 1500 | level: 5 | next: 250 EXP to level 6
```

### Special Behaviors
- **Public Access**: Available to all server members for social interaction
- **Self-Lookup Prevention**: Redirects users to `/you` command for self-profiles
- **Performance Optimized**: Multi-tier LRU caching with 70%+ cache hit rates
- **Graceful Degradation**: Handles missing data and service failures elegantly
- **Enterprise Monitoring**: Comprehensive performance analytics and optimization

### Internal Logic
**Caching Strategy**:
- Guild EXP config: 10 minutes cache
- Member data: 5 minutes cache
- EXP calculations: 15 minutes cache
- Parallel data fetching for optimal performance

**Permission Model**:
- No permission restrictions (public command)
- Context menu accessible on any user
- Self-lookup redirects to personal profile command

### Testing Requirements
- Public access validation for regular users
- Admin access confirmation (should still work)
- Self-lookup prevention functionality
- Performance benchmarks and cache efficiency
- Data accuracy across all display sections

## Clear Data System

### Overview
Administrative utility for cleaning up bot data, removing inactive users, and maintaining database health.

### Key Functions

#### `execute(interaction)`
**Purpose**: Data cleanup operations (owner-only)
**Features**:
- Inactive user removal
- Orphaned data cleanup
- Database optimization
- Backup before cleanup

### Special Behaviors
- **Owner-Only Access**: Restricted to bot owner for safety
- **Confirmation Required**: Multi-step confirmation for destructive operations
- **Backup Integration**: Automatic backup before major cleanup operations
- **Selective Cleanup**: Choose specific data types to clean

### Testing Requirements
- Owner permission validation
- Confirmation workflow testing
- Data integrity after cleanup
- Backup creation verification

## Testing Guidelines

### General Testing Patterns
```javascript
// Example utility feature test
class UtilityFeatureTester extends BotTestBase {
    async testLogsConfiguration() {
        const interaction = this.createMockInteraction(5, 'logs-select', ['configure']);
        await logs.select(interaction, []);
        
        // Validate configuration interface
        assert(interaction._responses.length > 0);
    }
    
    async testStickyDataPersistence() {
        // Test nickname/role persistence logic
        const memberData = { nickname: 'TestNick', roles: ['role1', 'role2'] };
        await storeStickyData(memberData);
        
        const restored = await restoreStickyData(memberData.userId);
        assert(restored.nickname === 'TestNick');
    }
}
```

### Performance Considerations
- Bulk operations should handle large datasets efficiently
- Event logging should not impact server performance
- Thread monitoring should scale with server size
- Database cleanup should be non-blocking

## Maintenance Requirements

### Regular Tasks
1. **Log Rotation**: Archive old log data to prevent database bloat
2. **Sticky Data Cleanup**: Remove data for users who haven't returned
3. **Thread Monitoring**: Optimize bump schedules based on activity patterns
4. **Performance Monitoring**: Track utility feature impact on bot performance

### Scaling Considerations
- Event logging volume management
- Sticky data storage optimization
- Thread monitoring efficiency
- Bulk operation performance tuning

## Development Guidelines

### Code Patterns
- Use batch operations for bulk processing
- Implement proper error handling for external operations
- Maintain audit trails for administrative actions
- Follow consistent configuration patterns across utilities

### Testing Standards
- Test both individual and bulk operations
- Validate permission boundaries
- Include performance testing for high-volume scenarios
- Test error recovery and graceful degradation
