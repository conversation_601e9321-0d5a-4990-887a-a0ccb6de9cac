/**
 * Redis Distributed Cache for Discovery Ranks
 * 
 * Phase 3.1: Enterprise-scale distributed caching with automatic fallback
 * to LRU cache for high availability and infinite scalability.
 * 
 * Features:
 * - Redis-backed distributed caching across multiple bot instances
 * - Automatic fallback to LRU cache if Redis is unavailable
 * - Intelligent cache invalidation strategies
 * - Performance monitoring and metrics
 * - Connection pooling and health monitoring
 */

// Graceful Redis fallback - use LRU cache if Redis is not available
let redis;
try {
    redis = require('redis');
} catch (error) {
    console.log('[RedisCache] ⚠️ Redis module not found, using LRU-only mode');
    redis = null;
}

const { LRUCache } = require('./LRUCache.js');

// Redis configuration
const REDIS_CONFIG = {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: process.env.REDIS_DB || 0,
    
    // Connection pooling
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    
    // Performance settings
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000
};

// Cache configuration
const CACHE_CONFIG = {
    discoveryTTL: 300, // 5 minutes for discovery ranks
    keyPrefix: 'discovery:',
    fallbackCacheSize: 10000, // Large LRU cache for fallback
    fallbackTTL: 300000 // 5 minutes in milliseconds
};

class RedisDiscoveryCache {
    constructor() {
        this.redisClient = null;
        this.fallbackCache = new LRUCache(CACHE_CONFIG.fallbackCacheSize, CACHE_CONFIG.fallbackTTL);
        this.isRedisAvailable = false;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 5;
        
        // Performance metrics
        this.metrics = {
            redisHits: 0,
            redisMisses: 0,
            fallbackHits: 0,
            fallbackMisses: 0,
            redisErrors: 0,
            totalRequests: 0,
            avgRedisTime: 0,
            avgFallbackTime: 0,
            startTime: Date.now()
        };
        
        this.initializeRedis();
    }

    /**
     * Initialize Redis connection with error handling
     */
    async initializeRedis() {
        // Skip Redis initialization if module not available
        if (!redis) {
            console.log('[RedisCache] 🔄 Redis not available, using LRU-only mode');
            this.isRedisAvailable = false;
            return;
        }

        try {
            console.log('[RedisCache] 🔄 Initializing Redis connection...');

            this.redisClient = redis.createClient(REDIS_CONFIG);
            
            // Event handlers
            this.redisClient.on('connect', () => {
                console.log('[RedisCache] ✅ Redis connected');
                this.isRedisAvailable = true;
                this.connectionAttempts = 0;
            });
            
            this.redisClient.on('error', (error) => {
                console.error('[RedisCache] ❌ Redis error:', error.message);
                this.isRedisAvailable = false;
                this.metrics.redisErrors++;
                
                // Attempt reconnection with exponential backoff
                if (this.connectionAttempts < this.maxConnectionAttempts) {
                    const delay = Math.pow(2, this.connectionAttempts) * 1000;
                    console.log(`[RedisCache] 🔄 Retrying connection in ${delay}ms...`);
                    setTimeout(() => this.initializeRedis(), delay);
                    this.connectionAttempts++;
                }
            });
            
            this.redisClient.on('end', () => {
                console.log('[RedisCache] 🔌 Redis connection ended');
                this.isRedisAvailable = false;
            });
            
            // Connect to Redis
            await this.redisClient.connect();
            
        } catch (error) {
            console.error('[RedisCache] ❌ Failed to initialize Redis:', error.message);
            console.log('[RedisCache] 🔄 Falling back to LRU cache only');
            this.isRedisAvailable = false;
        }
    }

    /**
     * Get discovery rank from cache (Redis first, LRU fallback)
     */
    async get(key) {
        this.metrics.totalRequests++;
        const fullKey = CACHE_CONFIG.keyPrefix + key;
        
        // Try Redis first if available
        if (this.isRedisAvailable && this.redisClient) {
            try {
                const startTime = Date.now();
                const result = await this.redisClient.get(fullKey);
                const redisTime = Date.now() - startTime;
                
                this.updateAvgTime('redis', redisTime);
                
                if (result) {
                    this.metrics.redisHits++;
                    console.log(`[RedisCache] ⚡ Redis hit for ${key} (${redisTime}ms)`);
                    return JSON.parse(result);
                } else {
                    this.metrics.redisMisses++;
                }
            } catch (error) {
                console.error(`[RedisCache] ❌ Redis get error for ${key}:`, error.message);
                this.metrics.redisErrors++;
                this.isRedisAvailable = false;
            }
        }
        
        // Fallback to LRU cache
        const startTime = Date.now();
        const result = this.fallbackCache.get(key);
        const fallbackTime = Date.now() - startTime;
        
        this.updateAvgTime('fallback', fallbackTime);
        
        if (result) {
            this.metrics.fallbackHits++;
            console.log(`[RedisCache] 🔄 LRU fallback hit for ${key} (${fallbackTime}ms)`);
            return result;
        } else {
            this.metrics.fallbackMisses++;
            return null;
        }
    }

    /**
     * Set discovery rank in cache (Redis + LRU for redundancy)
     */
    async set(key, value, ttl = CACHE_CONFIG.discoveryTTL) {
        const fullKey = CACHE_CONFIG.keyPrefix + key;
        const serializedValue = JSON.stringify(value);
        
        // Set in Redis if available
        if (this.isRedisAvailable && this.redisClient) {
            try {
                await this.redisClient.setEx(fullKey, ttl, serializedValue);
                console.log(`[RedisCache] ✅ Redis set for ${key} (TTL: ${ttl}s)`);
            } catch (error) {
                console.error(`[RedisCache] ❌ Redis set error for ${key}:`, error.message);
                this.metrics.redisErrors++;
                this.isRedisAvailable = false;
            }
        }
        
        // Always set in LRU cache as backup
        this.fallbackCache.set(key, value);
        console.log(`[RedisCache] 💾 LRU backup set for ${key}`);
    }

    /**
     * Clear specific cache key
     */
    async clear(key) {
        const fullKey = CACHE_CONFIG.keyPrefix + key;
        
        // Clear from Redis
        if (this.isRedisAvailable && this.redisClient) {
            try {
                await this.redisClient.del(fullKey);
                console.log(`[RedisCache] 🗑️ Redis cleared for ${key}`);
            } catch (error) {
                console.error(`[RedisCache] ❌ Redis clear error for ${key}:`, error.message);
            }
        }
        
        // Clear from LRU cache
        this.fallbackCache.delete(key);
        console.log(`[RedisCache] 🗑️ LRU cleared for ${key}`);
    }

    /**
     * Clear all discovery cache
     */
    async clearAll() {
        // Clear Redis pattern
        if (this.isRedisAvailable && this.redisClient) {
            try {
                const keys = await this.redisClient.keys(CACHE_CONFIG.keyPrefix + '*');
                if (keys.length > 0) {
                    await this.redisClient.del(keys);
                    console.log(`[RedisCache] 🗑️ Redis cleared ${keys.length} discovery keys`);
                }
            } catch (error) {
                console.error('[RedisCache] ❌ Redis clearAll error:', error.message);
            }
        }
        
        // Clear LRU cache
        this.fallbackCache.clear();
        console.log('[RedisCache] 🗑️ LRU cache cleared');
    }

    /**
     * Update average timing metrics
     */
    updateAvgTime(type, time) {
        const metricKey = `avg${type.charAt(0).toUpperCase() + type.slice(1)}Time`;
        const currentAvg = this.metrics[metricKey];
        const requestKey = `${type}Hits`;
        const requestCount = this.metrics[requestKey];
        
        // Calculate running average
        this.metrics[metricKey] = requestCount > 0 
            ? ((currentAvg * (requestCount - 1)) + time) / requestCount
            : time;
    }

    /**
     * Get comprehensive cache statistics
     */
    getStats() {
        const uptime = Date.now() - this.metrics.startTime;
        const totalRedisRequests = this.metrics.redisHits + this.metrics.redisMisses;
        const totalFallbackRequests = this.metrics.fallbackHits + this.metrics.fallbackMisses;
        const redisHitRate = totalRedisRequests > 0 ? (this.metrics.redisHits / totalRedisRequests) * 100 : 0;
        const fallbackHitRate = totalFallbackRequests > 0 ? (this.metrics.fallbackHits / totalFallbackRequests) * 100 : 0;
        const overallHitRate = this.metrics.totalRequests > 0 
            ? ((this.metrics.redisHits + this.metrics.fallbackHits) / this.metrics.totalRequests) * 100 
            : 0;

        return {
            // Connection status
            redisAvailable: this.isRedisAvailable,
            connectionAttempts: this.connectionAttempts,
            
            // Performance metrics
            redisHitRate: redisHitRate.toFixed(1) + '%',
            fallbackHitRate: fallbackHitRate.toFixed(1) + '%',
            overallHitRate: overallHitRate.toFixed(1) + '%',
            
            // Request counts
            totalRequests: this.metrics.totalRequests,
            redisHits: this.metrics.redisHits,
            redisMisses: this.metrics.redisMisses,
            fallbackHits: this.metrics.fallbackHits,
            fallbackMisses: this.metrics.fallbackMisses,
            redisErrors: this.metrics.redisErrors,
            
            // Timing
            avgRedisTime: this.metrics.avgRedisTime.toFixed(2) + 'ms',
            avgFallbackTime: this.metrics.avgFallbackTime.toFixed(2) + 'ms',
            
            // System info
            uptime: Math.round(uptime / 1000) + 's',
            fallbackCacheSize: this.fallbackCache.cache.size,
            maxFallbackSize: CACHE_CONFIG.fallbackCacheSize
        };
    }

    /**
     * Health check for monitoring
     */
    async healthCheck() {
        const health = {
            redis: false,
            fallback: true,
            overall: true
        };

        // Test Redis
        if (this.isRedisAvailable && this.redisClient) {
            try {
                await this.redisClient.ping();
                health.redis = true;
            } catch (error) {
                console.error('[RedisCache] ❌ Redis health check failed:', error.message);
                this.isRedisAvailable = false;
            }
        }

        return health;
    }

    /**
     * Graceful shutdown
     */
    async shutdown() {
        console.log('[RedisCache] 🔄 Shutting down...');
        
        if (this.redisClient) {
            try {
                await this.redisClient.quit();
                console.log('[RedisCache] ✅ Redis connection closed gracefully');
            } catch (error) {
                console.error('[RedisCache] ❌ Error closing Redis connection:', error.message);
            }
        }
        
        this.fallbackCache.clear();
        console.log('[RedisCache] ✅ Shutdown complete');
    }
}

// Create singleton instance
const redisDiscoveryCache = new RedisDiscoveryCache();

module.exports = {
    RedisDiscoveryCache,
    redisDiscoveryCache
};
