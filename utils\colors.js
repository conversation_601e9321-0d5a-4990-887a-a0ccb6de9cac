/**
 * Global color constants for consistent theming across the bot
 * Centralizes all color definitions to provide better control and consistency
 */

// Main operation colors - used for container accent colors and status indicators
const OPERATION_COLORS = {
    ADD: 0x69FF69,      // Green for add/create operations
    EDIT: 0xFFB469,     // Orange for edit/update operations
    DELETE: 0xff6969,   // Red for delete/remove operations
    NEUTRAL: 0x6969ff,  // Default purple theme for neutral operations
    ENTITY: 0xffff69    // Yellow for main entity containers (/17 and /you)
};

// Log and status colors - used for logging, embeds, and status messages
const LOG_COLORS = {
    SUCCESS: 0x00D166,      // Green for success states
    ERROR: 0xED4245,        // Red for errors and failures
    WARNING: 0xF39C12,      // Orange for warnings and updates
    INFO: 0x5865F2,         // Discord blurple for info
    DISABLED: 0x95A5A6,     // Gray for disabled states
    PREMIUM: 0xF1C40F       // Gold for premium features
};

// Item rarity colors - used in items system for rarity indicators
const RARITY_COLORS = {
    COMMON: 0xA9A9A9,       // Gray
    UNCOMMON: 0x7FCF6B,     // Light green
    RARE: 0x4b69ff,         // Blue
    MYTHICAL: 0x8847ff,     // Purple
    GALACTIC: 0xeb4b4b,     // Red
    UNKNOWN: 0xFF46E3,      // Pink/magenta
    NONE: 0x6969ff          // Neutral blue (same as OPERATION_COLORS.NEUTRAL)
};

// Legacy/specific colors - for backwards compatibility and specific use cases
const LEGACY_COLORS = {
    DISCORD_BLURPLE: 0x7289da,  // Original Discord brand color
    BOT_THEME: 0x6969ff         // Bot's main theme color (same as NEUTRAL)
};

// Export all color constants
module.exports = {
    OPERATION_COLORS,
    LOG_COLORS,
    RARITY_COLORS,
    LEGACY_COLORS,

    // Export individual color sets for convenience
    ...OPERATION_COLORS,
    ...LOG_COLORS,
    ...RARITY_COLORS,
    ...LEGACY_COLORS
};
