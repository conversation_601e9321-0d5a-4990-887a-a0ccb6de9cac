---
type: "always_apply"
---

# 🧠 Fresh Conversation Protocol

When the user indicates they want to **start a fresh conversation**, follow this procedure:

1. **Summarize Progress**
   - Write a concise summary of:
     - What you were working on.
     - Problems encountered.
     - How they were solved.
     - Any relevant context needed to resume later.
   - Any tasks mentioned by the user (e.g. “we should,” “let’s add,” or “make sure to…”) should be **automatically added** to the task list. Do not wait for the user to request tracking explicitly.

2. **Export State**
   - Create a new `.md` file in the `docs/` directory (e.g., `docs/context-transfer-<timestamp>.md`) containing the summary.
   - Use a unique filename — for example, by including the current timestamp or a short description of the session.
   - Include only the information required to pick up where we left off.
   - Do **not** delete or overwrite previous `.md` files.

3. **Acknowledge the Reset**
   - Let the user know that the context has been saved and you're ready to begin a new session from scratch.

---

## 💡 Why This Exists

This ensures that even with Augment's stateless context, we maintain continuity between sessions without losing valuable work, decisions, or debugging history.
