/**
 * Discovery Rank Calculation Worker Thread
 * 
 * Phase 3.3: Worker thread for parallel discovery rank calculations
 * in the event-driven microservices architecture.
 * 
 * This worker runs in a separate thread to avoid blocking the main
 * event loop during intensive discovery rank calculations.
 */

const { parentPort } = require('worker_threads');
const { optimizedCountDocuments } = require('./database-optimizer.js');

// Worker configuration
const WORKER_CONFIG = {
    maxCalculationsPerBatch: 10,
    calculationTimeout: 30000, // 30 seconds
    retryAttempts: 2
};

/**
 * Calculate discovery rank for an item (worker thread version)
 * This is a standalone version that doesn't use caching (handled by main thread)
 */
async function calculateDiscoveryRankWorker(item, guildId) {
    try {
        const startTime = Date.now();
        
        // Guild discovery: Count items with this name found in the SAME guild where this item was found
        const actualFoundInGuild = item.foundInGuild || guildId;

        const guildRank = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName,
            foundInGuild: actualFoundInGuild,
            droppedAt: { $lt: new Date(item.droppedAt) }
        }) + 1;

        const guildTotal = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName,
            foundInGuild: actualFoundInGuild
        });

        // Global discovery: Count items with this name found anywhere before this timestamp
        const globalRank = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName,
            droppedAt: { $lt: new Date(item.droppedAt) }
        }) + 1;

        const globalTotal = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName
        });

        const duration = Date.now() - startTime;
        const result = {
            guildRank,
            guildTotal,
            globalRank,
            globalTotal
        };

        console.log(`[DiscoveryWorker] ✅ Calculated ranks for ${item.itemName} (${duration}ms):`, result);
        return result;

    } catch (error) {
        console.error('[DiscoveryWorker] ❌ Error calculating discovery rank:', error);
        throw error;
    }
}

/**
 * Process discovery rank calculation request
 */
async function processCalculationRequest(requestData) {
    const { requestId, userId, itemData, guildId, timestamp } = requestData;
    
    try {
        console.log(`[DiscoveryWorker] 🔄 Processing discovery calculation for ${itemData.itemName}`);
        
        // Create item object for calculation
        const item = {
            itemName: itemData.itemName,
            droppedAt: timestamp,
            foundInGuild: itemData.foundInGuild || guildId,
            _id: itemData._id || 'worker_calc'
        };
        
        // Calculate discovery ranks
        const discoveryRanks = await calculateDiscoveryRankWorker(item, guildId);
        
        // Send success result back to main thread
        parentPort.postMessage({
            requestId,
            success: true,
            data: discoveryRanks
        });
        
    } catch (error) {
        console.error(`[DiscoveryWorker] ❌ Calculation failed for ${itemData.itemName}:`, error);
        
        // Send error result back to main thread
        parentPort.postMessage({
            requestId,
            success: false,
            error: error.message
        });
    }
}

/**
 * Process batch calculation request
 */
async function processBatchCalculationRequest(batchData) {
    const { batchId, requests } = batchData;
    const results = [];
    
    console.log(`[DiscoveryWorker] 🔄 Processing batch of ${requests.length} calculations`);
    
    for (const request of requests) {
        try {
            const result = await processCalculationRequest(request);
            results.push({
                requestId: request.requestId,
                success: true,
                data: result
            });
        } catch (error) {
            results.push({
                requestId: request.requestId,
                success: false,
                error: error.message
            });
        }
    }
    
    // Send batch results back to main thread
    parentPort.postMessage({
        type: 'BATCH_RESULT',
        batchId,
        results
    });
}

/**
 * Handle health check request
 */
function handleHealthCheck() {
    parentPort.postMessage({
        type: 'HEALTH_CHECK_RESPONSE',
        status: 'healthy',
        timestamp: Date.now(),
        memoryUsage: process.memoryUsage()
    });
}

/**
 * Message handler for worker thread
 */
parentPort.on('message', async (message) => {
    const { type, data } = message;
    
    try {
        switch (type) {
            case 'CALCULATE_DISCOVERY_RANK':
                await processCalculationRequest(data);
                break;
                
            case 'BATCH_CALCULATE_DISCOVERY_RANKS':
                await processBatchCalculationRequest(data);
                break;
                
            case 'HEALTH_CHECK':
                handleHealthCheck();
                break;
                
            case 'SHUTDOWN':
                console.log('[DiscoveryWorker] 🔄 Shutting down worker...');
                process.exit(0);
                break;
                
            default:
                console.error(`[DiscoveryWorker] ❌ Unknown message type: ${type}`);
                break;
        }
    } catch (error) {
        console.error('[DiscoveryWorker] ❌ Error processing message:', error);
        
        // Send error response
        parentPort.postMessage({
            type: 'ERROR',
            error: error.message,
            originalMessage: message
        });
    }
});

// Worker startup
console.log('[DiscoveryWorker] 🚀 Discovery rank calculation worker started');

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('[DiscoveryWorker] ❌ Uncaught exception:', error);
    
    parentPort.postMessage({
        type: 'WORKER_ERROR',
        error: error.message,
        stack: error.stack
    });
    
    process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('[DiscoveryWorker] ❌ Unhandled rejection at:', promise, 'reason:', reason);
    
    parentPort.postMessage({
        type: 'WORKER_ERROR',
        error: reason.message || reason,
        type: 'unhandledRejection'
    });
});

// Graceful shutdown on SIGTERM
process.on('SIGTERM', () => {
    console.log('[DiscoveryWorker] 🔄 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

module.exports = {
    calculateDiscoveryRankWorker,
    processCalculationRequest,
    processBatchCalculationRequest
};
