/**
 * Global Data Persistence Bug Verification Test
 * 
 * This test verifies that the clearMemberData() function properly clears
 * all global and user-specific data, including:
 * - global_user_data (global EXP, stars, boosters)
 * - global_level_notifications
 * - item_notifications_queue
 * - exp_main_msgs (EXP message tracking)
 * - user_images_cache (user image cache)
 * - item_creation_state (item creation temporary state)
 */

const { mongoClient } = require('../mongo/client.js');
const { clearMemberData } = require('../commands/utility/clearData.js');
const { optimizedInsertOne, optimizedFindOne, optimizedCountDocuments } = require('../utils/database-optimizer.js');

class GlobalDataPersistenceTest {
    constructor() {
        this.testUserId = 'test_user_123456789';
        this.testGuildId = 'test_guild_987654321';
        this.testCollections = [
            'member',
            'user_inventory', 
            'item_leaderboards',
            'global_user_data',
            'global_level_notifications',
            'item_notifications_queue',
            'exp_main_msgs',
            'user_images_cache',
            'item_creation_state'
        ];
    }

    /**
     * Run the complete test suite
     */
    async runTest() {
        console.log('🧪 Starting Global Data Persistence Bug Verification Test...\n');

        try {
            // Step 1: Setup test data
            await this.setupTestData();
            console.log('✅ Test data setup complete\n');

            // Step 2: Verify test data exists
            const beforeCounts = await this.countTestData();
            console.log('📊 Data counts before deletion:');
            this.displayCounts(beforeCounts);

            // Step 3: Run clearMemberData()
            console.log('\n🗑️  Running clearMemberData()...');
            const clearStats = await clearMemberData();
            console.log('✅ clearMemberData() completed');
            console.log('📈 Clear statistics:', clearStats);

            // Step 4: Verify all data is cleared
            const afterCounts = await this.countTestData();
            console.log('\n📊 Data counts after deletion:');
            this.displayCounts(afterCounts);

            // Step 5: Analyze results
            const testResult = this.analyzeResults(beforeCounts, afterCounts, clearStats);
            
            if (testResult.success) {
                console.log('\n🎉 ✅ GLOBAL DATA PERSISTENCE TEST PASSED!');
                console.log('All global and user-specific data was properly cleared.');
            } else {
                console.log('\n❌ GLOBAL DATA PERSISTENCE TEST FAILED!');
                console.log('Issues found:', testResult.issues);
            }

            return testResult;

        } catch (error) {
            console.error('❌ Test failed with error:', error);
            return { success: false, error: error.message };
        } finally {
            // Cleanup any remaining test data
            await this.cleanupTestData();
        }
    }

    /**
     * Setup test data in all relevant collections
     */
    async setupTestData() {
        // Member data
        await optimizedInsertOne('member', {
            guildId: this.testGuildId,
            userId: this.testUserId,
            exp: { total: 1000 }
        });

        // User inventory
        await optimizedInsertOne('user_inventory', {
            userId: this.testUserId,
            guildId: this.testGuildId,
            itemId: 'test_item_123'
        });

        // Global user data (most important for this test)
        await optimizedInsertOne('global_user_data', {
            userId: this.testUserId,
            globalExp: 5000,
            globalLevel: 10,
            prestigeLevel: 2,
            boosters: {
                expMultiplier: 1.5,
                dropChanceMultiplier: 1.2
            },
            starfall: {
                stars: 150,
                currentStreak: 5,
                longestStreak: 10
            }
        });

        // Global level notifications
        await optimizedInsertOne('global_level_notifications', {
            userId: this.testUserId,
            levelUpData: { level: 10 },
            viewed: false
        });

        // Item notifications queue
        await optimizedInsertOne('item_notifications_queue', {
            userId: this.testUserId,
            guildId: this.testGuildId,
            items: [{ itemName: 'Test Item' }],
            viewed: false
        });

        // EXP message tracking
        await optimizedInsertOne('exp_main_msgs', {
            userId: this.testUserId,
            guildId: this.testGuildId,
            messageId: 'test_message_123'
        });

        // User images cache
        await optimizedInsertOne('user_images_cache', {
            userId: this.testUserId,
            imageUrl: 'https://example.com/test.jpg'
        });

        // Item creation state
        await optimizedInsertOne('item_creation_state', {
            userId: this.testUserId,
            guildId: this.testGuildId,
            state: 'creating'
        });
    }

    /**
     * Count test data in all collections
     */
    async countTestData() {
        const counts = {};
        
        for (const collection of this.testCollections) {
            try {
                const count = await optimizedCountDocuments(collection, {
                    $or: [
                        { userId: this.testUserId },
                        { guildId: this.testGuildId }
                    ]
                });
                counts[collection] = count;
            } catch (error) {
                counts[collection] = 0; // Collection might not exist
            }
        }
        
        return counts;
    }

    /**
     * Display data counts in a readable format
     */
    displayCounts(counts) {
        for (const [collection, count] of Object.entries(counts)) {
            const status = count > 0 ? '📄' : '🗂️';
            console.log(`  ${status} ${collection}: ${count} records`);
        }
    }

    /**
     * Analyze test results
     */
    analyzeResults(beforeCounts, afterCounts, clearStats) {
        const issues = [];
        let totalBefore = 0;
        let totalAfter = 0;

        // Check each collection
        for (const collection of this.testCollections) {
            const before = beforeCounts[collection] || 0;
            const after = afterCounts[collection] || 0;
            
            totalBefore += before;
            totalAfter += after;

            if (before > 0 && after > 0) {
                issues.push(`${collection}: ${after} records remain (should be 0)`);
            }
        }

        // Verify clear statistics match expectations
        const expectedClears = totalBefore;
        const reportedClears = Object.values(clearStats).reduce((sum, count) => sum + count, 0);

        if (reportedClears < expectedClears) {
            issues.push(`Clear statistics mismatch: reported ${reportedClears}, expected at least ${expectedClears}`);
        }

        return {
            success: issues.length === 0 && totalAfter === 0,
            issues: issues,
            summary: {
                totalBefore,
                totalAfter,
                expectedClears,
                reportedClears
            }
        };
    }

    /**
     * Cleanup any remaining test data
     */
    async cleanupTestData() {
        for (const collection of this.testCollections) {
            try {
                const col = mongoClient.db().collection(collection);
                await col.deleteMany({
                    $or: [
                        { userId: this.testUserId },
                        { guildId: this.testGuildId }
                    ]
                });
            } catch (error) {
                // Collection might not exist, ignore error
            }
        }
    }
}

// Export for use in other test files
module.exports = { GlobalDataPersistenceTest };

// Run test if called directly
if (require.main === module) {
    const test = new GlobalDataPersistenceTest();
    test.runTest().then(result => {
        process.exit(result.success ? 0 : 1);
    }).catch(error => {
        console.error('Test execution failed:', error);
        process.exit(1);
    });
}
