# 17

A comprehensive Discord bot with EXP system, item drops, sticky roles, and advanced logging features.

## 🚀 Setup

### Environment Variables
Create a `.env` file in the root directory with the following variables:

```env
TOKEN=your_discord_bot_token
MONGO=your_mongodb_connection_string
OWNER=your_discord_user_id
GUILDIDTWO=417175807795134475
```

**Required Variables:**
- `TOKEN` - Your Discord bot token
- `MONGO` - MongoDB connection string for database access
- `OWNER` - Your Discord user ID for owner-only features
- `GUILDIDTWO` - Test server ID for bot testing

### Installation
```bash
npm install
node index.js
```

## 🧪 Testing

### Comprehensive Test Suite
Run the full automated test suite that discovers and tests all bot interactions:

```bash
# Run comprehensive real bot tests (recommended)
npm test

# Run interaction-focused tests
npm run test:interaction

# Run deep-dive feature tests
npm run test:deep
```

The test suite provides comprehensive testing with:
- Real Discord client connections
- Live MongoDB database operations
- Mock interaction objects for all features
- End-to-end workflow validation

### Test Results
Tests generate detailed console output showing:
- Feature-by-feature test results
- Database operation validation
- Interaction success/failure rates
- Error details and stack traces

For detailed testing documentation, see [`tests/README.md`](tests/README.md).

## 📁 Project Structure

- `commands/` - Slash command implementations
- `events/` - Discord event handlers
- `utils/` - Utility functions and helpers
  - `colors.js` - Centralized color system for consistent theming
  - `LRUCache.js` - High-performance caching system
  - `imageUploader.js` - Shared image upload utilities
- `tests/` - Comprehensive testing suite
- `mongo/` - Database connection and utilities
- `docs/` - Documentation and guides

## 🎯 Features

- **EXP System** - Text and voice experience tracking with leveling
- **Global Levels** - Cross-server progression with prestige system
- **Item Drops** - Random item drops with rarity system and inventory
- **Starfall Rewards** - Daily reward system with streak bonuses
- **Sticky Roles** - Persistent role assignment across rejoins
- **Advanced Logging** - Comprehensive activity logging
- **Owner Tools** - Bot management and configuration tools
- **Centralized Theming** - Consistent color system across all features

## 🎨 Design System

This bot uses a centralized color system for consistent theming and visual identity:

- **Operation Colors** - UI actions (add, edit, delete, neutral, entity)
- **Status Colors** - Feedback and logging (success, error, warning, info)
- **Rarity Colors** - Item system rarities (common to unknown)
- **Performance Optimized** - LRU caching and database optimizations

For detailed information about the color system and development guidelines, see:
- [Color System Guide](docs/COLOR_SYSTEM_GUIDE.md) - Complete color system documentation
- [Testing Guide](docs/comprehensive-testing-guide.md) - Testing methodologies
- [Fresh Session Instructions](docs/fresh-conversation-instructions.md) - Getting started

## 🏗️ Architecture Highlights

- **Components v2** - Modern Discord UI with containers and sections
- **Modular Design** - Feature-based file organization
- **Performance Optimized** - 87% improvement in database operations
- **Comprehensive Testing** - Real Discord API interaction testing
- **Memory Management** - LRU caching with automatic eviction
