const { <PERSON><PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

const { optimizedFind } = require('../../utils/database-optimizer.js');
const { ObjectId } = require('mongodb');
const { getRecentImagesFromChannel, uploadImageAsEmote, buildImageSelectMenu, buildNoImagesSelectMenu, handleImageSelection } = require('../../utils/imageUploader.js');
const fetch = require('node-fetch');
const { OPERATION_COLORS, LOG_COLORS, LEGACY_COLORS } = require('../../utils/colors.js');

// Temporary state storage for global level creation
const tempGlobalLevelState = new Map();

/**
 * Owner Global Levels Management Module
 * Extracted from owner.js to reduce file size and improve maintainability
 * Handles all global level creation, editing, and management functionality
 */

/**
 * Build global levels container - EXACT copy of original from owner.js
 * @param {string} subcomponent - Subcomponent type (default: 'main')
 * @param {boolean} forceFresh - Force fresh data from database (default: false)
 * @returns {ContainerBuilder} Global levels container
 */
async function buildGlobalLevelsContainer(subcomponent = 'main', forceFresh = false) {
    try {
        let levels;
        if (forceFresh) {
            // Force fresh data from database, bypassing cache
            levels = await optimizedFind('global_levels',
                { isActive: true },
                { sort: { level: 1 } }
            );
            console.log('[buildGlobalLevelsContainer] Using FRESH data from database');
        } else {
            const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
            levels = await getCachedGlobalLevels();
            console.log('[buildGlobalLevelsContainer] Using cached data');
        }

        // Header with back button
        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);

        const backSection = new SectionBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# levels')
            )
            .setButtonAccessory(backButton);

        // Description and levels display
        let description;
        if (levels.length === 0) {
            description = new TextDisplayBuilder().setContent(
                '> manage global levels system\n\n' +
                '__no global levels created yet.__'
            );
        } else {
            // Build levels display
            let levelsText = '> manage global levels system\n\n';
            for (const level of levels) {
                const prestigeIcon = level.prestigeIcon ? `${level.prestigeIcon} ` : '';
                const levelIcon = level.levelIcon ? `${level.levelIcon} ` : '';



                // Count actual rewards, not just properties
                let rewardCount = 0;
                const rewards = level.rewards || {};
                if (rewards.items && rewards.items.length > 0) rewardCount += rewards.items.length;
                if (rewards.xpBooster) rewardCount += 1;
                if (rewards.dropBooster) rewardCount += 1;
                if (rewards.stars) rewardCount += 1;

                levelsText += `level ${level.level}: ${prestigeIcon}${levelIcon}**${level.name}** ${level.expRequired.toLocaleString()} exp, ${rewardCount} reward${rewardCount !== 1 ? 's' : ''}\n`;
            }
            description = new TextDisplayBuilder().setContent(levelsText);
        }

        // Main action menu with levels integrated (like exp levels and items)
        const actionOptions = [
            {
                label: 'create level',
                description: 'create a new global level',
                value: 'create',
                emoji: '➕'
            }
        ];

        // Add existing levels to the main menu (like exp levels and items)
        if (levels.length > 0) {
            levels.forEach(level => {
                actionOptions.push({
                    label: level.name,
                    value: level._id.toString(),
                    description: `Level ${level.level} - ${level.expRequired.toLocaleString()} exp`,
                    emoji: level.levelIcon || '✏️' // Use level's configured icon or fallback to pencil
                });
            });
        }

        // Add utility options at the end
        actionOptions.push({
            label: 'create indexes',
            description: 'create database indexes for performance',
            value: 'indexes',
            emoji: '🔧'
        });

        const actionMenu = new StringSelectMenuBuilder()
            .setCustomId('global-levels-action')
            .setPlaceholder('choose an action')
            .addOptions(actionOptions);

        const components = [new ActionRowBuilder().addComponents(actionMenu)];

        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(description)
            .addActionRowComponents(...components)
            .setAccentColor(LEGACY_COLORS.DISCORD_BLURPLE);

        return container;

    } catch (error) {
        console.error('[buildGlobalLevelsContainer] Error:', error);

        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);

        const backSection = new SectionBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# levels')
            )
            .setButtonAccessory(backButton);

        const errorText = new TextDisplayBuilder().setContent(
            '❌ Error loading global levels system.\n\n' +
            `**error:** ${error.message}`
        );

        return new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(errorText)
            .setAccentColor(LOG_COLORS.ERROR);
    }
}

/**
 * Start global level creation process
 * @param {Object} interaction - Discord interaction
 */
async function startGlobalLevelCreation(interaction) {
    if (interaction.user.id !== process.env.OWNER) {
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** who r u? no.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }

    try {
        // Get next level number
        const existingLevels = await optimizedFind('global_levels',
            {},
            { sort: { level: -1 }, limit: 1 }
        );
        const nextLevel = existingLevels.length > 0 ? existingLevels[0].level + 1 : 0;

        // Clear all feature states to prevent modal data persistence
        await clearAllFeatureStates(interaction.user.id, interaction.guild?.id);

        // Initialize temp state
        const stateKey = `${interaction.user.id}_create`;
        tempGlobalLevelState.set(stateKey, {
            level: nextLevel,
            name: null,
            expRequired: null,
            levelIcon: null,
            prestigeIcon: null,
            rewards: {
                items: [], // Array of item IDs
                xpBooster: null, // XP multiplier (e.g., 1.1, 1.5)
                dropBooster: null, // Drop chance multiplier (e.g., 1.2, 2.0)
                stars: null // Star reward amount (e.g., 10, 25, 50)
            },
            currentConfig: null
        });

        return await showGlobalLevelCreationInterface(interaction);

    } catch (error) {
        console.error('[owner-global-levels] Error starting creation:', error);
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Error starting level creation. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }
}

/**
 * Show global level creation interface
 * @param {Object} interaction - Discord interaction
 */
async function showGlobalLevelCreationInterface(interaction) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Creation session expired. Please start again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }

    // Build the interface container
    const container = await buildGlobalLevelConfigContainer(state, 'create', interaction);

    // Return the container instead of calling interaction.update
    return container;
}

/**
 * Build global level configuration container
 * @param {Object} state - Current state
 * @param {string} mode - 'create' or 'edit'
 * @param {Object} interaction - Discord interaction object
 * @returns {ContainerBuilder} Configuration container
 */
async function buildGlobalLevelConfigContainer(state, mode = 'create', interaction) {
    const heading = new TextDisplayBuilder().setContent(`# ${mode} global level ${state.level}`);
    
    // Level info with rewards (exactly like edit interface)
    let levelInfo = `**Level:** ${state.level}\n` +
        `**Name:** ${state.name || '*not set*'}\n` +
        `**EXP Required:** ${state.expRequired !== null ? state.expRequired.toLocaleString() : '*not set*'}\n` +
        `**Level Icon:** ${state.levelIcon || '*not set*'}\n` +
        `**Prestige Icon:** ${state.prestigeIcon || '*not set*'}\n`;

    // Show rewards info (exactly like edit interface)
    const rewards = state.rewards || {};
    const itemCount = rewards.items ? rewards.items.length : 0;
    const xpBooster = rewards.xpBooster ? `${rewards.xpBooster}x` : 'none';
    const dropBooster = rewards.dropBooster ? `${rewards.dropBooster}x` : 'none';
    const stars = rewards.stars ? `${rewards.stars} stars` : 'none';

    // Show item rewards with name and icon (exactly like edit interface)
    if (itemCount > 0 && rewards.items[0]) {
        const item = rewards.items[0];
        const itemName = item.itemName || item.name || 'Unknown Item';
        const itemEmote = item.itemEmote || item.emote || '📦';
        levelInfo += `**Item Rewards:** 1 item (${itemEmote} ${itemName})\n`;
    } else {
        levelInfo += `**Item Rewards:** none\n`;
    }
    levelInfo += `**XP Booster:** ${xpBooster}\n`;
    levelInfo += `**Drop Booster:** ${dropBooster}\n`;
    levelInfo += `**Stars:** ${stars}`;

    const infoDisplay = new TextDisplayBuilder().setContent(levelInfo);

    // Configuration select menu
    const configOptions = [
        {
            label: 'edit name',
            value: 'name',
            description: state.name ? `${state.name}` : 'Set level name',
            emoji: '📝',
            default: state.currentConfig === 'name'
        },
        {
            label: 'edit exp required',
            value: 'exp',
            description: state.expRequired !== null ? `${state.expRequired.toLocaleString()} EXP` : 'Set EXP requirement',
            emoji: '⭐',
            default: state.currentConfig === 'exp'
        },
        {
            label: 'edit level icon',
            value: 'level_icon',
            description: state.levelIcon ? 'Level icon set' : 'Set level icon',
            emoji: '🎨',
            default: state.currentConfig === 'level_icon'
        },
        {
            label: 'edit prestige icon',
            value: 'prestige_icon',
            description: state.prestigeIcon ? 'Prestige icon set' : 'Set prestige icon',
            emoji: '👑',
            default: state.currentConfig === 'prestige_icon'
        }
    ];

    // Reward options (items only for bot owner since LEVEL_UP is owner-only)
    const isOwner = true; // This module is owner-only
    if (isOwner) {
        configOptions.push({
            label: 'edit items',
            value: 'items',
            description: state.rewards?.items?.length > 0 && state.rewards.items[0] ?
                `${state.rewards.items[0].itemName || state.rewards.items[0].name || 'Unknown Item'}` :
                'Set item reward',
            emoji: '🎁',
            default: state.currentConfig === 'items'
        });
    }

    configOptions.push({
        label: 'edit xp boosters',
        value: 'xp_boosters',
        description: state.rewards?.xpBooster ? `${state.rewards.xpBooster}x XP boost` : 'Set XP booster',
        emoji: '⚡',
        default: state.currentConfig === 'xp_boosters'
    });

    configOptions.push({
        label: 'edit drop boosters',
        value: 'drop_boosters',
        description: state.rewards?.dropBooster ? `${state.rewards.dropBooster}x drop boost` : 'Set drop booster',
        emoji: '💎',
        default: state.currentConfig === 'drop_boosters'
    });

    configOptions.push({
        label: 'edit stars',
        value: 'stars',
        description: state.rewards?.stars ? `${state.rewards.stars} stars` : 'Set star reward',
        emoji: '⭐',
        default: state.currentConfig === 'stars'
    });

    const configSelect = new StringSelectMenuBuilder()
        .setCustomId('global-level-config-select')
        .setPlaceholder('configure level properties')
        .addOptions(configOptions);

    const configRow = new ActionRowBuilder().addComponents(configSelect);
    const components = [configRow];

    // Show cascading select menu based on current config (like items.js)
    if (state.currentConfig === 'exp') {
        const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
        const levels = await getCachedGlobalLevels();

        // Allow 0 EXP for level 0, otherwise use minimum based on existing levels
        let minExp;
        if (state.level === 0) {
            minExp = 0; // Allow 0 EXP option for level 0
        } else {
            minExp = levels.length > 0 ? Math.max(...levels.map(l => l.expRequired)) + 1000 : 1000;
        }

        const expOptions = generateExpOptions(minExp);

        const expSelect = new StringSelectMenuBuilder()
            .setCustomId(mode === 'create' ? 'global-level-exp-select' : 'global-level-edit-exp-select')
            .setPlaceholder('select XP requirement')
            .addOptions(expOptions.slice(0, 25)); // Discord limit

        components.push(new ActionRowBuilder().addComponents(expSelect));

    } else if (state.currentConfig === 'xp_boosters') {
        const currentXpBooster = state.rewards?.xpBooster;
        const boosterOptions = [
            { label: '1.1x XP Boost', value: '1.1', description: '10% XP increase', default: currentXpBooster === 1.1 },
            { label: '1.2x XP Boost', value: '1.2', description: '20% XP increase', default: currentXpBooster === 1.2 },
            { label: '1.3x XP Boost', value: '1.3', description: '30% XP increase', default: currentXpBooster === 1.3 },
            { label: '1.5x XP Boost', value: '1.5', description: '50% XP increase', default: currentXpBooster === 1.5 },
            { label: '2.0x XP Boost', value: '2.0', description: '100% XP increase', default: currentXpBooster === 2.0 },
            { label: 'No XP Boost', value: 'remove', description: 'No XP booster for this level', default: !currentXpBooster }
        ];

        const xpBoosterSelect = new StringSelectMenuBuilder()
            .setCustomId(mode === 'create' ? 'global-level-xp-booster-select' : 'global-level-edit-xp-booster-select')
            .setPlaceholder('select XP booster')
            .addOptions(boosterOptions);

        components.push(new ActionRowBuilder().addComponents(xpBoosterSelect));

    } else if (state.currentConfig === 'drop_boosters') {
        const currentDropBooster = state.rewards?.dropBooster;
        const dropBoosterOptions = [
            { label: '1.1x Drop Boost', value: '1.1', description: '10% drop rate increase', default: currentDropBooster === 1.1 },
            { label: '1.2x Drop Boost', value: '1.2', description: '20% drop rate increase', default: currentDropBooster === 1.2 },
            { label: '1.5x Drop Boost', value: '1.5', description: '50% drop rate increase', default: currentDropBooster === 1.5 },
            { label: '2.0x Drop Boost', value: '2.0', description: '100% drop rate increase', default: currentDropBooster === 2.0 },
            { label: '3.0x Drop Boost', value: '3.0', description: '200% drop rate increase', default: currentDropBooster === 3.0 },
            { label: 'No Drop Boost', value: 'remove', description: 'No drop booster for this level', default: !currentDropBooster }
        ];

        const dropBoosterSelect = new StringSelectMenuBuilder()
            .setCustomId(mode === 'create' ? 'global-level-drop-booster-select' : 'global-level-edit-drop-booster-select')
            .setPlaceholder('select drop booster')
            .addOptions(dropBoosterOptions);

        components.push(new ActionRowBuilder().addComponents(dropBoosterSelect));

    } else if (state.currentConfig === 'stars') {
        const currentStars = state.rewards?.stars;
        const starOptions = [
            { label: '1000 Stars', value: '1000', description: 'Award 1000 stars', default: currentStars === 1000 },
            { label: '2000 Stars', value: '2000', description: 'Award 2000 stars', default: currentStars === 2000 },
            { label: '3000 Stars', value: '3000', description: 'Award 3000 stars', default: currentStars === 3000 },
            { label: '4000 Stars', value: '4000', description: 'Award 4000 stars', default: currentStars === 4000 },
            { label: '5000 Stars', value: '5000', description: 'Award 5000 stars', default: currentStars === 5000 },
            { label: '10000 Stars', value: '10000', description: 'Award 10000 stars', default: currentStars === 10000 },
            { label: 'No Stars', value: 'remove', description: 'No star rewards for this level', default: !currentStars }
        ];

        const starSelect = new StringSelectMenuBuilder()
            .setCustomId(mode === 'create' ? 'global-level-stars-select' : 'global-level-edit-stars-select')
            .setPlaceholder('select star reward')
            .addOptions(starOptions);

        components.push(new ActionRowBuilder().addComponents(starSelect));

    } else if (state.currentConfig === 'items') {
        // Show item selection menu for level rewards (creation)
        try {
            const { optimizedFind } = require('../../utils/database-optimizer.js');

            // Get all available items (both global and guild items for owner)
            const allItems = await optimizedFind('custom_items', {
                disabled: { $ne: true } // Only show enabled items
            });

            if (allItems.length > 0) {
                // Check if any item is currently selected
                const hasSelectedItem = state.rewards?.items?.length > 0;

                const itemOptions = allItems.slice(0, 24).map(item => {
                    const itemId = item._id.toString();
                    const isSelected = hasSelectedItem && state.rewards.items[0]?.itemId === itemId;

                    // Format rarity properly, hiding NONE rarity
                    const { formatRarity } = require('./items.js');
                    const rarityText = formatRarity(item.rarity);
                    const description = rarityText ?
                        `${item.type || 'Unknown'} | ${rarityText}` :
                        `${item.type || 'Unknown'}`;

                    return {
                        label: item.name || 'Unnamed Item',
                        value: itemId,
                        description: description,
                        emoji: item.emote || '📦',
                        default: isSelected
                    };
                });

                // Add "No Items" option (following stars/boosters pattern)
                itemOptions.push({
                    label: 'No Items',
                    value: 'remove',
                    description: 'No item rewards for this level',
                    emoji: '❌',
                    default: !hasSelectedItem // Default selected when no items are configured
                });

                const itemSelect = new StringSelectMenuBuilder()
                    .setCustomId('global-level-items-select')
                    .setPlaceholder('select item to award for this level')
                    .addOptions(itemOptions);

                components.push(new ActionRowBuilder().addComponents(itemSelect));
            } else {
                // No items available
                const noItemsSelect = new StringSelectMenuBuilder()
                    .setCustomId('global-level-items-select')
                    .setPlaceholder('no items available')
                    .setDisabled(true)
                    .addOptions({
                        label: 'No items found',
                        value: 'none',
                        description: 'Create items first in the items interface'
                    });

                components.push(new ActionRowBuilder().addComponents(noItemsSelect));
            }
        } catch (error) {
            console.error('[buildGlobalLevelConfigContainer] Error loading items:', error);

            // Error fallback
            const errorSelect = new StringSelectMenuBuilder()
                .setCustomId('global-level-items-select')
                .setPlaceholder('error loading items')
                .setDisabled(true)
                .addOptions({
                    label: 'Error loading items',
                    value: 'error',
                    description: 'Please try again'
                });

            components.push(new ActionRowBuilder().addComponents(errorSelect));
        }

    } else if (state.currentConfig === 'level_icon') {
        // Use shared image uploader utility with error handling
        try {
            // PERFORMANCE OPTIMIZATION: Force refresh cache to show newly uploaded images
            const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
            const recentImages = await forceRefreshImageCache(interaction);
            const imageSelectRow = buildImageSelectMenu(
                recentImages,
                mode === 'create' ? 'global-level-icon-select' : 'global-level-edit-icon-select',
                'select image for level icon'
            );

            if (imageSelectRow) {
                components.push(imageSelectRow);
            } else {
                components.push(buildNoImagesSelectMenu(
                    mode === 'create' ? 'global-level-icon-select' : 'global-level-edit-icon-select',
                    'select image for level icon'
                ));
            }
        } catch (error) {
            console.error('[buildGlobalLevelConfigContainer] Error loading images for level_icon:', error);
            components.push(buildNoImagesSelectMenu(
                mode === 'create' ? 'global-level-icon-select' : 'global-level-edit-icon-select',
                'select image for level icon'
            ));
        }

    } else if (state.currentConfig === 'prestige_icon') {
        // Use shared image uploader utility with error handling
        try {
            // PERFORMANCE OPTIMIZATION: Force refresh cache to show newly uploaded images
            const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
            const recentImages = await forceRefreshImageCache(interaction);
            const imageSelectRow = buildImageSelectMenu(
                recentImages,
                mode === 'create' ? 'global-prestige-icon-select' : 'global-level-edit-prestige-icon-select',
                'select image for prestige icon'
            );

            if (imageSelectRow) {
                components.push(imageSelectRow);
            } else {
                components.push(buildNoImagesSelectMenu(
                    mode === 'create' ? 'global-prestige-icon-select' : 'global-level-edit-prestige-icon-select',
                    'select image for prestige icon'
                ));
            }
        } catch (error) {
            console.error('[buildGlobalLevelConfigContainer] Error loading images for prestige_icon:', error);
            components.push(buildNoImagesSelectMenu(
                mode === 'create' ? 'global-prestige-icon-select' : 'global-level-edit-prestige-icon-select',
                'select image for prestige icon'
            ));
        }
    }

    // Back button section (consistent with other features)
    const backButton = new ButtonBuilder()
        .setCustomId('global-levels-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);

    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);

    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(infoDisplay)
        .addActionRowComponents(...components)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    // Add create button if minimum requirements are met
    const isReadyToCreate = state.name && (state.expRequired !== null && state.expRequired !== undefined);
    if (isReadyToCreate && mode === 'create') {
        const createButton = new ButtonBuilder()
            .setCustomId('global-level-create-final')
            .setLabel('create level')
            .setStyle(ButtonStyle.Success);

        const createRow = new ActionRowBuilder().addComponents(createButton);
        container.addActionRowComponents(createRow);
    }

    return container;
}

/**
 * Handle global levels action selection
 * @param {Object} interaction - Discord interaction
 * @param {string} action - Selected action
 */
async function handleGlobalLevelsAction(interaction, action) {
    try {
        switch (action) {
            case 'create':
                return await startGlobalLevelCreation(interaction);
            case 'indexes':
                return await createGlobalLevelsIndexes(interaction);
            default:
                // Check if it's a level ID (for editing)
                if (action.length === 24) { // MongoDB ObjectId length
                    return await startGlobalLevelEdit(interaction, action);
                } else {
                    const container = await buildGlobalLevelsContainer();
                    return container;
                }
        }
    } catch (error) {
        console.error('[handleGlobalLevelsAction] Error:', error);

        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);

        const backSection = new SectionBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# levels')
            )
            .setButtonAccessory(backButton);

        const errorText = new TextDisplayBuilder().setContent(
            '❌ Error handling global levels action.\n\n' +
            `**error:** ${error.message}`
        );

        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(errorText)
            .setAccentColor(LOG_COLORS.ERROR);

        // Return error container instead of calling interaction.update
        return container;
    }
}

/**
 * Start global level edit process
 * @param {Object} interaction - Discord interaction
 * @param {string} levelId - Level ID to edit
 */
async function startGlobalLevelEdit(interaction, levelId) {
    const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
    const levels = await getCachedGlobalLevels();
    const level = levels.find(l => l._id.toString() === levelId);

    if (!level) {
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Level not found.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }

    // Initialize temp state with existing level data
    const stateKey = `${interaction.user.id}_edit`;
    tempGlobalLevelState.set(stateKey, {
        levelId: levelId,
        level: level.level,
        name: level.name,
        expRequired: level.expRequired,
        levelIcon: level.levelIcon,
        prestigeIcon: level.prestigeIcon,
        rewards: level.rewards || { items: [], xpBooster: null, dropBooster: null, stars: null },
        currentConfig: null,
        originalData: JSON.parse(JSON.stringify(level)) // Deep copy for comparison
    });

    return await showGlobalLevelEditInterface(interaction);
}

/**
 * Create global levels database indexes
 * @param {Object} interaction - Discord interaction
 */
async function createGlobalLevelsIndexes(interaction) {
    try {
        const { createGlobalLevelsIndexes, checkGlobalLevelsIndexes } = require('../../utils/createGlobalLevelsIndexes.js');

        // Check if indexes already exist
        const indexesExist = await checkGlobalLevelsIndexes();

        // Header with back button
        const backButton = new ButtonBuilder()
            .setCustomId('global-levels-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);

        const backSection = new SectionBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# levels')
            )
            .setButtonAccessory(backButton);

        if (indexesExist) {
            const successText = new TextDisplayBuilder().setContent(
                '> all required global levels indexes already exist\n\n' +
                'the database is optimized for performance.'
            );

            const container = new ContainerBuilder()
                .addSectionComponents(backSection)
                .addTextDisplayComponents(successText)
                .setAccentColor(LOG_COLORS.SUCCESS);

            // Return container instead of calling interaction.update
            return container;
        }

        // Create indexes
        await createGlobalLevelsIndexes();

        const successText = new TextDisplayBuilder().setContent(
            '> database indexes created successfully\n\n' +
            'all required indexes for the global levels system have been created.\n' +
            'the database is now optimized for performance.'
        );

        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(successText)
            .setAccentColor(LOG_COLORS.SUCCESS);

        // Return container instead of calling interaction.update
        return container;

    } catch (error) {
        console.error('[createGlobalLevelsIndexes] Error:', error);

        const backButton = new ButtonBuilder()
            .setCustomId('global-levels-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);

        const backSection = new SectionBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# levels')
            )
            .setButtonAccessory(backButton);

        const errorText = new TextDisplayBuilder().setContent(
            '❌ Error creating database indexes.\n\n' +
            `**error:** ${error.message}`
        );

        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(errorText)
            .setAccentColor(LOG_COLORS.ERROR);

        // Return container instead of calling interaction.update
        return container;
    }
}

/**
 * Show global level edit interface
 * @param {Object} interaction - Discord interaction
 */
async function showGlobalLevelEditInterface(interaction) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        // Return container instead of calling interaction.update
        return container;
    }

    // Header with back button
    const backButton = new ButtonBuilder()
        .setCustomId('global-levels-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);

    const backSection = new SectionBuilder()
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(`# edit global level ${state.level}`)
        )
        .setButtonAccessory(backButton);

    // Build description showing current state
    let description = '> edit global level\n\n';
    description += `**level:** ${state.level}\n`;
    description += `**name:** ${state.name || 'not set'}\n`;
    description += `**exp required:** ${state.expRequired !== null ? state.expRequired.toLocaleString() : 'not set'}\n`;
    description += `**level icon:** ${state.levelIcon || 'not set'}\n`;
    description += `**prestige icon:** ${state.prestigeIcon || 'not set'}\n`;

    // Show rewards info
    const rewards = state.rewards || {};
    const itemCount = rewards.items ? rewards.items.length : 0;
    const xpBooster = rewards.xpBooster ? `${rewards.xpBooster}x` : 'none';
    const dropBooster = rewards.dropBooster ? `${rewards.dropBooster}x` : 'none';
    const stars = rewards.stars ? `${rewards.stars} stars` : 'none';

    // Show item rewards with name and icon (like DM message)
    if (itemCount > 0 && rewards.items[0]) {
        const item = rewards.items[0];
        const itemName = item.itemName || item.name || 'Unknown Item';
        const itemEmote = item.itemEmote || item.emote || '📦';
        description += `**item rewards:** 1 item (${itemEmote} ${itemName})\n`;
    } else {
        description += `**item rewards:** none\n`;
    }
    description += `**xp booster:** ${xpBooster}\n`;
    description += `**drop booster:** ${dropBooster}\n`;
    description += `**stars:** ${stars}`;

    const descriptionDisplay = new TextDisplayBuilder().setContent(description);

    // Configuration options
    const configOptions = [
        {
            label: 'edit name',
            value: 'name',
            description: state.name ? `${state.name}` : 'Set level name',
            emoji: '📝',
            default: state.currentConfig === 'name'
        },
        {
            label: 'edit exp required',
            value: 'exp',
            description: state.expRequired !== null ? `${state.expRequired.toLocaleString()} EXP` : 'Set EXP requirement',
            emoji: '⭐',
            default: state.currentConfig === 'exp'
        },
        {
            label: 'edit level icon',
            value: 'level_icon',
            description: state.levelIcon ? 'Level icon set' : 'Set level icon',
            emoji: '🎨',
            default: state.currentConfig === 'level_icon'
        },
        {
            label: 'edit prestige icon',
            value: 'prestige_icon',
            description: state.prestigeIcon ? 'Prestige icon set' : 'Set prestige icon',
            emoji: '👑',
            default: state.currentConfig === 'prestige_icon'
        },
        {
            label: 'edit items',
            value: 'items',
            description: itemCount > 0 && rewards.items[0] ?
                `${rewards.items[0].itemName || rewards.items[0].name || 'Unknown Item'}` :
                'Set item reward',
            emoji: '🎁',
            default: state.currentConfig === 'items'
        },
        {
            label: 'edit xp boosters',
            value: 'xp_boosters',
            description: rewards.xpBooster ? `${rewards.xpBooster}x XP boost` : 'Set XP booster',
            emoji: '⚡',
            default: state.currentConfig === 'xp_boosters'
        },
        {
            label: 'edit drop boosters',
            value: 'drop_boosters',
            description: rewards.dropBooster ? `${rewards.dropBooster}x drop boost` : 'Set drop booster',
            emoji: '💎',
            default: state.currentConfig === 'drop_boosters'
        },
        {
            label: 'edit stars',
            value: 'stars',
            description: rewards.stars ? `${rewards.stars} stars` : 'Set star reward',
            emoji: '⭐',
            default: state.currentConfig === 'stars'
        }
    ];

    const configSelect = new StringSelectMenuBuilder()
        .setCustomId('global-level-edit-config-select')
        .setPlaceholder('edit level')
        .addOptions(configOptions);

    const components = [new ActionRowBuilder().addComponents(configSelect)];

    // Show cascading select menu based on current config (like items.js)
    if (state.currentConfig === 'exp') {
        const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
        const levels = await getCachedGlobalLevels();
        const minExp = 1000; // Allow any exp for editing
        const expOptions = generateExpOptions(minExp);

        const expSelect = new StringSelectMenuBuilder()
            .setCustomId('global-level-edit-exp-select')
            .setPlaceholder('select XP requirement')
            .addOptions(expOptions.slice(0, 25)); // Discord limit

        components.push(new ActionRowBuilder().addComponents(expSelect));

    } else if (state.currentConfig === 'xp_boosters') {
        const currentXpBooster = state.rewards?.xpBooster;
        const boosterOptions = [
            { label: '1.1x XP Boost', value: '1.1', description: '10% XP increase', default: currentXpBooster === 1.1 },
            { label: '1.2x XP Boost', value: '1.2', description: '20% XP increase', default: currentXpBooster === 1.2 },
            { label: '1.3x XP Boost', value: '1.3', description: '30% XP increase', default: currentXpBooster === 1.3 },
            { label: '1.5x XP Boost', value: '1.5', description: '50% XP increase', default: currentXpBooster === 1.5 },
            { label: '2.0x XP Boost', value: '2.0', description: '100% XP increase', default: currentXpBooster === 2.0 },
            { label: 'No XP Boost', value: 'remove', description: 'No XP booster for this level', default: !currentXpBooster }
        ];

        const xpBoosterSelect = new StringSelectMenuBuilder()
            .setCustomId('global-level-edit-xp-booster-select')
            .setPlaceholder('select XP booster')
            .addOptions(boosterOptions);

        components.push(new ActionRowBuilder().addComponents(xpBoosterSelect));

    } else if (state.currentConfig === 'drop_boosters') {
        const currentDropBooster = state.rewards?.dropBooster;
        const dropBoosterOptions = [
            { label: '1.1x Drop Boost', value: '1.1', description: '10% drop rate increase', default: currentDropBooster === 1.1 },
            { label: '1.2x Drop Boost', value: '1.2', description: '20% drop rate increase', default: currentDropBooster === 1.2 },
            { label: '1.5x Drop Boost', value: '1.5', description: '50% drop rate increase', default: currentDropBooster === 1.5 },
            { label: '2.0x Drop Boost', value: '2.0', description: '100% drop rate increase', default: currentDropBooster === 2.0 },
            { label: '3.0x Drop Boost', value: '3.0', description: '200% drop rate increase', default: currentDropBooster === 3.0 },
            { label: 'No Drop Boost', value: 'remove', description: 'No drop booster for this level', default: !currentDropBooster }
        ];

        const dropBoosterSelect = new StringSelectMenuBuilder()
            .setCustomId('global-level-edit-drop-booster-select')
            .setPlaceholder('select drop booster')
            .addOptions(dropBoosterOptions);

        components.push(new ActionRowBuilder().addComponents(dropBoosterSelect));

    } else if (state.currentConfig === 'stars') {
        const currentStars = state.rewards?.stars;
        const starOptions = [
            { label: '1000 Stars', value: '1000', description: 'Award 1000 stars', default: currentStars === 1000 },
            { label: '2000 Stars', value: '2000', description: 'Award 2000 stars', default: currentStars === 2000 },
            { label: '3000 Stars', value: '3000', description: 'Award 3000 stars', default: currentStars === 3000 },
            { label: '4000 Stars', value: '4000', description: 'Award 4000 stars', default: currentStars === 4000 },
            { label: '5000 Stars', value: '5000', description: 'Award 5000 stars', default: currentStars === 5000 },
            { label: '10000 Stars', value: '10000', description: 'Award 10000 stars', default: currentStars === 10000 },
            { label: 'No Stars', value: 'remove', description: 'No star rewards for this level', default: !currentStars }
        ];

        const starSelect = new StringSelectMenuBuilder()
            .setCustomId('global-level-edit-stars-select')
            .setPlaceholder('select star reward')
            .addOptions(starOptions);

        components.push(new ActionRowBuilder().addComponents(starSelect));

    } else if (state.currentConfig === 'items') {
        // Show item selection menu for level rewards
        try {
            const { optimizedFind } = require('../../utils/database-optimizer.js');

            // Get all available items (both global and guild items for owner)
            const allItems = await optimizedFind('custom_items', {
                disabled: { $ne: true } // Only show enabled items
            });

            if (allItems.length > 0) {
                // Get currently selected item IDs
                const selectedItemIds = (state.rewards.items || []).map(item => item.itemId);
                const hasSelectedItem = selectedItemIds.length > 0;

                const itemOptions = allItems.slice(0, 24).map(item => {
                    const itemId = item._id.toString();

                    return {
                        label: item.name || 'Unnamed Item',
                        value: itemId,
                        description: `${item.type || 'Unknown'} | ${item.rarity || 'Common'}`,
                        emoji: item.emote || '📦',
                        default: hasSelectedItem && selectedItemIds[0] === itemId // Pre-select the selected item
                    };
                });

                // Add "No Items" option (following stars/boosters pattern)
                itemOptions.push({
                    label: 'No Items',
                    value: 'remove',
                    description: 'No item rewards for this level',
                    emoji: '❌',
                    default: !hasSelectedItem // Default selected when no items are configured
                });

                const itemSelect = new StringSelectMenuBuilder()
                    .setCustomId('global-level-edit-items-select')
                    .setPlaceholder(selectedItemIds.length > 0 ? 'item selected' : 'select item to award for this level')
                    .addOptions(itemOptions);

                components.push(new ActionRowBuilder().addComponents(itemSelect));
            } else {
                // No items available
                const noItemsSelect = new StringSelectMenuBuilder()
                    .setCustomId('global-level-edit-items-select')
                    .setPlaceholder('no items available')
                    .setDisabled(true)
                    .addOptions({
                        label: 'No items found',
                        value: 'none',
                        description: 'Create items first in the items interface'
                    });

                components.push(new ActionRowBuilder().addComponents(noItemsSelect));
            }
        } catch (error) {
            console.error('[showGlobalLevelEditInterface] Error loading items:', error);

            // Error fallback
            const errorSelect = new StringSelectMenuBuilder()
                .setCustomId('global-level-edit-items-select')
                .setPlaceholder('error loading items')
                .setDisabled(true)
                .addOptions({
                    label: 'Error loading items',
                    value: 'error',
                    description: 'Please try again'
                });

            components.push(new ActionRowBuilder().addComponents(errorSelect));
        }

    } else if (state.currentConfig === 'level_icon') {
        // Use shared image uploader utility with error handling
        try {
            // PERFORMANCE OPTIMIZATION: Force refresh cache to show newly uploaded images
            const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
            const recentImages = await forceRefreshImageCache(interaction);
            const imageSelectRow = buildImageSelectMenu(
                recentImages,
                'global-level-edit-icon-select',
                'select image for level icon'
            );

            if (imageSelectRow) {
                components.push(imageSelectRow);
            } else {
                components.push(buildNoImagesSelectMenu('global-level-edit-icon-select', 'select image for level icon'));
            }
        } catch (error) {
            console.error('[showGlobalLevelEditInterface] Error loading images for level_icon:', error);
            components.push(buildNoImagesSelectMenu('global-level-edit-icon-select', 'select image for level icon'));
        }

    } else if (state.currentConfig === 'prestige_icon') {
        // Use shared image uploader utility with error handling
        try {
            // PERFORMANCE OPTIMIZATION: Force refresh cache to show newly uploaded images
            const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
            const recentImages = await forceRefreshImageCache(interaction);
            const imageSelectRow = buildImageSelectMenu(
                recentImages,
                'global-level-edit-prestige-icon-select',
                'select image for prestige icon'
            );

            if (imageSelectRow) {
                components.push(imageSelectRow);
            } else {
                components.push(buildNoImagesSelectMenu('global-level-edit-prestige-icon-select', 'select image for prestige icon'));
            }
        } catch (error) {
            console.error('[showGlobalLevelEditInterface] Error loading images for prestige_icon:', error);
            components.push(buildNoImagesSelectMenu('global-level-edit-prestige-icon-select', 'select image for prestige icon'));
        }
    }

    // Add update button if changes have been made (like items.js)
    const hasChanges = hasGlobalLevelBeenModified(state);
    console.log(`[showGlobalLevelEditInterface] hasChanges: ${hasChanges}, state:`, JSON.stringify({
        name: state.name,
        expRequired: state.expRequired,
        itemsCount: state.rewards?.items?.length || 0,
        originalItemsCount: state.originalData?.rewards?.items?.length || 0,
        currentItemIds: (state.rewards?.items || []).map(item => item.itemId),
        originalItemIds: (state.originalData?.rewards?.items || []).map(item => item.itemId || item._id?.toString() || item)
    }, null, 2));
    if (hasChanges) {
        const updateButton = new ButtonBuilder()
            .setCustomId('global-level-update-final')
            .setLabel('update level')
            .setStyle(ButtonStyle.Success);

        components.push(new ActionRowBuilder().addComponents(updateButton));
    }

    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(descriptionDisplay)
        .addActionRowComponents(...components)
        .setAccentColor(LEGACY_COLORS.DISCORD_BLURPLE);

    // CONVERTED: Return components instead of using legacy error handler
    // The Universal Interaction Manager will handle the response automatically
    return [container];
}

/**
 * Check if global level has been modified
 * @param {Object} state - Current state
 * @returns {boolean} True if modified
 */
function hasGlobalLevelBeenModified(state) {
    if (!state.originalData) return false;

    const original = state.originalData;

    // Check basic properties
    if (state.name !== original.name) return true;
    if (state.expRequired !== original.expRequired) return true;
    if (state.levelIcon !== original.levelIcon) return true;
    if (state.prestigeIcon !== original.prestigeIcon) return true;

    // Check rewards
    const originalRewards = original.rewards || {};
    const currentRewards = state.rewards || {};

    // Check items (compare by itemId since structures may differ)
    const originalItems = originalRewards.items || [];
    const currentItems = currentRewards.items || [];
    if (originalItems.length !== currentItems.length) return true;

    // Compare item IDs (handle both original format and reward format)
    const originalItemIds = originalItems.map(item => item.itemId || item._id?.toString() || item);
    const currentItemIds = currentItems.map(item => item.itemId || item._id?.toString() || item);

    if (!originalItemIds.every(id => currentItemIds.includes(id))) return true;
    if (!currentItemIds.every(id => originalItemIds.includes(id))) return true;

    // Check boosters and stars
    if (originalRewards.xpBooster !== currentRewards.xpBooster) return true;
    if (originalRewards.dropBooster !== currentRewards.dropBooster) return true;
    if (originalRewards.stars !== currentRewards.stars) return true;

    return false;
}

/**
 * Generate EXP options for select menu
 * @param {number} minExp - Minimum EXP value
 * @returns {Array} Array of select options
 */
function generateExpOptions(minExp) {
    const options = [];

    // Add 0 EXP option for level 0
    if (minExp === 0) {
        options.push({
            label: '0 EXP',
            value: '0',
            description: 'Level 0 - Starting level'
        });
    }

    // Generate standard EXP values
    const expValues = [
        1000, 2000, 3000, 4000, 5000,
        7500, 10000, 15000, 20000, 25000,
        30000, 40000, 50000, 75000, 100000,
        150000, 200000, 300000, 500000, 1000000
    ];

    expValues.forEach(exp => {
        if (exp >= minExp) {
            options.push({
                label: `${exp.toLocaleString()} EXP`,
                value: exp.toString(),
                description: `${exp >= 1000000 ? (exp / 1000000).toFixed(1) + 'M' : exp >= 1000 ? (exp / 1000).toFixed(1).replace('.0', '') + 'K' : exp} experience points`
            });
        }
    });

    return options;
}

/**
 * Clear global level state for a user (ENHANCED: Cross-feature state management)
 * @param {string} userId - User ID
 */
function clearGlobalLevelState(userId) {
    const createKey = `${userId}_create`;
    const editKey = `${userId}_edit`;

    tempGlobalLevelState.delete(createKey);
    tempGlobalLevelState.delete(editKey);

    console.log(`[owner-global-levels] 🧹 Cleared global level state for user ${userId}`);
}

/**
 * Clear all feature states when switching between features (ENHANCED: Cross-feature state management)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (for items)
 */
async function clearAllFeatureStates(userId, guildId) {
    // Clear global levels state
    clearGlobalLevelState(userId);

    // Clear custom emoji state (if available)
    try {
        const { clearEmojiState } = require('./owner-emojis.js');
        clearEmojiState(userId);
    } catch (error) {
        // Emoji module might not be available
    }

    // Clear items state (if available)
    try {
        const { clearCreationState } = require('./items.js');
        await clearCreationState(userId, guildId);
    } catch (error) {
        // Items module might not be available
    }

    console.log(`[owner-global-levels] 🧹 Cleared all feature states for user ${userId}`);
}

module.exports = {
    buildGlobalLevelsContainer,
    startGlobalLevelCreation,
    showGlobalLevelCreationInterface,
    buildGlobalLevelConfigContainer,
    handleGlobalLevelsAction,
    startGlobalLevelEdit,
    showGlobalLevelEditInterface,
    createGlobalLevelsIndexes,
    hasGlobalLevelBeenModified,
    generateExpOptions,
    clearGlobalLevelState,
    tempGlobalLevelState
};
