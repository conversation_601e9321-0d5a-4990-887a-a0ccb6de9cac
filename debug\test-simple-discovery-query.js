/**
 * Simple test to check discovery records in the database
 */

require('dotenv').config();

async function testSimpleDiscoveryQuery() {
    console.log('🔧 Simple discovery records test...');
    
    try {
        // Test database operations directly
        const { optimizedCountDocuments, optimizedFind } = require('../utils/database-optimizer.js');
        
        console.log('\n=== Test 1: Total Records Check ===');
        
        // Check total records in item_records
        const totalRecords = await optimizedCountDocuments("item_records", {});
        console.log(`Total item_records: ${totalRecords}`);
        
        // Check discovery records specifically
        const discoveryRecords = await optimizedCountDocuments("item_records", {
            parameter: '_item_discovery'
        });
        console.log(`Discovery records: ${discoveryRecords}`);
        
        if (discoveryRecords === 0) {
            console.log('❌ ROOT CAUSE FOUND: No discovery records exist in database');
            console.log('   This explains why getLiveDiscoveryTotal returns 0');
            
            // Check if there are any records at all
            if (totalRecords === 0) {
                console.log('   The item_records collection is completely empty');
            } else {
                console.log('   Records exist but none have parameter "_item_discovery"');
                
                // Check what parameters do exist
                const sampleRecords = await optimizedFind("item_records", {}, { limit: 5 });
                console.log('   Sample records:');
                sampleRecords.forEach((record, i) => {
                    console.log(`     ${i + 1}. parameter: "${record.parameter}", itemName: "${record.itemName}"`);
                });
            }
        } else {
            console.log('✅ Discovery records found, investigating further...');
            
            // Get sample discovery records
            const sampleDiscovery = await optimizedFind("item_records", 
                { parameter: '_item_discovery' }, 
                { limit: 3, sort: { recordedAt: -1 } }
            );
            
            console.log('Sample discovery records:');
            sampleDiscovery.forEach((record, i) => {
                console.log(`  ${i + 1}. ${record.itemName} (${record.itemType}) - ${record.scope} - ${record.guildId || 'global'}`);
            });
            
            // Test a specific query
            if (sampleDiscovery.length > 0) {
                const testRecord = sampleDiscovery[0];
                const testQuery = {
                    scope: testRecord.scope,
                    itemName: testRecord.itemName,
                    itemType: testRecord.itemType,
                    parameter: '_item_discovery'
                };
                
                if (testRecord.guildId) {
                    testQuery.guildId = testRecord.guildId;
                }
                
                const queryResult = await optimizedCountDocuments("item_records", testQuery);
                console.log(`Test query result: ${queryResult} records`);
                console.log('Test query:', testQuery);
            }
        }
        
        console.log('\n=== Test 2: Collection Structure Check ===');
        
        // Check if user_inventory has records (this is where items are stored)
        const inventoryRecords = await optimizedCountDocuments("user_inventory", {});
        console.log(`User inventory records: ${inventoryRecords}`);
        
        if (inventoryRecords > 0) {
            const sampleInventory = await optimizedFind("user_inventory", {}, { limit: 3 });
            console.log('Sample inventory records:');
            sampleInventory.forEach((record, i) => {
                console.log(`  ${i + 1}. ${record.itemName} (${record.itemType}) - User: ${record.userId}`);
            });
        }
        
        console.log('\n=== Test 3: Data Flow Analysis ===');
        
        if (inventoryRecords > 0 && discoveryRecords === 0) {
            console.log('❌ ISSUE IDENTIFIED: Items exist in inventory but no discovery records');
            console.log('   This suggests the item discovery tracking is not working');
            console.log('   When items are found, discovery records should be created in item_records');
        } else if (inventoryRecords === 0) {
            console.log('ℹ️  No items in inventory - this is normal if no items have been found yet');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error during simple test:', error);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testSimpleDiscoveryQuery().then(success => {
        if (success) {
            console.log('\n🏁 Simple discovery test completed');
            process.exit(0);
        } else {
            console.log('\n💥 Test failed');
            process.exit(1);
        }
    }).catch(error => {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { testSimpleDiscoveryQuery };
