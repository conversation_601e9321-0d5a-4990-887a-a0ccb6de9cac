const { Container<PERSON>uilder, SectionBuilder, TextDisplayBuilder, ThumbnailBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ActionRowBuilder, MessageFlags, SeparatorBuilder, SeparatorSpacingSize, ChannelSelectMenuBuilder } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne, optimizedUpdateMany, optimizedDeleteOne, optimizedDeleteMany, optimizedFind, optimizedFindOneAndUpdate, optimizedCountDocuments, optimizedAggregate } = require('../../utils/database-optimizer.js');
const { sendFeatureToggleLog } = require("../../utils/sendLog.js");
const { buildSelectMenu } = require('./featuresMenu');
const fetch = require('node-fetch');
const { parseParameterValue } = require('../../utils/itemRecords.js');
const { getRecentImagesFromChannel, uploadImageAsEmote, buildImageSelectMenu, buildNoImagesSelectMenu, handleImageSelection } = require('../../utils/imageUploader.js');
const { markEmojiAsSaved } = require('../../utils/emojiCleanup.js');
const { OPERATION_COLORS, RARITY_COLORS } = require('../../utils/colors.js');
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');
const { getFeatureStatus } = require('../../utils/permissionHandler.js');

/**
 * Items System (Enterprise-Grade Performance Optimized)
 * Handles all item management with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const itemsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    itemsProcessed: 0,
    inventoryLookupsProcessed: 0,
    guildConfigLookupsProcessed: 0,
    personalRecordChecks: 0,
    parameterCalculations: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const itemStateCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for item creation states
const guildItemConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild item configurations
const userInventoryCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user inventory data
const itemParameterCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for parameter calculations
const personalRecordCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for personal record data

// Register caches for global cleanup (MANDATORY)
registerCache(itemStateCache);
registerCache(guildItemConfigCache);
registerCache(userInventoryCache);
registerCache(itemParameterCache);
registerCache(personalRecordCache);

/**
 * Get cached guild item configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Guild configuration data
 */
async function getCachedGuildItemConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_item_config_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildItemConfigCache.get(cacheKey);
        if (cached) {
            itemsMetrics.cacheHits++;
            if (itemsMetrics.verboseLogging) {
                console.log(`[items] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        itemsMetrics.cacheMisses++;
        itemsMetrics.databaseQueries++;
        itemsMetrics.guildConfigLookupsProcessed++;

        // Get guild data
        const guildData = await optimizedFindOne("guilds", { id: guildId });
        if (!guildData) {
            // Return null for non-existent guilds instead of creating defaults
            return null;
        }

        // Cache the result
        guildItemConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        itemsMetrics.averageQueryTime =
            (itemsMetrics.averageQueryTime * (itemsMetrics.databaseQueries - 1) + duration) /
            itemsMetrics.databaseQueries;

        if (itemsMetrics.verboseLogging || duration > 100) {
            console.log(`[items] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error('[items] ❌ Error getting guild config:', error);
        return null;
    }
}

/**
 * Get cached user inventory data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for inventory lookups
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional, for guild-specific inventory)
 * @returns {Promise<Array>} User inventory items
 */
async function getCachedUserInventory(userId, guildId = null) {
    const startTime = Date.now();
    const cacheKey = guildId ? `user_inventory_${userId}_${guildId}` : `user_inventory_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = userInventoryCache.get(cacheKey);
        if (cached) {
            itemsMetrics.cacheHits++;
            if (itemsMetrics.verboseLogging) {
                console.log(`[items] ⚡ User inventory cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        itemsMetrics.cacheMisses++;
        itemsMetrics.databaseQueries++;
        itemsMetrics.inventoryLookupsProcessed++;

        // Build query
        const query = { userId: userId };
        if (guildId) {
            query.guildId = guildId;
        }

        // Get user inventory
        const inventory = await optimizedFind("user_inventory", query);

        // Cache the result
        userInventoryCache.set(cacheKey, inventory);

        const duration = Date.now() - startTime;
        itemsMetrics.averageQueryTime =
            (itemsMetrics.averageQueryTime * (itemsMetrics.databaseQueries - 1) + duration) /
            itemsMetrics.databaseQueries;

        if (itemsMetrics.verboseLogging || duration > 100) {
            console.log(`[items] ✅ User inventory fetched for ${userId}: ${inventory.length} items in ${duration}ms - cached for future access`);
        }

        return inventory;
    } catch (error) {
        console.error('[items] ❌ Error getting user inventory:', error);
        return [];
    }
}

/**
 * Check if a parameter value is a personal record for the user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for personal record checks
 * @param {string} userId - User ID
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 * @param {string} paramName - Parameter name
 * @param {string} paramValue - Parameter value
 * @returns {Object} { isPersonalRecord: boolean, previousBest: string|null }
 */
async function checkPersonalRecord(userId, itemName, itemType, paramName, paramValue, excludeCurrentItem = true) {
    const startTime = Date.now();

    try {
        itemsMetrics.personalRecordChecks++;

        // OPTIMIZED: Use cached user inventory for personal record checking
        const userItems = await getCachedUserInventory(userId);

        // Filter items for this specific type and parameter
        const relevantItems = userItems.filter(item =>
            item.itemName === itemName &&
            item.itemType === itemType &&
            item.catchData &&
            item.catchData[paramName] !== undefined
        );

        // If excluding current item, filter out recent items (last 5 seconds)
        let filteredItems = relevantItems;
        if (excludeCurrentItem) {
            const fiveSecondsAgo = new Date(Date.now() - 5000);
            filteredItems = relevantItems.filter(item =>
                new Date(item.droppedAt) < fiveSecondsAgo
            );
        }

        if (filteredItems.length === 0) {
            // First item of this type = personal record
            const duration = Date.now() - startTime;
            if (itemsMetrics.verboseLogging || duration > 50) {
                console.log(`[items] ✅ Personal record check for ${userId}: first item (${duration}ms)`);
            }
            return { isPersonalRecord: true, previousBest: null };
        }

        // Parse current value
        const currentNumeric = parseParameterValue(paramValue);
        if (currentNumeric === null) {
            return { isPersonalRecord: false, previousBest: null };
        }

        // Find previous best value from filtered items
        let previousBest = null;
        let previousBestNumeric = -1;

        for (const item of filteredItems) {
            const itemParamValue = item.catchData?.[paramName];
            if (itemParamValue) {
                const itemNumeric = parseParameterValue(itemParamValue);
                if (itemNumeric !== null && itemNumeric > previousBestNumeric) {
                    previousBestNumeric = itemNumeric;
                    previousBest = itemParamValue;
                }
            }
        }

        // Check if current value beats previous best
        const isPersonalRecord = currentNumeric > previousBestNumeric;

        const duration = Date.now() - startTime;
        if (itemsMetrics.verboseLogging || duration > 50) {
            console.log(`[items] ✅ Personal record check for ${userId}: ${isPersonalRecord ? 'NEW RECORD' : 'not a record'} (${duration}ms)`);
        }

        return { isPersonalRecord, previousBest };

    } catch (error) {
        console.error('[items] Error checking personal record:', error);
        return { isPersonalRecord: false, previousBest: null };
    }
}

/**
 * Get comprehensive items system performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive items system performance data
 */
function getItemsSystemStats() {
    const cacheHitRate = itemsMetrics.cacheHits + itemsMetrics.cacheMisses > 0 ?
        (itemsMetrics.cacheHits / (itemsMetrics.cacheHits + itemsMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: itemsMetrics.cacheHits,
            cacheMisses: itemsMetrics.cacheMisses,
            databaseQueries: itemsMetrics.databaseQueries,
            averageQueryTime: `${itemsMetrics.averageQueryTime.toFixed(2)}ms`,
            itemsProcessed: itemsMetrics.itemsProcessed,
            inventoryLookupsProcessed: itemsMetrics.inventoryLookupsProcessed,
            guildConfigLookupsProcessed: itemsMetrics.guildConfigLookupsProcessed,
            personalRecordChecks: itemsMetrics.personalRecordChecks,
            parameterCalculations: itemsMetrics.parameterCalculations,
            lastOptimization: new Date(itemsMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            itemState: itemStateCache.getStats(),
            guildItemConfig: guildItemConfigCache.getStats(),
            userInventory: userInventoryCache.getStats(),
            itemParameter: itemParameterCache.getStats(),
            personalRecord: personalRecordCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            itemState: itemStateCache.getStats().memoryUsage,
            guildItemConfig: guildItemConfigCache.getStats().memoryUsage,
            userInventory: userInventoryCache.getStats().memoryUsage,
            itemParameter: itemParameterCache.getStats().memoryUsage,
            personalRecord: personalRecordCache.getStats().memoryUsage,
            total: itemStateCache.getStats().memoryUsage +
                   guildItemConfigCache.getStats().memoryUsage +
                   userInventoryCache.getStats().memoryUsage +
                   itemParameterCache.getStats().memoryUsage +
                   personalRecordCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    itemsMetrics.lastOptimization = Date.now();

    const stats = getItemsSystemStats();
    if (itemsMetrics.verboseLogging) {
        console.log(`[items] 📊 Performance Report:`);
        console.log(`[items]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[items]   Items Processed: ${stats.performance.itemsProcessed}`);
        console.log(`[items]   Inventory Lookups: ${stats.performance.inventoryLookupsProcessed}`);
        console.log(`[items]   Guild Config Lookups: ${stats.performance.guildConfigLookupsProcessed}`);
        console.log(`[items]   Personal Record Checks: ${stats.performance.personalRecordChecks}`);
        console.log(`[items]   Parameter Calculations: ${stats.performance.parameterCalculations}`);
        console.log(`[items]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[items]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[items]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Clear all items system caches (Enterprise-Grade Cache Management)
 */
function clearAllItemsCaches() {
    itemStateCache.clear();
    guildItemConfigCache.clear();
    userInventoryCache.clear();
    itemParameterCache.clear();
    personalRecordCache.clear();

    console.log('[items] 🗑️ Cleared all items system caches');
}

/**
 * Invalidate user inventory cache (for when inventory changes)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional)
 */
function invalidateUserInventoryCache(userId, guildId = null) {
    const cacheKey = guildId ? `user_inventory_${userId}_${guildId}` : `user_inventory_${userId}`;
    userInventoryCache.delete(cacheKey);

    // Also clear global user inventory cache
    userInventoryCache.delete(`user_inventory_${userId}`);

    if (itemsMetrics.verboseLogging) {
        console.log(`[items] 🗑️ Invalidated inventory cache for user ${userId}`);
    }
}

/**
 * Invalidate guild item configuration cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildItemConfigCache(guildId) {
    const cacheKey = `guild_item_config_${guildId}`;
    guildItemConfigCache.delete(cacheKey);

    if (itemsMetrics.verboseLogging) {
        console.log(`[items] 🗑️ Invalidated guild config cache for ${guildId}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, itemsMetrics.performanceReportInterval);



// Parameter randomizer system
const PARAMETER_RANDOMIZER = {
    /**
     * Randomize a parameter value based on its configuration with weighted rarity distribution
     * Bottom 10% and top 10% of values are incredibly rare (1% chance each)
     * Middle 80% of values are common (98% chance total)
     * @param {string} parameterValue - The parameter value (e.g., "2-150lbs", "1-5 oz")
     * @param {number} step - The step interval (e.g., 1, 0.1, 0.5, 15)
     * @param {string} parameterName - The parameter name for context
     * @param {boolean} useRarityDistribution - Whether to use weighted rarity distribution (default: true)
     * @returns {string} Randomized value (e.g., "47.5lbs")
     */
    randomizeParameter(parameterValue, step = 1, parameterName = null, useRarityDistribution = true) {
        if (!parameterValue || typeof parameterValue !== 'string') {
            return parameterValue;
        }

        // Handle 'random' parameter values by selecting from available options
        if (parameterValue === 'random' && parameterName) {
            // Define parameter options inline to avoid dependency issues
            const parameterOptions = {
                condition: ['pristine', 'excellent', 'good', 'fair', 'poor'],
                origin: ['cosmic_void', 'ancient_ruins', 'deep_ocean', 'alien_world', 'time_rift'],
                freshness: ['fresh', 'good', 'fair', 'wilted', 'dried']
            };

            const options = parameterOptions[parameterName];
            if (options && options.length > 0) {
                const randomIndex = Math.floor(Math.random() * options.length);
                return options[randomIndex];
            }
            // If no options available, return as-is
            return parameterValue;
        }

        // Extract range pattern: "min-max unit" or "min-max"
        const rangeMatch = parameterValue.match(/^(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)\s*(.*)$/);

        if (!rangeMatch) {
            // Not a range, return as-is
            return parameterValue;
        }

        const [, minStr, maxStr, unit] = rangeMatch;
        const min = parseFloat(minStr);
        const max = parseFloat(maxStr);

        if (isNaN(min) || isNaN(max) || min >= max) {
            // Invalid range, return as-is
            return parameterValue;
        }

        // Calculate random value within range, respecting step interval
        const range = max - min;
        const steps = Math.floor(range / step);

        // Fix for lower bounds: if min is 0, start from the first increment
        // This prevents items with 0 weight, 0 height, etc. which don't make sense
        let effectiveMin = min;
        let effectiveSteps = steps;

        if (min === 0) {
            effectiveMin = step; // Start from first increment instead of 0
            effectiveSteps = Math.floor((max - step) / step); // Adjust step count
        }

        let randomValue;

        if (useRarityDistribution && effectiveSteps > 10) {
            // Use weighted rarity distribution for ranges with enough steps
            randomValue = this.generateWeightedRarityValue(effectiveMin, max, step, effectiveSteps);
        } else {
            // Use uniform distribution for small ranges or when disabled
            const randomStep = Math.floor(Math.random() * (effectiveSteps + 1));
            randomValue = effectiveMin + (randomStep * step);
        }

        // Format the result
        const formattedValue = step < 1 ? randomValue.toFixed(1) : Math.round(randomValue).toString();
        return unit ? `${formattedValue}${unit}` : formattedValue;
    },

    /**
     * Generate a weighted rarity value where extreme values (bottom/top 10%) are incredibly rare
     * @param {number} min - Minimum value (effective minimum after 0-fix)
     * @param {number} max - Maximum value
     * @param {number} step - Step interval
     * @param {number} totalSteps - Total number of steps available
     * @returns {number} Weighted random value
     */
    generateWeightedRarityValue(min, max, step, totalSteps) {
        // Define the distribution zones
        const bottomZoneSize = Math.floor(totalSteps * 0.1); // Bottom 10%
        const topZoneSize = Math.floor(totalSteps * 0.1);    // Top 10%
        const middleZoneSize = totalSteps - bottomZoneSize - topZoneSize; // Middle 80%

        // Define weights (higher = more likely)
        const EXTREME_WEIGHT = 1;    // Bottom/top 10% get 1% chance each
        const MIDDLE_WEIGHT = 98;    // Middle 80% gets 98% chance total

        // Calculate total weight
        const totalWeight = (EXTREME_WEIGHT * 2) + MIDDLE_WEIGHT;

        // Roll to determine which zone
        const roll = Math.floor(Math.random() * totalWeight) + 1;

        let selectedStep;

        if (roll <= EXTREME_WEIGHT) {
            // Bottom 10% zone (incredibly rare)
            selectedStep = Math.floor(Math.random() * bottomZoneSize);
        } else if (roll <= EXTREME_WEIGHT * 2) {
            // Top 10% zone (incredibly rare)
            selectedStep = totalSteps - topZoneSize + Math.floor(Math.random() * topZoneSize);
        } else {
            // Middle 80% zone (common)
            selectedStep = bottomZoneSize + Math.floor(Math.random() * middleZoneSize);
        }

        // Convert step to actual value
        return min + (selectedStep * step);
    },

    /**
     * Randomize all parameters in an item
     * @param {Object} item - The item object with parameters
     * @param {Object} stepConfig - Step configuration for each parameter (optional)
     * @param {boolean} useRarityDistribution - Whether to use weighted rarity distribution (default: true)
     * @returns {Object} Item with randomized parameters
     */
    randomizeItemParameters(item, stepConfig = {}, useRarityDistribution = true) {
        if (!item || !item.parameters) {
            return item;
        }

        const randomizedParameters = {};

        for (const [paramName, paramValue] of Object.entries(item.parameters)) {
            const step = stepConfig[paramName] || this.getDefaultStep(paramValue);
            randomizedParameters[paramName] = this.randomizeParameter(paramValue, step, paramName, useRarityDistribution);
        }

        return {
            ...item,
            parameters: randomizedParameters
        };
    },

    /**
     * Get default step based on parameter value
     * @param {string} parameterValue - The parameter value
     * @returns {number} Default step interval
     */
    getDefaultStep(parameterValue) {
        if (!parameterValue || typeof parameterValue !== 'string') {
            return 1;
        }

        // Check for weight units - use 0.1 for precision
        if (parameterValue.includes('lbs') || parameterValue.includes('oz') || parameterValue.includes('kg')) {
            return 0.1;
        }

        // Check for length units - use 0.1 for precision
        if (parameterValue.includes('feet') || parameterValue.includes('cm') || parameterValue.includes('inches')) {
            return 0.1;
        }

        // Check for large numbers - use larger steps
        const rangeMatch = parameterValue.match(/(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)/);
        if (rangeMatch) {
            const max = parseFloat(rangeMatch[2]);
            if (max > 1000) return 50;  // Large numbers get bigger steps
            if (max > 100) return 5;    // Medium numbers get medium steps
            if (max > 10) return 0.5;   // Small numbers get small steps
        }

        return 1; // Default step
    }
};

// Rarity system with colors and weights for spinning wheel drops
// Supports both default emojis and custom Discord emotes
const RARITIES = {
    COMMON: {
        name: 'Common',
        color: RARITY_COLORS.COMMON,
        weight: 100000, // Most common
        emote: '👁️', // Can be replaced with custom emote like '<:common:123456789>'
        order: 1
    },
    UNCOMMON: {
        name: 'Uncommon',
        color: RARITY_COLORS.UNCOMMON,
        weight: 500, // Less common
        emote: '🍀', // Can be replaced with custom emote like '<:uncommon:123456789>'
        order: 2
    },
    RARE: {
        name: 'Rare',
        color: RARITY_COLORS.RARE,
        weight: 200, // Rare
        emote: '⚗️', // Can be replaced with custom emote like '<:rare:123456789>'
        order: 3
    },
    MYTHICAL: {
        name: 'Mythical',
        color: RARITY_COLORS.MYTHICAL,
        weight: 50, // Very rare
        emote: '🌀', // Can be replaced with custom emote like '<:mythical:123456789>'
        order: 4
    },
    GALACTIC: {
        name: 'Galactic',
        color: RARITY_COLORS.GALACTIC,
        weight: 20, // Extremely rare
        emote: '🪐', // Can be replaced with custom emote like '<:galactic:123456789>'
        order: 5
    },
    UNKNOWN: {
        name: 'Unknown',
        color: RARITY_COLORS.UNKNOWN,
        weight: 5, // Ultra rare
        emote: '🧬', // Can be replaced with custom emote like '<:unknown:123456789>'
        order: 6
    },
    NONE: {
        name: 'None',
        color: OPERATION_COLORS.NEUTRAL, // Use neutral color for NONE rarity
        weight: 100, // Default weight for items where rarity doesn't matter
        emote: '', // No emote for NONE rarity
        order: 0, // First in order (lowest)
        hideDisplay: true // Special flag to hide rarity display
    }
};

// Weight for "nothing" drops - controls overall item drop frequency
// Higher = items drop less often, Lower = items drop more often
const NOTHING_WEIGHT = 8000;

const DROP_LOCATIONS = {
    TEXT: {
        name: 'Text EXP',
        displayName: 'text chat',
        description: 'Drops when gaining EXP from text messages',
        emote: '💬'
    },
    VOICE: {
        name: 'Voice EXP',
        displayName: 'voice chat',
        description: 'Drops when gaining EXP from voice activity',
        emote: '🎤'
    },
    LEVEL_UP: {
        name: 'Level Up',
        displayName: 'level up',
        description: 'Rewards given when reaching global levels',
        emote: '🆙'
    },
    STARFALL: {
        name: 'Starfall',
        displayName: 'starfall',
        description: 'Daily reward items from Starfall feature',
        emote: '⭐'
    }
};

/**
 * Format drop location with scope context
 * @param {string} locationKey - Location key (TEXT, VOICE, LEVEL_UP)
 * @param {boolean} isGlobalContext - Whether this is global (bot owner) context
 * @returns {string} Formatted location string
 */
function formatLocationWithScope(locationKey, isGlobalContext) {
    const location = DROP_LOCATIONS[locationKey];
    if (!location) return locationKey.toLowerCase();

    const scope = isGlobalContext ? 'global' : 'server';
    return `${scope}, ${location.displayName}`;
}

/**
 * Get rarity by name
 * @param {string} rarityName - Rarity name (COMMON, RARE, etc.)
 * @returns {Object|null} Rarity object or null if not found
 */
function getRarity(rarityName) {
    return RARITIES[rarityName] || null;
}

/**
 * Get all rarities sorted by order
 * @returns {Array} Array of rarity objects with keys
 */
function getAllRarities() {
    return Object.entries(RARITIES)
        .map(([key, rarity]) => ({ key, ...rarity }))
        .sort((a, b) => a.order - b.order);
}

/**
 * Generate a random rarity based on weights (spinning wheel system)
 * @returns {string} Rarity key (COMMON, RARE, etc.)
 */
function generateRandomRarity() {
    // Calculate total weight of all rarities
    const rarities = getAllRarities();
    const totalWeight = rarities.reduce((sum, rarity) => sum + rarity.weight, 0);

    // Roll from 1 to totalWeight
    const roll = Math.floor(Math.random() * totalWeight) + 1;

    // Find which rarity this roll corresponds to
    let currentWeight = 0;
    for (const rarity of rarities) {
        currentWeight += rarity.weight;
        if (roll <= currentWeight) {
            return rarity.key;
        }
    }

    return 'COMMON'; // Fallback
}

/**
 * Format rarity for display with emote
 * @param {string} rarityKey - Rarity key
 * @param {boolean} includeWeight - Whether to include weight in parentheses
 * @returns {string} Formatted rarity string, or empty string for NONE rarity
 */
function formatRarity(rarityKey, includeWeight = false) {
    const rarity = getRarity(rarityKey);
    if (!rarity) return 'Unknown';

    // Hide display for NONE rarity
    if (rarity.hideDisplay) return '';

    // Only include emote if it exists and is not empty
    const emotePrefix = (rarity.emote && rarity.emote.trim() !== '') ? `${rarity.emote} ` : '';
    let formatted = `${emotePrefix}${rarity.name}`;
    if (includeWeight) {
        formatted += ` (${rarity.weight})`;
    }
    return formatted;
}

/**
 * Format rarity for item display with weight
 * @param {string} rarityKey - Rarity key
 * @returns {string} Formatted rarity string with weight, or empty string for NONE rarity
 */
function formatRarityWithWeight(rarityKey) {
    const rarity = getRarity(rarityKey);
    if (!rarity) return 'Unknown';

    // Hide display for NONE rarity
    if (rarity.hideDisplay) return '';

    return `${rarity.name} (${rarity.weight})`;
}

// Item types with their parameters
const ITEM_TYPES = {
    JUNK: { 
        name: 'Junk', 
        description: 'Filler items, common drops',
        parameters: ['weight', 'condition'],
        emote: '🗑️'
    },
    ANIMAL: {
        name: 'Animal',
        description: 'Land or air creatures',
        parameters: ['weight', 'height', 'length'],
        emote: '🐾'
    },
    AQUATIC: {
        name: 'Aquatic',
        description: 'Fish and sea creatures',
        parameters: ['weight', 'length'],
        emote: '🐟'
    },
    INSECT: {
        name: 'Insect',
        description: 'Bugs, beetles, flying or crawling',
        parameters: ['weight', 'length'],
        emote: '🐛'
    },
    TOOL: {
        name: 'Tool',
        description: 'Usable items like nets, rods, etc.',
        parameters: ['condition', 'usesLeft'],
        emote: '🔧'
    },
    ARTIFACT: {
        name: 'Artifact',
        description: 'Unique or magical finds',
        parameters: ['condition', 'age', 'origin'],
        emote: '🏺'
    },
    TREASURE: {
        name: 'Treasure',
        description: 'Gold, gems, or rare shiny things',
        parameters: ['weight', 'purity'],
        emote: '💎'
    },
    HUMANOID: { 
        name: 'Humanoid', 
        description: 'Creepy/morbid finds like corpses',
        parameters: ['height', 'weight', 'age', 'condition'],
        emote: '💀'
    },
    PLANT: {
        name: 'Plant',
        description: 'Herbs, flowers, fungi, trees, etc.',
        parameters: ['height', 'age', 'freshness'],
        emote: '🌱'
    },
    OTHER: {
        name: 'Other',
        description: 'Simple trophy items without parameters',
        parameters: [],
        emote: '🕳'
    }
};

// Note: Item creation access now uses the standardized global.hasFeaturePermission() system
// which includes server owner bypass and Administrator permission checking

/**
 * Store temporary item creation state (with caching and shared connection)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Object} state - Creation state
 */
async function storeCreationState(userId, guildId, state) {
    // FIXED: Removed clearExpiredCache() call - LRU cache handles TTL automatically
    const cacheKey = `${userId}-${guildId}`;

    // Update LRU cache immediately for instant UI updates
    itemStateCache.set(cacheKey, { ...state, updatedAt: new Date() });

    // Update database asynchronously (don't await to improve performance)
    setImmediate(async () => {
        try {
            await optimizedUpdateOne("item_creation_temp",
                { userId, guildId },
                { $set: { ...state, updatedAt: new Date() } },
                { upsert: true }
            );
        } catch (error) {
            console.error('Error storing creation state:', error);
        }
    });
}

/**
 * Get temporary item creation state (with caching)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Object|null} Creation state
 */
async function getCreationState(userId, guildId) {
    // FIXED: Removed clearExpiredCache() call - LRU cache handles TTL automatically
    const cacheKey = `${userId}-${guildId}`;

    // Check LRU cache first
    const cached = itemStateCache.get(cacheKey);
    if (cached) {
        return cached;
    }

    // Fetch from database if not in LRU cache
    try {
        const state = await optimizedFindOne("item_creation_temp", { userId, guildId });

        if (state) {
            // Store in LRU cache for future access
            itemStateCache.set(cacheKey, state);
        }

        return state;
    } catch (error) {
        console.error('[items] ❌ Error getting creation state:', error);
        return null;
    }
}

/**
 * Clear temporary item creation state (with cache clearing)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 */
async function clearCreationState(userId, guildId) {
    const cacheKey = `${userId}-${guildId}`;

    // Clear from cache immediately
    itemStateCache.delete(cacheKey);

    // Clear from database asynchronously
    setImmediate(async () => {
        try {
            await optimizedDeleteOne("item_creation_temp", { userId, guildId });
        } catch (error) {
            console.error('Error clearing creation state:', error);
        }
    });
}

/**
 * Clear all feature states when switching between features (ENHANCED: Cross-feature state management)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 */
async function clearAllFeatureStates(userId, guildId) {
    // Clear items state
    await clearCreationState(userId, guildId);

    // Clear custom emoji state (if available)
    try {
        const { clearEmojiState } = require('./owner-emojis.js');
        clearEmojiState(userId);
    } catch (error) {
        // Emoji module might not be available
    }

    // Clear global levels state (if available)
    try {
        const { clearGlobalLevelState } = require('./owner-global-levels.js');
        clearGlobalLevelState(userId);
    } catch (error) {
        // Global levels module might not be available
    }

    console.log(`[items] 🧹 Cleared all feature states for user ${userId}`);
}

/**
 * Create a new custom item in the database (optimized with shared connection)
 * @param {Object} itemData - Item data
 * @param {string} itemData.name - Item name
 * @param {string} itemData.type - Item type (JUNK, ANIMAL, etc.)
 * @param {string} itemData.rarity - Item rarity (COMMON, RARE, etc.)
 * @param {string} itemData.emote - Item emote
 * @param {string} itemData.emoteId - Application emote ID (for cleanup)
 * @param {Object} itemData.parameters - Item parameters (weight, length, etc.)
 * @param {string} itemData.description - Item description
 * @param {string} itemData.createdBy - User ID who created the item
 * @param {string} itemData.guildId - Guild ID (null for bot-wide items)
 * @returns {Object} Created item with ID
 */
async function createItem(itemData) {
    try {
        // Get order numbers in parallel for better performance
        const [globalOrder, guildOrder] = await Promise.all([
            getNextGlobalOrder(),
            itemData.guildId ? getNextGuildOrder(itemData.guildId) : Promise.resolve(null)
        ]);

        const item = {
            ...itemData,
            id: `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            createdAt: new Date(),
            globalOrder,
            guildOrder
        };

        // PERFORMANCE OPTIMIZATION: Use optimized database operation with monitoring and retry logic
        await optimizedInsertOne('custom_items', item);

        // CRITICAL FIX: Invalidate item cache after creation to ensure new item is visible immediately
        try {
            const { invalidateItemCache } = require('../../utils/itemCache.js');
            invalidateItemCache(item.id);
            console.log(`[items] 🔄 Invalidated item cache for newly created item: ${item.id} (${item.name})`);
        } catch (cacheError) {
            console.error('[items] Error invalidating item cache after creation:', cacheError);
        }

        // FIXED: Clear all relevant caches to force refresh after item creation
        clearAllItemsCaches();

        return item;
    } catch (error) {
        console.error('Error creating item:', error);
        throw error;
    }
}

/**
 * Get a custom item by ID (optimized with shared connection)
 * @param {string} itemId - Item ID to fetch
 * @returns {Object|null} Item data or null if not found
 */
async function getItemById(itemId) {
    try {
        const item = await optimizedFindOne("custom_items", { id: itemId });
        return item;
    } catch (error) {
        console.error('Error fetching item by ID:', error);
        return null;
    }
}

/**
 * Delete a custom item and its associated application emote
 * @param {string} itemId - Item ID to delete
 * @param {Object} client - Discord client for emote deletion
 * @returns {boolean} Success status
 */
async function deleteItem(itemId, client) {
    try {


        // Get the item to find emote ID
        const item = await optimizedFindOne("custom_items", { id: itemId });

        if (item) {
            // Delete the application emote if it exists
            if (item.emoteId) {
                try {
                    await client.application.emojis.delete(item.emoteId);
                    console.log(`Deleted application emote ${item.emoteId} for item ${itemId}`);
                } catch (emoteError) {
                    console.error(`Failed to delete emote ${item.emoteId}:`, emoteError);
                    // Continue with item deletion even if emote deletion fails
                }
            }

            // Perform database operations in parallel for better performance
            const [inventoryDeleteResult, leaderboardDeleteResult, itemDeleteResult] = await Promise.all([
                optimizedDeleteMany("user_inventory", { itemId: itemId }),
                optimizedDeleteMany("item_leaderboards", {
                    itemName: item.name,
                    itemType: item.type
                }),
                optimizedDeleteOne("custom_items", { id: itemId })
            ]);

            console.log(`Removed ${inventoryDeleteResult.deletedCount} instances of item ${itemId} from user inventories`);
            console.log(`Removed ${leaderboardDeleteResult.deletedCount} leaderboard records for item ${item.name} (${item.type})`);
            console.log(`Deleted item ${itemId} from items collection`);

            // CRITICAL FIX: Invalidate item cache after deletion to ensure item is removed immediately
            try {
                const { invalidateItemCache } = require('../../utils/itemCache.js');
                invalidateItemCache(itemId);
                console.log(`[items] 🔄 Invalidated item cache for deleted item: ${itemId} (${item.name})`);
            } catch (cacheError) {
                console.error('[items] Error invalidating item cache after deletion:', cacheError);
            }

            // FIXED: Clear all relevant caches to force refresh after item deletion
            clearAllItemsCaches();
        }

        return true;

    } catch (error) {
        console.error('Error deleting item:', error);
        return false;
    }
}

/**
 * Get next global order number for items (optimized with shared connection)
 * @returns {number} Next global order number
 */
async function getNextGlobalOrder() {
    try {
        const lastItem = await optimizedFindOne("custom_items", {}, { sort: { globalOrder: -1 } });
        return lastItem ? lastItem.globalOrder + 1 : 1;
    } catch (error) {
        console.error('Error getting next global order:', error);
        return 1;
    }
}

/**
 * Get next guild order number for items (optimized with shared connection)
 * @param {string} guildId - Guild ID
 * @returns {number} Next guild order number
 */
async function getNextGuildOrder(guildId) {
    try {
        const lastItem = await optimizedFindOne("custom_items", { guildId }, { sort: { guildOrder: -1 } });
        return lastItem ? lastItem.guildOrder + 1 : 1;
    } catch (error) {
        console.error('Error getting next guild order:', error);
        return 1;
    }
}

/**
 * Get item counts for display
 * @param {string} guildId - Guild ID (null for bot-wide items)
 * @param {boolean} isOwner - Whether user is bot owner
 * @returns {Object} Item counts {available, active, disabled}
 */
async function getItemCounts(guildId, isOwner) {
    try {

        let query = {};
        if (guildId === null) {
            // Bot owner: Show bot-wide items (items without guildId or with guildId: null)
            query = { $or: [{ guildId: null }, { guildId: { $exists: false } }] };
        } else {
            // Guild-specific items
            query = { guildId: guildId };
        }

        // OPTIMIZED: Use parallel optimizedCountDocuments for efficient counting (no data transfer)
        const [active, disabled, total] = await Promise.all([
            optimizedCountDocuments("custom_items", { ...query, disabled: { $ne: true } }),
            optimizedCountDocuments("custom_items", { ...query, disabled: true }),
            optimizedCountDocuments("custom_items", query)
        ]);

        // For bot owner, don't show available count (unlimited)
        // For guild, available = total items they can create
        if (isOwner) {
            return { active, disabled };
        } else {
            // Guild has a limit of items they can create
            const maxItems = 5; // Based on the premium system
            const available = Math.max(0, maxItems - total);
            return { available, active, disabled };
        }
    } catch (error) {
        console.error('Error getting item counts:', error);
        return isOwner ? { active: 0, disabled: 0 } : { available: 0, active: 0, disabled: 0 };
    }
}

/**
 * Helper function to determine if guild has real items data (not just defaults)
 * @param {string} guildId - Guild ID
 * @returns {Promise<boolean>} Whether guild has real items data
 */
async function hasRealItemsData(guildId) {
    if (!guildId) return false; // Bot-wide items always considered "real data"

    try {
        // Check if there are any custom items for this guild
        const itemCount = await optimizedCountDocuments("custom_items", { guildId: guildId });
        return itemCount > 0;
    } catch (error) {
        console.error('Error checking for real items data:', error);
        return false;
    }
}

/**
 * Get items for display (demo data or real data) with caching
 * @param {string} guildId - Guild ID
 * @param {boolean} hasAccess - Whether user has access to create/view items
 * @param {boolean} isEnabled - Whether items feature is enabled
 * @returns {Array} Array of items
 */
async function getItemsForDisplay(guildId, hasAccess, isEnabled = true) {
    // Determine if we should show demo data
    const shouldShowDemo = !hasAccess || (!isEnabled && !(await hasRealItemsData(guildId)));

    if (shouldShowDemo) {
        // Return demo data - exactly 5 items, no dollar values
        return [
            {
                id: 'demo-0',
                name: 'Stale Baguette',
                type: 'JUNK',
                rarity: 'COMMON',
                emote: '🥖',
                parameters: { weight: '2-4 lbs', condition: 'poor' },
                dropLocations: ['TEXT', 'VOICE'],
                isDemo: true
            },
            {
                id: 'demo-1',
                name: 'Shiny Diamond',
                type: 'TREASURE',
                rarity: 'MYTHICAL',
                emote: '💎',
                parameters: { weight: '1-3 oz', purity: '95%' },
                dropLocations: ['TEXT', 'VOICE'],
                isDemo: true
            },
            {
                id: 'demo-2',
                name: 'Grizzly Bear',
                type: 'ANIMAL',
                rarity: 'RARE',
                emote: '🐻',
                parameters: { weight: '300-600 lbs', height: '6-8 feet', length: '5-7 feet' },
                dropLocations: ['TEXT', 'VOICE'],
                isDemo: true
            },
            {
                id: 'demo-3',
                name: 'Annoying Mosquito',
                type: 'INSECT',
                rarity: 'COMMON',
                emote: '🦟',
                parameters: { weight: '1-5 oz', length: '2-4 cm' },
                dropLocations: ['TEXT', 'VOICE'],
                isDemo: true
            },
            {
                id: 'demo-4',
                name: 'Mysterious Moon Rock',
                type: 'ARTIFACT',
                rarity: 'GALACTIC',
                emote: '🌔',
                parameters: { condition: 'pristine', age: '4.5 billion years', origin: 'cosmic_void' },
                dropLocations: ['TEXT', 'VOICE'],
                isDemo: true
            }
        ];
    }

    // OPTIMIZED: Use LRU cache system with performance monitoring
    const startTime = Date.now();
    const cacheKey = `items_display_${guildId || 'bot_wide'}`;

    try {
        // Check LRU cache first
        const cached = itemParameterCache.get(cacheKey);
        if (cached) {
            itemsMetrics.cacheHits++;
            if (itemsMetrics.verboseLogging) {
                console.log(`[items] ⚡ Items display cache hit for ${guildId || 'bot-wide'} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        itemsMetrics.cacheMisses++;
        itemsMetrics.databaseQueries++;

        // Fetch real items from database using optimized utilities
        let query = {};
        if (guildId === null) {
            // Bot owner: Show bot-wide items (items without guildId or with guildId: null)
            query = { $or: [{ guildId: null }, { guildId: { $exists: false } }] };
        } else {
            // Guild-specific items
            query = { guildId: guildId };
        }

        const items = await optimizedFind("custom_items", query, { sort: { createdAt: -1 } });

        // Cache the results in LRU cache
        itemParameterCache.set(cacheKey, items);

        const duration = Date.now() - startTime;
        itemsMetrics.averageQueryTime =
            (itemsMetrics.averageQueryTime * (itemsMetrics.databaseQueries - 1) + duration) /
            itemsMetrics.databaseQueries;

        if (itemsMetrics.verboseLogging || duration > 100) {
            console.log(`[items] ✅ Items display fetched for ${guildId || 'bot-wide'}: ${items.length} items in ${duration}ms - cached for future access`);
        }

        return items;
    } catch (error) {
        console.error('[items] ❌ Error fetching items for display:', error);
        return [];
    }
}

// Premium warning container removed - no longer needed

/**
 * Build the main items container
 * @param {Object} options - Container options
 * @param {boolean} options.isOwner - Whether user is bot owner
 * @param {string} options.guildId - Guild ID
 * @param {number} options.page - Current page (0-based)
 * @param {Object} options.client - Discord client
 * @param {Object} options.member - Guild member for permission checking
 * @param {boolean} options.showBackButton - Whether to show back button (owner panel context)
 * @param {string} options.selectedNotificationOption - Currently selected notification option to show cascading menu
 * @returns {ContainerBuilder} The container
 */
async function buildItemsContainer({ isOwner, guildId, page = 0, client = null, member = null, showBackButton = false, guildData = null, selectedNotificationOption = null }) {
    const components = [];
    
    // Header section with back button (only in owner panel context)
    if (showBackButton) {
        const heading = new TextDisplayBuilder().setContent('# items');
        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(heading)
            .setButtonAccessory(backButton);
        components.push(backSection);
    } else {
        const heading = new TextDisplayBuilder().setContent('# items');
        components.push(heading);
    }
    
    const quote = new TextDisplayBuilder().setContent('> so cool and unique');
    components.push(quote);

    // Add notification status display for both owner and guild context
    if (isOwner) {
        // Owner notification status
        let dmNotificationsEnabled = true;
        let dmMessageTemplate = 'You found {emoji} {items} in {server}, dropped from {location}:';
        try {
            const config = await optimizedFindOne("item_notifications", { key: 'global' });
            dmNotificationsEnabled = config?.enabled ?? true;
            dmMessageTemplate = config?.dmMessage ?? 'You found {emoji} {items} in {server}, dropped from {location}:';
        } catch (error) {
            console.error('Error fetching owner DM config for status:', error);
        }

        let statusText = `**send DMs:** ${dmNotificationsEnabled ? 'enabled' : 'disabled'}`;
        statusText += `\n**template:** ${dmMessageTemplate}`;

        const notificationStatus = new TextDisplayBuilder().setContent(statusText);
        components.push(notificationStatus);

        // Add large separator before items list
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
        components.push(separator);
    } else if (guildData) {
        const dropNotificationsEnabled = guildData.items?.dropNotificationsEnabled ?? false;
        const dropChannelId = guildData.items?.dropChannel;

        let statusText = `**drop notifications:** ${dropNotificationsEnabled ? 'enabled' : 'disabled'}\n`;
        statusText += `**drop channel:** ${dropChannelId ? `<#${dropChannelId}>` : 'not set'}`;

        const notificationStatus = new TextDisplayBuilder().setContent(statusText);
        components.push(notificationStatus);

        // Add large separator before items list
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
        components.push(separator);
    }
    
    // OPTIMIZED: Get guild data first, then items with enabled state
    const hasAccess = isOwner || global.hasFeaturePermission(member, 'items');

    // Get guild data first
    if (!guildData && guildId) {
        try {
            guildData = await getCachedGuildItemConfig(guildId);
        } catch (error) {
            console.error('Error fetching guild data for items:', error);
        }
    }

    if (!guildData) {
        guildData = { items: { enabled: true } };
    }

    // Ensure items structure exists
    if (!guildData.items) {
        guildData.items = { enabled: true };
    }

    // Get items for display with enabled state
    const isEnabled = guildData.items.enabled !== false;
    const items = await getItemsForDisplay(guildId, hasAccess, isEnabled);

    // Track performance metrics
    itemsMetrics.itemsProcessed += items.length;
    const itemsPerPage = 10;
    const startIndex = page * itemsPerPage;
    const pageItems = items.slice(startIndex, startIndex + itemsPerPage);

    // Add item count display at the top of the list
    if (hasAccess && !items.some(item => item.isDemo)) {
        const counts = await getItemCounts(guildId, isOwner);
        let countText = '';

        if (isOwner) {
            // Bot owner: don't show available count (unlimited)
            countText = `${counts.active} active, ${counts.disabled} disabled`;
        } else {
            // Guild: show available, active, disabled
            countText = `${counts.available} available, ${counts.active} active, ${counts.disabled} disabled`;
        }

        const countDisplay = new TextDisplayBuilder().setContent(countText);
        components.push(countDisplay);
    }

    // Items display
    if (pageItems.length > 0) {
        const itemsText = pageItems.map((item, i) => {
            const rarity = RARITIES[item.rarity];
            const type = ITEM_TYPES[item.type];
            const rarityText = rarity ? formatRarityWithWeight(item.rarity) : 'Unknown';
            const typeText = type ? type.name : 'Unknown';

            // Format drop locations with scope context
            let dropLocationsText = 'none';
            if (item.dropLocations && item.dropLocations.length > 0) {
                const isGlobalContext = !guildId; // Global context when no guildId
                const scope = isGlobalContext ? 'global' : 'server';
                const locationNames = item.dropLocations.map(loc => {
                    const location = DROP_LOCATIONS[loc];
                    return location ? location.displayName : loc.toLowerCase();
                }).join(', ');
                dropLocationsText = `${scope}, ${locationNames}`;
            }

            // Build item display - hide rarity column if NONE rarity (empty rarityText)
            let itemDisplay;
            if (rarityText) {
                itemDisplay = `${item.emote} **${item.name}** | ${typeText} | ${rarityText} | ${dropLocationsText}`;
            } else {
                // NONE rarity: hide rarity column entirely
                itemDisplay = `${item.emote} **${item.name}** | ${typeText} | ${dropLocationsText}`;
            }

            // Add strikethrough if item is disabled
            if (item.disabled) {
                itemDisplay = `~~${itemDisplay}~~`;
            }

            return itemDisplay;
        }).join('\n');
        
        const itemsDisplay = new TextDisplayBuilder().setContent(itemsText);
        components.push(itemsDisplay);
    } else {
        const noItemsDisplay = new TextDisplayBuilder().setContent('__no items found__');
        components.push(noItemsDisplay);
    }
    
    // Pagination controls
    const totalPages = Math.ceil(items.length / itemsPerPage);
    if (totalPages > 1) {
        // Include access mode in button IDs to preserve context
        const accessMode = isOwner ? 'owner' : 'regular';

        const prevButton = new ButtonBuilder()
            .setCustomId(`items-page-${Math.max(0, page - 1)}-${accessMode}`)
            .setLabel('<')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(page === 0);

        const pageDisplay = new ButtonBuilder()
            .setCustomId('items-page-display')
            .setLabel(`${startIndex + 1}-${Math.min(startIndex + itemsPerPage, items.length)}`)
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(true);

        const nextButton = new ButtonBuilder()
            .setCustomId(`items-page-${Math.min(totalPages - 1, page + 1)}-${accessMode}`)
            .setLabel('>')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(page >= totalPages - 1);
            
        const paginationRow = new ActionRowBuilder().addComponents(prevButton, pageDisplay, nextButton);
        components.push(paginationRow);
    }
    
    // Item selection menu (disabled for users without access or when feature is disabled)
    const isFeatureEnabled = !guildData || guildData.items.enabled !== false;
    // Encode access context in custom ID: owner panel vs guild access
    const accessContext = showBackButton ? 'owner' : 'guild';
    const itemSelect = new StringSelectMenuBuilder()
        .setCustomId(`items-select-${accessContext}`)
        .setPlaceholder('item(s)')
        .setDisabled(!hasAccess || !isFeatureEnabled);

    // Always show "Add New Item" option at the top
    itemSelect.addOptions({
        label: 'Add New Item',
        value: 'add-new',
        description: 'Create a new custom item',
        emoji: '➕'
    });

    // Add notification configuration options to the items select menu
    if (isOwner) {
        // Bot owner notification configuration - get current state
        let dmNotificationsEnabled = true;
        try {
            const config = await optimizedFindOne("item_notifications", { key: 'global' });
            dmNotificationsEnabled = config?.enabled ?? true;
        } catch (error) {
            console.error('Error fetching owner DM config:', error);
        }

        itemSelect.addOptions(
            {
                label: dmNotificationsEnabled ? 'disable DM notifications' : 'enable DM notifications',
                value: 'toggle-owner-dm-notifications',
                description: dmNotificationsEnabled ? 'Currently enabled' : 'Currently disabled',
                emoji: '📨',
                default: selectedNotificationOption === 'toggle-owner-dm-notifications'
            },
            {
                label: 'edit DM message template',
                value: 'edit-owner-dm-message-template',
                description: 'Customize the DM message format',
                emoji: '✏️',
                default: selectedNotificationOption === 'edit-owner-dm-message-template'
            }
        );
    } else {
        // Guild notification configuration options (simplified)
        const dropNotificationsEnabled = guildData?.items?.dropNotificationsEnabled ?? false;

        itemSelect.addOptions(
            {
                label: dropNotificationsEnabled ? 'disable drop notifications' : 'enable drop notifications',
                value: 'toggle-drop-notifications',
                description: dropNotificationsEnabled ? 'Currently enabled' : 'Currently disabled',
                emoji: '📢',
                default: selectedNotificationOption === 'toggle-drop-notifications'
            },
            {
                label: 'drop channel',
                value: 'set-drop-channel',
                description: 'Set channel for item drop notifications',
                emoji: '📍',
                default: selectedNotificationOption === 'set-drop-channel'
            }
        );
    }

    if (pageItems.length > 0) {
        pageItems.forEach(item => {
            const option = {
                label: item.name,
                value: item.id,
                description: `${ITEM_TYPES[item.type]?.name || 'Unknown'} - ${RARITIES[item.rarity]?.name || 'Unknown'}`
            };

            // Only add emoji if it exists and is not empty
            if (item.emote && item.emote.trim() !== '') {
                option.emoji = item.emote;
            }

            itemSelect.addOptions(option);
        });
    }
    
    // Only add the select menu if it has options (notification options or items)
    if (itemSelect.options.length > 0) {
        const itemSelectRow = new ActionRowBuilder().addComponents(itemSelect);
        components.push(itemSelectRow);
    }

    // Add cascading channel select menu if "set-drop-channel" is selected
    if (selectedNotificationOption === 'set-drop-channel' && !isOwner) {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('items-notification-channel-select')
            .setPlaceholder('select channel for drop notifications')
            .setChannelTypes([0, 5]) // Text and announcement channels
            .setDisabled(!hasAccess || !isFeatureEnabled);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        components.push(channelRow);
    }
    
    // Build container
    const container = new ContainerBuilder();
    
    // Add components based on type
    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
            container.addTextDisplayComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        }
    });
    
    container.setAccentColor(OPERATION_COLORS.NEUTRAL);

    // Simple access system:
    // - Users with access: Full interface
    // - Users without access: Demo data only
    // No premium warnings or purchase buttons





    return container;
}

/**
 * Get the effective guild ID based on access context
 * @param {Object} state - Creation state
 * @param {string} fallbackGuildId - Fallback guild ID from interaction
 * @returns {string|null} Effective guild ID (null for bot-wide, guild ID for guild-specific)
 */
function getEffectiveGuildId(state, fallbackGuildId) {
    const accessContext = state?.accessContext || 'guild';
    return accessContext === 'owner' ? null : fallbackGuildId;
}

/**
 * Helper function to get state and build unified container with correct guild IDs
 * @param {string} userId - User ID
 * @param {string} actualGuildId - Actual guild ID from interaction
 * @param {boolean} isOwner - Whether user is bot owner
 * @param {Object} interaction - Discord interaction (for fresh image fetching)
 * @returns {Array} Array of containers
 */
async function buildUnifiedItemContainerWithState(userId, actualGuildId, isOwner, interaction = null) {
    const state = await getCreationState(userId, actualGuildId);
    const effectiveGuildId = getEffectiveGuildId(state, actualGuildId);
    return await buildUnifiedItemContainer(userId, effectiveGuildId, isOwner, actualGuildId, interaction);
}

/**
 * Build the unified item creation container (main container + preview)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (actual guild ID from interaction)
 * @param {boolean} isOwner - Whether user is bot owner
 * @param {string} actualGuildId - Optional: actual guild ID for image caching (defaults to guildId)
 * @param {Object} interaction - Discord interaction (for fresh image fetching)
 * @returns {Array} Array of containers [main, preview]
 */
async function buildUnifiedItemContainer(userId, guildId, isOwner, actualGuildId = null, interaction = null) {
    // Always use actual guild ID for state operations to maintain consistency
    const stateGuildId = actualGuildId || guildId;
    const state = await getCreationState(userId, stateGuildId) || {};

    // Use effective guild ID based on access context for item operations
    const effectiveGuildId = getEffectiveGuildId(state, guildId);

    // Use actual guild ID for image operations (images are always channel-specific)
    const imageGuildId = actualGuildId || guildId;

    // Build main container
    const mainContainer = await buildMainItemContainer(state, isOwner, userId, effectiveGuildId, imageGuildId, interaction);

    // Only build preview container if type is selected
    const containers = [mainContainer];

    if (state.selectedType) {
        const previewContainer = buildItemPreviewContainer({
            selectedType: state.selectedType,
            selectedRarity: state.selectedRarity,
            parameters: state.parameters || {},
            itemName: state.itemName,
            itemEmote: state.itemEmote,
            itemDescription: state.itemDescription,
            isComplete: false
        });
        containers.push(previewContainer);

        // Add create/update button outside preview (no separate container)
        const isReadyToCreate = state.selectedType && state.selectedRarity && state.itemName;
        if (isReadyToCreate) {
            const isEditing = !!state.editingItemId;
            const hasChanges = hasItemBeenModified(state);

            const createButton = new ButtonBuilder()
                .setCustomId('items-create-final')
                .setLabel(isEditing ? 'update item' : 'create item')
                .setStyle(ButtonStyle.Success)
                .setDisabled(isEditing && !hasChanges); // Disable if editing and no changes
            const createRow = new ActionRowBuilder().addComponents(createButton);

            // Add button directly to main container
            mainContainer.addActionRowComponents(createRow);
        }

        // Add management buttons for editing mode
        if (state.editingItemId) {
            // Check if item is currently disabled
            const isItemDisabled = state.originalData?.disabled || false;

            const disableButton = new ButtonBuilder()
                .setCustomId('items-toggle-disable')
                .setLabel(isItemDisabled ? 'enable item' : 'disable item')
                .setStyle(ButtonStyle.Secondary);

            // Delete button with click counter
            const deleteClicks = state.deleteClicks || 0;
            let deleteLabel = 'delete item';
            if (deleteClicks === 1) {
                deleteLabel = 'delete item (press 2x)';
            } else if (deleteClicks === 2) {
                deleteLabel = 'delete item (press 1x)';
            }

            const deleteButton = new ButtonBuilder()
                .setCustomId('items-delete-item')
                .setLabel(deleteLabel)
                .setStyle(ButtonStyle.Danger);

            const managementRow = new ActionRowBuilder().addComponents(disableButton, deleteButton);
            mainContainer.addActionRowComponents(managementRow);
        }
    }

    return containers;
}

/**
 * Build the main item creation container with cascading selects
 * @param {Object} state - Current creation state
 * @param {boolean} isOwner - Whether user is bot owner
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (effective guild ID for item context)
 * @param {string} imageGuildId - Guild ID for image operations (actual guild ID)
 * @param {Object} interaction - Discord interaction (for fresh image fetching)
 * @returns {ContainerBuilder} The main container
 */
async function buildMainItemContainer(state, isOwner, userId, guildId, imageGuildId = null, interaction = null) {
    const components = [];

    // Header with back button (owner only) - dynamic text for editing vs creating
    const isEditing = !!state.editingItemId;
    const headerText = isEditing ? `# edit ${state.itemName || 'item'}` : '# create item';
    const quoteText = isEditing ? `> edit the config of ${state.itemName || 'item'}` : '> configure your custom item';

    const heading = new TextDisplayBuilder().setContent(headerText);

    // Both owners and guild admins get back button for navigation
    const backButton = new ButtonBuilder()
        .setCustomId('items-creation-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);
    components.push(backSection);

    const quote = new TextDisplayBuilder().setContent(quoteText);
    components.push(quote);

    // Show item display if type is selected (this will appear in the list when back is pressed)
    if (state.selectedType) {
        const type = ITEM_TYPES[state.selectedType];

        const displayEmote = state.itemEmote || 'emote';
        const displayName = state.itemName || 'name';
        const typeName = type?.name || 'Unknown';
        const rarityName = state.selectedRarity ? formatRarityWithWeight(state.selectedRarity) : 'rarity';

        // Format drop locations for display with scope context
        let dropLocationsText = 'none';
        if (state.dropLocations && state.dropLocations.length > 0) {
            // Determine context from the function parameters
            const isGlobalContext = !guildId; // Global context when no guildId
            const scope = isGlobalContext ? 'global' : 'server';
            const locationNames = state.dropLocations.map(loc => {
                const location = DROP_LOCATIONS[loc];
                return location ? location.displayName : loc.toLowerCase();
            }).join(', ');
            dropLocationsText = `${scope}, ${locationNames}`;
        }

        // Build item display - hide rarity column if NONE rarity (empty rarityName)
        let itemDisplay;
        if (rarityName) {
            itemDisplay = `${displayEmote} **${displayName}** | ${typeName} | ${rarityName} | ${dropLocationsText}`;
        } else {
            // NONE rarity: hide rarity column entirely
            itemDisplay = `${displayEmote} **${displayName}** | ${typeName} | ${dropLocationsText}`;
        }

        // Add strikethrough if item is disabled (when editing)
        if (state.editingItemId && state.originalData?.disabled) {
            itemDisplay = `~~${itemDisplay}~~`;
        }

        const itemText = new TextDisplayBuilder().setContent(itemDisplay);
        components.push(itemText);
    }

    // Main type selection menu (always shows all types)
    if (!state.selectedType) {
        const typeSelect = new StringSelectMenuBuilder()
            .setCustomId('items-type-select')
            .setPlaceholder('select item type');

        Object.entries(ITEM_TYPES).forEach(([key, type]) => {
            typeSelect.addOptions({
                label: type.name,
                value: key,
                description: type.description,
                emoji: type.emote
            });
        });

        const typeRow = new ActionRowBuilder().addComponents(typeSelect);
        components.push(typeRow);
    } else {
        // Show type config options after type is selected
        const configOptions = [];

        // Rarity selection
        const rarityOption = {
            label: 'rarity',
            value: 'rarity',
            description: state.selectedRarity ? RARITIES[state.selectedRarity]?.name : 'Choose item rarity',
            default: state.currentConfig === 'rarity'
        };

        // Only add emoji if rarity is selected and has a non-empty emote
        if (state.selectedRarity && RARITIES[state.selectedRarity]?.emote && RARITIES[state.selectedRarity].emote.trim() !== '') {
            rarityOption.emoji = RARITIES[state.selectedRarity].emote;
        } else {
            rarityOption.emoji = '⭐'; // Default emoji
        }

        configOptions.push(rarityOption);

        // Name and emote
        configOptions.push({
            label: 'name',
            value: 'name',
            description: state.itemName || 'Set item name',
            emoji: '📝',
            default: state.currentConfig === 'name'
        });

        // Extract clean emote name for display
        let emoteDescription = 'Set item emote';
        if (state.itemEmote) {
            if (state.itemEmote.includes(':')) {
                // Custom emote format: <:name:id> or <a:name:id> - extract just the name
                const emoteMatch = state.itemEmote.match(/<a?:([^:]+):\d+>/);
                emoteDescription = emoteMatch ? emoteMatch[1] : state.itemEmote;
            } else {
                // Regular emoji
                emoteDescription = state.itemEmote;
            }
        }

        configOptions.push({
            label: 'emote',
            value: 'emote',
            description: emoteDescription,
            emoji: '😀',
            default: state.currentConfig === 'emote'
        });

        configOptions.push({
            label: 'description',
            value: 'description',
            description: state.itemDescription || 'Set item description',
            emoji: '📝',
            default: state.currentConfig === 'description'
        });

        configOptions.push({
            label: 'drop locations',
            value: 'drop-locations',
            description: state.dropLocations?.length ? `${state.dropLocations.length} location(s) selected` : 'Set where item can drop',
            emoji: '📍',
            default: state.currentConfig === 'drop-locations'
        });

        // Type-specific parameters
        const typeInfo = ITEM_TYPES[state.selectedType];
        if (typeInfo?.parameters) {
            typeInfo.parameters.forEach(param => {
                configOptions.push({
                    label: param,
                    value: `param-${param}`,
                    description: state.parameters?.[param] || `Set ${param}`,
                    emoji: getParameterEmote(param),
                    default: state.currentConfig === `param-${param}`
                });
            });
        }

        const configSelect = new StringSelectMenuBuilder()
            .setCustomId('items-config-select')
            .setPlaceholder('configure item')
            .addOptions(configOptions);

        const configRow = new ActionRowBuilder().addComponents(configSelect);
        components.push(configRow);

        // Show cascading select menu based on current config
        if (state.currentConfig === 'rarity') {
            const raritySelect = new StringSelectMenuBuilder()
                .setCustomId('items-rarity-select')
                .setPlaceholder('select rarity');

            getAllRarities().forEach(rarity => {
                const option = {
                    label: rarity.name,
                    value: rarity.key,
                    description: `Weight: ${rarity.weight}`
                };

                // Only add emoji if it exists and is not empty
                if (rarity.emote && rarity.emote.trim() !== '') {
                    option.emoji = rarity.emote;
                }

                raritySelect.addOptions(option);
            });

            const rarityRow = new ActionRowBuilder().addComponents(raritySelect);
            components.push(rarityRow);

        } else if (state.currentConfig === 'emote') {
            // CRITICAL FIX: Use fresh image fetching when interaction is available, fallback to direct fetch
            let recentImages = [];

            if (interaction) {
                try {
                    const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
                    recentImages = await forceRefreshImageCache(interaction);
                } catch (error) {
                    console.error('[items] Error with forceRefreshImageCache, trying direct fetch:', error);
                    // Fallback to direct image fetching if force refresh fails
                    try {
                        const { getRecentImagesFromChannel } = require('../../utils/imageUploader.js');
                        recentImages = await getRecentImagesFromChannel(interaction, { limit: 8, cacheMinutes: 2 });
                    } catch (fallbackError) {
                        console.error('[items] Error with direct image fetch fallback:', fallbackError);
                        recentImages = [];
                    }
                }
            } else {
                // No interaction available, return empty array (can't fetch without interaction)
                console.warn('[items] No interaction available for image fetching');
                recentImages = [];
            }

            const imageSelectRow = buildImageSelectMenu(
                recentImages,
                'items-image-select',
                'select image for emote'
            );

            if (imageSelectRow) {
                components.push(imageSelectRow);
            } else {
                // Always show the "no images" menu so users can still upload
                components.push(buildNoImagesSelectMenu('items-image-select', 'select image for emote'));
            }

        } else if (state.currentConfig === 'drop-locations') {
            // Show drop locations multi-select menu
            // First, calculate available options to set correct maxValues
            const availableOptions = [];

            // Determine if we're in bot-wide context
            let isBotWideContext = false;
            if (state.editingItemId) {
                // When editing, check if the original item is bot-wide
                isBotWideContext = state.originalData?.guildId === null;
            } else {
                // When creating, check if guildId is null (owner.js context)
                isBotWideContext = guildId === null;
            }

            Object.entries(DROP_LOCATIONS).forEach(([key, location]) => {
                // Skip LEVEL_UP and STARFALL locations if user is not bot owner OR if not in bot-wide context
                if ((key === 'LEVEL_UP' || key === 'STARFALL') && (!isOwner || !isBotWideContext)) {
                    return;
                }

                availableOptions.push({
                    label: location.name,
                    value: key,
                    description: location.description,
                    emoji: location.emote,
                    default: state.dropLocations?.includes(key) || false
                });
            });

            const dropSelect = new StringSelectMenuBuilder()
                .setCustomId('items-drop-locations-select')
                .setPlaceholder('select drop locations')
                .setMinValues(1)
                .setMaxValues(availableOptions.length); // Use actual available options count

            availableOptions.forEach(option => {
                dropSelect.addOptions(option);
            });

            const dropRow = new ActionRowBuilder().addComponents(dropSelect);
            components.push(dropRow);

        } else if (state.currentConfig && state.currentConfig.startsWith('param-')) {
            const paramName = state.currentConfig.replace('param-', '');

            // Check if this parameter should use a select menu
            if (shouldUseSelectMenu(paramName)) {
                const paramOptions = getParameterOptions(paramName);

                const paramSelect = new StringSelectMenuBuilder()
                    .setCustomId(`items-param-select-${paramName}`)
                    .setPlaceholder(`select ${paramName}`);

                paramOptions.forEach(option => {
                    paramSelect.addOptions({
                        label: option.label,
                        value: option.value,
                        description: option.description,
                        emoji: option.emoji
                    });
                });

                const paramRow = new ActionRowBuilder().addComponents(paramSelect);
                components.push(paramRow);
            }
        }
    }

    // Build container with color coding
    const container = new ContainerBuilder();

    // Set color based on operation type
    if (state.editingItemId) {
        container.setAccentColor(OPERATION_COLORS.EDIT); // Orange for editing
    } else {
        container.setAccentColor(OPERATION_COLORS.ADD); // Green for adding
    }

    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
            container.addTextDisplayComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        }
    });



    return container;
}

/**
 * Get step indicator text
 * @param {string} currentStep - Current step
 * @returns {string} Step indicator
 */
function getStepIndicator(currentStep) {
    const steps = {
        type: '**1.** type → 2. rarity → 3. parameters → 4. preview',
        rarity: '1. type → **2.** rarity → 3. parameters → 4. preview',
        parameters: '1. type → 2. rarity → **3.** parameters → 4. preview',
        preview: '1. type → 2. rarity → 3. parameters → **4.** preview'
    };

    return steps[currentStep] || steps.type;
}

/**
 * Build unified item display container for all contexts (DM, notification center, spam channel, inventory)
 * @param {Object} itemData - Item data
 * @param {Object} catchData - Catch-specific data (weight, length, etc.)
 * @param {Object} context - Context data (user, server, location, timestamp)
 * @param {Object} leaderboardResults - Leaderboard results
 * @param {Object} options - Display options
 * @param {boolean} options.showLiveTotals - Whether to show live totals (for inventory) or static (for DM/notifications)
 * @param {boolean} options.showServerContext - Whether to show server/location context (false for spam channel)
 * @returns {ContainerBuilder} The unified item display container
 */
async function buildUnifiedItemDisplayContainer(itemData, catchData = {}, context = {}, leaderboardResults = null, options = {}) {
    const {
        showLiveTotals = false,
        showServerContext = true
    } = options;

    const components = [];

    // Get item type and rarity info
    const type = ITEM_TYPES[itemData.itemType || itemData.type];
    const rarity = RARITIES[itemData.rarity];

    // Header with thumbnail - exactly like creation preview
    const displayName = itemData.itemName || itemData.name || type?.name || 'Unknown Item';
    const headerText = `# ${displayName}`;

    // Extract emote URL if it's a custom emote
    let thumbnailUrl = null;
    if (itemData.emote && itemData.emote.includes(':')) {
        // Custom emote format: <:name:id> or <a:name:id>
        const emoteMatch = itemData.emote.match(/<a?:([^:]+):(\d+)>/);
        if (emoteMatch) {
            const [, emoteName, emoteId] = emoteMatch;
            const isAnimated = itemData.emote.startsWith('<a:');
            thumbnailUrl = `https://cdn.discordapp.com/emojis/${emoteId}.${isAnimated ? 'gif' : 'png'}`;
        }
    }

    // Quote/description - exactly like creation preview
    let quoteText = '';
    if (itemData.description || type?.description) {
        quoteText = `> ${itemData.description || type?.description}`;
    }

    // Create header section with optional thumbnail - EXACTLY like creation preview
    if (thumbnailUrl) {
        // Use section with thumbnail accessory when we have an emote
        const thumbnailComponent = new ThumbnailBuilder({
            media: { url: thumbnailUrl }
        });

        const heading = new TextDisplayBuilder().setContent(headerText);
        const quote = new TextDisplayBuilder().setContent(quoteText);
        const headerSection = new SectionBuilder()
            .setThumbnailAccessory(thumbnailComponent)
            .addTextDisplayComponents(heading, quote);
        components.push(headerSection);
    } else {
        // Use regular text display when no emote
        const heading = new TextDisplayBuilder().setContent(headerText);
        if (quoteText) {
            const quote = new TextDisplayBuilder().setContent(quoteText);
            components.push(heading, quote);
        } else {
            components.push(heading);
        }
    }

    // Info section - includes parameters, rarity, timestamp, and place
    let parametersText = '**info:**\n';

    // CRITICAL FIX: Helper function for ordinal suffix (works for all numbers)
    const getOrdinalSuffix = (num) => {
        const j = num % 10;
        const k = num % 100;
        if (j === 1 && k !== 11) return 'st';
        if (j === 2 && k !== 12) return 'nd';
        if (j === 3 && k !== 13) return 'rd';
        return 'th';
    };

    // Helper function to add record detection to parameter
    const addParameterWithRecords = async (paramName, paramValue) => {
        let recordText = '';

        // Check for records if leaderboard results are available
        if (leaderboardResults) {
            const guildRecord = leaderboardResults.guildRecords?.find(r => r.parameter === paramName);
            const globalRecord = leaderboardResults.globalRecords?.find(r => r.parameter === paramName);
            const guildRank = leaderboardResults.guildRanks?.[paramName];
            const globalRank = leaderboardResults.globalRanks?.[paramName];

            // Check if this is a guild-specific item (guildId exists) or bot-wide item (guildId is null)
            const isGuildSpecificItem = itemData.guildId !== null && itemData.guildId !== undefined;

            if (isGuildSpecificItem) {
                // Guild-specific item: only show server rankings
                if (guildRecord) {
                    recordText = ' (🏆 server record!)';
                } else if (guildRank) {
                    if (showLiveTotals) {
                        // Query live total for guild rankings
                        const liveGuildTotal = await getLiveParameterTotal(itemData.itemName || itemData.name, itemData.itemType || itemData.type, paramName, context.guildId);
                        recordText = ` (${guildRank.rank}${getOrdinalSuffix(guildRank.rank)}/${liveGuildTotal} server)`;
                    } else {
                        recordText = ` (${guildRank.rank}${getOrdinalSuffix(guildRank.rank)}/${guildRank.total} server)`;
                    }
                }
            } else {
                // Bot-wide item: show both server and global rankings
                if (guildRecord && globalRecord) {
                    recordText = ' (🏆 NEW RECORD!)';
                } else if (guildRecord) {
                    if (showLiveTotals) {
                        const liveGlobalTotal = await getLiveParameterTotal(itemData.itemName || itemData.name, itemData.itemType || itemData.type, paramName, null);
                        recordText = ` (🏆 server record, ${globalRank?.rank || '?'}${getOrdinalSuffix(globalRank?.rank || 1)}/${liveGlobalTotal} global)`;
                    } else {
                        recordText = ` (🏆 server record, ${globalRank?.rank || '?'}${getOrdinalSuffix(globalRank?.rank || 1)}/${globalRank?.total || '?'} global)`;
                    }
                } else if (globalRecord) {
                    if (showLiveTotals) {
                        const liveGuildTotal = await getLiveParameterTotal(itemData.itemName || itemData.name, itemData.itemType || itemData.type, paramName, context.guildId);
                        recordText = ` (${guildRank?.rank || '?'}${getOrdinalSuffix(guildRank?.rank || 1)}/${liveGuildTotal} server, 🏆 global record)`;
                    } else {
                        recordText = ` (${guildRank?.rank || '?'}${getOrdinalSuffix(guildRank?.rank || 1)}/${guildRank?.total || '?'} server, 🏆 global record)`;
                    }
                } else if (guildRank && globalRank) {
                    if (showLiveTotals) {
                        const liveGuildTotal = await getLiveParameterTotal(itemData.itemName || itemData.name, itemData.itemType || itemData.type, paramName, context.guildId);
                        const liveGlobalTotal = await getLiveParameterTotal(itemData.itemName || itemData.name, itemData.itemType || itemData.type, paramName, null);
                        recordText = ` (${guildRank.rank}${getOrdinalSuffix(guildRank.rank)}/${liveGuildTotal} server, ${globalRank.rank}${getOrdinalSuffix(globalRank.rank)}/${liveGlobalTotal} global)`;
                    } else {
                        recordText = ` (${guildRank.rank}${getOrdinalSuffix(guildRank.rank)}/${guildRank.total} server, ${globalRank.rank}${getOrdinalSuffix(globalRank.rank)}/${globalRank.total} global)`;
                    }
                }
            }
        }

        // Check for personal records (if we have context with userId)
        if (context.userId && (itemData.itemName || itemData.name) && (itemData.itemType || itemData.type)) {
            try {
                // First check if we have personal best data passed in leaderboardResults
                if (leaderboardResults?.personalBests && leaderboardResults.personalBests.hasOwnProperty(paramName)) {
                    // We have personal best data for this parameter, use it
                    if (leaderboardResults.personalBests[paramName]) {
                        if (recordText) {
                            recordText += ` 🌟 personal best!`;
                        } else {
                            recordText = ` (🌟 personal best!)`;
                        }
                    }
                } else {
                    // Fallback to the original personal record checking only when we don't have personal best data
                    const personalRecord = await checkPersonalRecord(context.userId, itemData.itemName || itemData.name, itemData.itemType || itemData.type, paramName, paramValue);
                    if (personalRecord.isPersonalRecord) {
                        if (recordText) {
                            recordText += ` 🌟 personal record!`;
                        } else {
                            recordText = ` (🌟 personal record!)`;
                        }
                    }
                }
            } catch (error) {
                console.error('[items] Error checking personal record:', error);
            }
        }

        parametersText += `${paramName}: ${paramValue}${recordText}\n`;
    };

    // Add catch-specific parameters with record detection
    if (catchData.weight) {
        await addParameterWithRecords('weight', catchData.weight);
    }
    if (catchData.length) {
        await addParameterWithRecords('length', catchData.length);
    }
    if (catchData.purity) {
        await addParameterWithRecords('purity', catchData.purity);
    }
    if (catchData.condition) {
        await addParameterWithRecords('condition', catchData.condition);
    }

    // Add other type-specific parameters with record detection
    if (type?.parameters) {
        for (const param of type.parameters) {
            if (catchData[param] && !['weight', 'length', 'purity', 'condition'].includes(param)) {
                await addParameterWithRecords(param, catchData[param]);
            }
        }
    }

    // Add rarity with weight (hide for NONE rarity)
    if (rarity && !rarity.hideDisplay) {
        // Only include emote if it exists and is not empty
        const emotePrefix = (rarity.emote && rarity.emote.trim() !== '') ? `${rarity.emote} ` : '';
        parametersText += `rarity: ${emotePrefix}${rarity.name} (${rarity.weight})\n`;
    }

    // Timestamp and discovery info will be moved to context section below

    const parameters = new TextDisplayBuilder().setContent(parametersText.trim());
    components.push(parameters);

    // Add context information (server, location, found, discovered) with separator
    // Show context if we have any relevant info OR if we're not hiding server context
    const hasContextInfo = context.server || context.location || context.timestamp || leaderboardResults;
    if (hasContextInfo) {
        // Add separator before context info
        const { SeparatorBuilder, SeparatorSpacingSize } = require('discord.js');
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false);
        components.push(separator);

        let contextText = '';

        // Add server and location info (server only shown when showServerContext is true)
        if (showServerContext && context.server) {
            contextText += `server: ${context.server}\n`;
        }
        if (context.location) {
            // Determine if this is a global or server item based on guildId
            const isGlobalItem = !itemData.guildId;
            const scope = isGlobalItem ? 'global' : 'server';
            contextText += `location: ${scope}, ${context.location}\n`;
        }

        // Combine timestamp and discovery ranking into a single "found" line
        if (context.timestamp || (leaderboardResults && (leaderboardResults.guildRanks || leaderboardResults.globalRanks))) {
            let foundText = 'found: ';

            // Add timestamp if provided
            if (context.timestamp) {
                const timestamp = Math.floor(new Date(context.timestamp).getTime() / 1000);
                foundText += `<t:${timestamp}:R>`;
            }

            // Add discovery ranking information
            if (leaderboardResults && (leaderboardResults.guildRanks || leaderboardResults.globalRanks)) {
                // Always use '_item_discovery' for place display (item discovery order)
                const discoveryParam = '_item_discovery';
                const guildRank = leaderboardResults.guildRanks?.[discoveryParam];
                const globalRank = leaderboardResults.globalRanks?.[discoveryParam];

                // Check if this is a guild-specific item (guildId exists) or bot-wide item (guildId is null)
                const isGuildSpecificItem = itemData.guildId !== null && itemData.guildId !== undefined;

                // FIXED: Only add comma separator if we have timestamp AND actual ranking data to display
                const hasRankingData = (isGuildSpecificItem && guildRank?.discoveryRank) ||
                                     (!isGuildSpecificItem && (guildRank?.discoveryRank || globalRank?.discoveryRank));

                if (context.timestamp && hasRankingData) {
                    foundText += ', ';
                }

                // Use discoveryRank for place display (chronological order of finding)
                if (isGuildSpecificItem) {
                    // Guild-specific item: only show server ranking
                    if (guildRank?.discoveryRank) {
                        if (showLiveTotals) {
                            // FIXED: Use totals from live discovery results if available, otherwise query
                            if (guildRank.total) {
                                // Use the totals from the passed live discovery results
                                console.log(`[items] 🔍 DEBUG: Using live guild discovery totals from leaderboard results: ${guildRank.total} server`);
                                foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)}/${guildRank.total} server`;
                            } else {
                                // Fallback to old query method
                                const itemName = itemData.itemName || itemData.name;
                                const itemType = itemData.itemType || itemData.type;
                                console.log(`[items] 🔍 Guild discovery query params: itemName="${itemName}", itemType="${itemType}", guildId="${context.guildId}"`);
                                const liveGuildTotal = await getLiveDiscoveryTotal(itemName, itemType, context.guildId, guildRank.total || 1);
                                validateServerCountConsistency(guildRank.discoveryRank, liveGuildTotal, `Guild-specific item ${itemData.itemName || itemData.name}`);
                                foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)}/${liveGuildTotal} server`;
                            }
                        } else {
                            // ENHANCED: Static views (DMs/server notifications): Show rank with ordinal suffix but no total
                            validateServerCountConsistency(guildRank.discoveryRank, guildRank.total, `Guild-specific item ${itemData.itemName || itemData.name} (cached)`);
                            foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)} server`;
                        }
                    }
                } else {
                    // Bot-wide item: show both server and global rankings
                    if (guildRank?.discoveryRank && globalRank?.discoveryRank) {
                        if (showLiveTotals) {
                            // FIXED: Use totals from live discovery results if available, otherwise query
                            if (guildRank.total && globalRank.total) {
                                // Use the totals from the passed live discovery results
                                console.log(`[items] 🔍 DEBUG: Using live discovery totals from leaderboard results: ${guildRank.total} server, ${globalRank.total} global`);
                                foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)}/${guildRank.total} server, ${globalRank.discoveryRank}${getOrdinalSuffix(globalRank.discoveryRank)}/${globalRank.total} global`;
                            } else {
                                // Fallback to old query method
                                const itemName = itemData.itemName || itemData.name;
                                const itemType = itemData.itemType || itemData.type;
                                console.log(`[items] 🔍 Combined discovery query params: itemName="${itemName}", itemType="${itemType}", guildId="${context.guildId}"`);
                                const liveGuildTotal = await getLiveDiscoveryTotal(itemName, itemType, context.guildId, guildRank.total || 1);
                                const liveGlobalTotal = await getLiveDiscoveryTotal(itemName, itemType, null, globalRank.total || 1);
                                validateServerCountConsistency(guildRank.discoveryRank, liveGuildTotal, `Bot-wide item ${itemData.itemName || itemData.name} (guild)`);
                                validateServerCountConsistency(globalRank.discoveryRank, liveGlobalTotal, `Bot-wide item ${itemData.itemName || itemData.name} (global)`);
                                foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)}/${liveGuildTotal} server, ${globalRank.discoveryRank}${getOrdinalSuffix(globalRank.discoveryRank)}/${liveGlobalTotal} global`;
                            }
                        } else {
                            // ENHANCED: Static views (DMs/server notifications): Show ranks with ordinal suffixes but no totals
                            validateServerCountConsistency(guildRank.discoveryRank, guildRank.total, `Bot-wide item ${itemData.itemName || itemData.name} (guild cached)`);
                            validateServerCountConsistency(globalRank.discoveryRank, globalRank.total, `Bot-wide item ${itemData.itemName || itemData.name} (global cached)`);
                            foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)} server, ${globalRank.discoveryRank}${getOrdinalSuffix(globalRank.discoveryRank)} global`;
                        }
                    } else if (guildRank?.discoveryRank) {
                        if (showLiveTotals) {
                            // Dynamic views (inventory/notification center): Show full ratio format
                            const itemName = itemData.itemName || itemData.name;
                            const itemType = itemData.itemType || itemData.type;
                            console.log(`[items] 🔍 Guild-only discovery query params: itemName="${itemName}", itemType="${itemType}", guildId="${context.guildId}"`);
                            const liveGuildTotal = await getLiveDiscoveryTotal(itemName, itemType, context.guildId, guildRank.total || 1);
                            validateServerCountConsistency(guildRank.discoveryRank, liveGuildTotal, `Bot-wide item ${itemData.itemName || itemData.name} (guild only)`);
                            foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)}/${liveGuildTotal} server`;
                        } else {
                            // ENHANCED: Static views (DMs/server notifications): Show rank with ordinal suffix but no total
                            validateServerCountConsistency(guildRank.discoveryRank, guildRank.total, `Bot-wide item ${itemData.itemName || itemData.name} (guild only cached)`);
                            foundText += `${guildRank.discoveryRank}${getOrdinalSuffix(guildRank.discoveryRank)} server`;
                        }
                    } else if (globalRank?.discoveryRank) {
                        if (showLiveTotals) {
                            // Dynamic views (inventory/notification center): Show full ratio format
                            const itemName = itemData.itemName || itemData.name;
                            const itemType = itemData.itemType || itemData.type;
                            console.log(`[items] 🔍 Global-only discovery query params: itemName="${itemName}", itemType="${itemType}"`);
                            const liveGlobalTotal = await getLiveDiscoveryTotal(itemName, itemType, null, globalRank.total || 1);
                            validateServerCountConsistency(globalRank.discoveryRank, liveGlobalTotal, `Bot-wide item ${itemData.itemName || itemData.name} (global only)`);
                            foundText += `${globalRank.discoveryRank}${getOrdinalSuffix(globalRank.discoveryRank)}/${liveGlobalTotal} global`;
                        } else {
                            // ENHANCED: Static views (DMs/server notifications): Show rank with ordinal suffix but no total
                            validateServerCountConsistency(globalRank.discoveryRank, globalRank.total, `Bot-wide item ${itemData.itemName || itemData.name} (global only cached)`);
                            foundText += `${globalRank.discoveryRank}${getOrdinalSuffix(globalRank.discoveryRank)} global`;
                        }
                    }
                }
            }

            contextText += `${foundText}\n`;
        }

        const contextDisplay = new TextDisplayBuilder().setContent(contextText.trim());
        components.push(contextDisplay);
    }

    // Build container - EXACTLY like creation preview
    const container = new ContainerBuilder();

    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
            container.addTextDisplayComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        }
    });

    // Use rarity color if available, otherwise default - EXACTLY like creation preview
    const accentColor = rarity?.color || OPERATION_COLORS.NEUTRAL;
    container.setAccentColor(accentColor);

    return container;
}

/**
 * Build item display container for found items (DMs, server notifications)
 * OPTION 4: Show discovery ranks when provided in context
 * @param {Object} itemData - Item data
 * @param {Object} catchData - Catch-specific data (weight, length, etc.)
 * @param {Object} context - Context data (user, server, location, discoveryRanks)
 * @returns {ContainerBuilder} The item display container
 */
async function buildFoundItemContainer(itemData, catchData = {}, context = {}, leaderboardResults = null) {
    // OPTION 4: If context has discovery ranks, create leaderboard results
    let enhancedLeaderboardResults = leaderboardResults;

    // FIXED: Check if leaderboard results are empty, not just null
    const hasUsableLeaderboardResults = leaderboardResults &&
        (Object.keys(leaderboardResults.guildRanks || {}).length > 0 ||
         Object.keys(leaderboardResults.globalRanks || {}).length > 0);

    if (context.discoveryRanks && !hasUsableLeaderboardResults) {
        console.log(`[items] 🔍 DEBUG: Creating enhanced leaderboard results from context discovery ranks:`, context.discoveryRanks);

        enhancedLeaderboardResults = {
            guildRecords: [],
            globalRecords: [],
            guildRanks: {
                '_item_discovery': {
                    rank: context.discoveryRanks.guildRank,
                    total: context.discoveryRanks.guildTotal,
                    discoveryRank: context.discoveryRanks.guildRank
                }
            },
            globalRanks: {
                '_item_discovery': {
                    rank: context.discoveryRanks.globalRank,
                    total: context.discoveryRanks.globalTotal,
                    discoveryRank: context.discoveryRanks.globalRank
                }
            }
        };

        console.log(`[items] 🔍 DEBUG: Enhanced leaderboard results created:`, {
            hasGuildRanks: !!enhancedLeaderboardResults.guildRanks,
            hasGlobalRanks: !!enhancedLeaderboardResults.globalRanks,
            guildDiscoveryRank: enhancedLeaderboardResults.guildRanks._item_discovery.discoveryRank,
            globalDiscoveryRank: enhancedLeaderboardResults.globalRanks._item_discovery.discoveryRank
        });
    }

    return await buildUnifiedItemDisplayContainer(itemData, catchData, context, enhancedLeaderboardResults, {
        showLiveTotals: false,
        showServerContext: true
    });
}

/**
 * Build item display container for dynamic inventory views (shows live totals)
 * @param {Object} itemData - Item data
 * @param {Object} catchData - Catch-specific data (weight, length, etc.)
 * @param {Object} context - Context data (user, server, location)
 * @param {Object} leaderboardResults - Leaderboard results
 * @returns {ContainerBuilder} The item container with live rankings
 */
async function buildDynamicFoundItemContainer(itemData, catchData = {}, context = {}, leaderboardResults = null) {
    console.log(`[items] 🔍 DEBUG: buildDynamicFoundItemContainer called with:`, {
        itemName: itemData.itemName || itemData.name,
        contextGuildId: context.guildId,
        contextTimestamp: context.timestamp,
        hasLeaderboardResults: !!leaderboardResults
    });

    // NUCLEAR: Always calculate live discovery ranks for inventory
    let liveLeaderboardResults = null;

    // FIXED: Use correct property name (name vs itemName) and work with both guild and global items
    const itemName = itemData.itemName || itemData.name;
    if (itemName && (context.guildId || context.timestamp)) {
        try {
            const { calculateSimpleDiscoveryRank } = require('../../utils/discoveryRanks.js');
            // FIXED: For global items, use the guild where they were found
            const foundInGuild = context.guildId || (itemData.foundInGuild || itemData.guildId);

            const mockItem = {
                itemName: itemName,
                itemType: itemData.itemType || itemData.type,
                foundInGuild: foundInGuild,
                droppedAt: context.timestamp || new Date()
            };

            console.log(`[items] 🔍 DEBUG: Building inventory display for:`, {
                itemName: itemName,
                contextGuildId: context.guildId,
                foundInGuild: foundInGuild,
                timestamp: context.timestamp
            });

            const liveRanks = await calculateSimpleDiscoveryRank(mockItem, foundInGuild);

            console.log(`[items] 🔍 DEBUG: Calculated live discovery ranks for inventory:`, {
                itemName: itemData.itemName,
                liveRanks: liveRanks
            });

            liveLeaderboardResults = {
                guildRecords: [],
                globalRecords: [],
                guildRanks: {
                    '_item_discovery': {
                        rank: liveRanks.guildRank,
                        total: liveRanks.guildTotal,
                        discoveryRank: liveRanks.guildRank
                    }
                },
                globalRanks: {
                    '_item_discovery': {
                        rank: liveRanks.globalRank,
                        total: liveRanks.globalTotal,
                        discoveryRank: liveRanks.globalRank
                    }
                }
            };
        } catch (error) {
            console.error('[items] ❌ Error calculating live discovery ranks:', error);
        }
    }

    // FIXED: Always use live results for inventory, ignore old records system
    const finalLeaderboardResults = liveLeaderboardResults || leaderboardResults;

    console.log(`[items] 🔍 DEBUG: Using leaderboard results:`, {
        hasLiveResults: !!liveLeaderboardResults,
        hasOriginalResults: !!leaderboardResults,
        usingResults: !!finalLeaderboardResults
    });

    return await buildUnifiedItemDisplayContainer(itemData, catchData, context, finalLeaderboardResults, {
        showLiveTotals: true,
        showServerContext: true
    });
}

/**
 * Build item display container for server notifications (includes "found by" info)
 * @param {Object} itemData - Item data
 * @param {Object} catchData - Catch-specific data (weight, length, etc.)
 * @param {Object} context - Context data (user, server, location)
 * @returns {Array} Array with text component and item container
 */
async function buildServerFoundItemContainer(itemData, catchData = {}, context = {}, leaderboardResults = null) {
    // Create modified context for server notifications (location only, no server name)
    const serverContext = {
        ...context,
        server: null  // Remove server name since we're already in the server
    };

    // Use unified container for server notifications (show location but not server name)
    const container = await buildUnifiedItemDisplayContainer(itemData, catchData, serverContext, leaderboardResults, {
        showLiveTotals: false,
        showServerContext: true  // Show location context (text chat vs voice chat)
    });

    // Create context text component with user mention, item name, and location
    const userName = context.user || 'Someone';
    const itemName = itemData.itemName || itemData.name || 'item';
    const locationName = context.location || 'unknown location';

    // Determine correct article (a vs an) based on first letter
    const article = getArticle(itemName);
    const contextText = new TextDisplayBuilder().setContent(`${userName} found ${article} **${itemName}** in ${locationName}:`);

    // Return both text component and item container
    return [contextText, container];
}

/**
 * Build the live preview container that updates as user configures item
 * @param {Object} options - Preview options
 * @param {string} options.selectedType - Selected item type
 * @param {string} options.selectedRarity - Selected rarity
 * @param {Object} options.parameters - Selected parameters
 * @param {string} options.itemName - Custom item name
 * @param {string} options.itemEmote - Custom item emote
 * @param {boolean} options.isComplete - Whether configuration is complete
 * @returns {ContainerBuilder} The preview container
 */
function buildItemPreviewContainer({ selectedType, selectedRarity, parameters = {}, itemName, itemEmote, itemDescription, isComplete = false }) {
    const components = [];

    // Header with thumbnail - use item name if set, otherwise type name
    const type = ITEM_TYPES[selectedType];
    let headerText = '# item preview';
    if (itemName) {
        headerText = `# ${itemName}`;
    } else if (selectedType) {
        headerText = `# ${type?.name || 'Unknown'}`;
    }

    // Extract emote URL if it's a custom emote
    let thumbnailUrl = null;
    if (itemEmote && itemEmote.includes(':')) {
        // Custom emote format: <:name:id> or <a:name:id>
        const emoteMatch = itemEmote.match(/<a?:([^:]+):(\d+)>/);
        if (emoteMatch) {
            const [, emoteName, emoteId] = emoteMatch;
            const isAnimated = itemEmote.startsWith('<a:');
            thumbnailUrl = `https://cdn.discordapp.com/emojis/${emoteId}.${isAnimated ? 'gif' : 'png'}`;
        }
    }

    // Item preview
    const rarity = RARITIES[selectedRarity];

    // Quote - use item description if set, otherwise type description
    let quoteText = '> your item will look like this';
    if (itemDescription) {
        quoteText = `> ${itemDescription}`;
    } else if (selectedType) {
        quoteText = `> ${type?.description || 'custom item'}`;
    }

    // Create header section with optional thumbnail
    if (thumbnailUrl) {
        // Use section with thumbnail accessory when we have an emote
        const thumbnailComponent = new ThumbnailBuilder({
            media: { url: thumbnailUrl }
        });

        const heading = new TextDisplayBuilder().setContent(headerText);
        const quote = new TextDisplayBuilder().setContent(quoteText);
        const headerSection = new SectionBuilder()
            .setThumbnailAccessory(thumbnailComponent)
            .addTextDisplayComponents(heading, quote);
        components.push(headerSection);
    } else {
        // Use regular text display when no emote
        const heading = new TextDisplayBuilder().setContent(headerText);
        const quote = new TextDisplayBuilder().setContent(quoteText);
        components.push(heading, quote);
    }

    if (selectedType) {
        // Add parameter preview if available
        if (selectedType && type?.parameters && type.parameters.length > 0) {
            let paramText = '**parameters:**\n';
            type.parameters.forEach(param => {
                const value = parameters[param] || generateSampleParameter(param);
                paramText += `${param}: ${value}\n`;
            });
            const paramDisplay = new TextDisplayBuilder().setContent(paramText);
            components.push(paramDisplay);
        }
        // For "OTHER" type with no parameters, show nothing - keep it super sleek
    } else {
        const placeholderText = new TextDisplayBuilder().setContent('*Select a type to see preview...*');
        components.push(placeholderText);
    }

    // No create button in preview container anymore

    // Build container
    const container = new ContainerBuilder();

    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
            container.addTextDisplayComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        }
    });

    // Use rarity color if available, otherwise default
    const accentColor = rarity?.color || OPERATION_COLORS.NEUTRAL;
    container.setAccentColor(accentColor);

    return container;
}

/**
 * Generate a sample item name based on type
 * @param {string} type - Item type
 * @returns {string} Sample name
 */
function generateSampleItemName(type) {
    const sampleNames = {
        JUNK: 'Rusty Can',
        ANIMAL: 'Wild Rabbit',
        AQUATIC: 'Rainbow Trout',
        INSECT: 'Butterfly',
        TOOL: 'Fishing Net',
        ARTIFACT: 'Ancient Coin',
        TREASURE: 'Gold Nugget',
        HUMANOID: 'Skeleton',
        PLANT: 'Rose',
        OTHER: 'Victory Trophy'
    };

    return sampleNames[type] || 'Mystery Item';
}

/**
 * Generate a sample parameter value with units
 * @param {string} parameter - Parameter name
 * @returns {string} Sample value with units
 */
function generateSampleParameter(parameter) {
    const samples = {
        weight: '?-? lbs',
        height: '?-? feet',
        length: '?-? inches',
        condition: '?',
        usesLeft: '?-?',
        rarity: '?',
        age: '?-? years',
        origin: '?',
        purity: '?%',
        freshness: '?'
    };

    return samples[parameter] || '?';
}

// FIXED: Use enterprise-grade LRU cache instead of basic Map
const liveTotalsCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes TTL
registerCache(liveTotalsCache);

const LIVE_TOTALS_CACHE_TTL = 120000; // 2 minutes cache (matches LRU cache TTL)

/**
 * Get live total count for parameter rankings (for dynamic inventory views)
 * Uses optimized queries with caching and proper indexing
 * FIXED: Improved error handling with proper fallback value
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 * @param {string} parameter - Parameter name
 * @param {string|null} guildId - Guild ID (null for global)
 * @returns {number} Live total count
 */
async function getLiveParameterTotal(itemName, itemType, parameter, guildId) {
    try {
        // Create cache key
        const cacheKey = `param_${itemName}_${itemType}_${parameter}_${guildId || 'global'}`;
        const cached = liveTotalsCache.get(cacheKey);

        // Return cached result if still valid
        if (cached && (Date.now() - cached.timestamp) < LIVE_TOTALS_CACHE_TTL) {
            return cached.count;
        }

        // Use optimized query with proper indexing
        const query = {
            itemName: itemName,
            itemType: itemType,
            [`catchData.${parameter}`]: { $exists: true, $ne: null }
        };

        if (guildId) {
            query.guildId = guildId;
        }

        // OPTIMIZED: Use optimizedCountDocuments for efficient counting (no data transfer)
        const count = await optimizedCountDocuments("user_inventory", query);

        // Cache the result
        liveTotalsCache.set(cacheKey, {
            count: count,
            timestamp: Date.now()
        });

        return count;
    } catch (error) {
        console.error('[items] Error getting live parameter total:', error);
        // FIXED: Return 1 as fallback to avoid division by zero in parameter rankings
        // This is appropriate for parameter totals since they represent leaderboard positions
        return 1;
    }
}

/**
 * Get live total count for discovery rankings (for dynamic inventory views)
 * Uses optimized queries with caching and proper indexing
 * FIXED: Now queries item_records collection to match rank data source and returns proper fallback
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 * @param {string|null} guildId - Guild ID (null for global)
 * @returns {number} Live total count
 */
async function getLiveDiscoveryTotal(itemName, itemType, guildId, fallbackTotal = 1) {
    const startTime = Date.now();

    try {
        // ENHANCED: Comprehensive input validation
        if (!itemName || !itemType) {
            console.error('[items] ❌ Invalid parameters for getLiveDiscoveryTotal:', { itemName, itemType, guildId });
            return fallbackTotal;
        }

        // Create cache key
        const cacheKey = `discovery_${itemName}_${itemType}_${guildId || 'global'}`;
        const cached = liveTotalsCache.get(cacheKey);

        // Return cached result if still valid
        if (cached && (Date.now() - cached.timestamp) < LIVE_TOTALS_CACHE_TTL) {
            console.log(`[items] ⚡ Discovery cache hit for ${cacheKey}: ${cached.count}`);
            return cached.count;
        }

        // FIXED: Query item_records collection to match the data source used for rank calculations
        // This ensures consistency between rank and total count displays
        const scope = guildId ? 'guild' : 'global';
        const query = {
            scope: scope,
            itemName: itemName,
            itemType: itemType,
            parameter: '_item_discovery'
        };

        if (guildId) {
            query.guildId = guildId;
        }

        // ENHANCED: Log query details for debugging
        console.log(`[items] 🔍 Querying item_records for discovery total:`, {
            collection: 'item_records',
            query: query,
            cacheKey: cacheKey
        });

        // OPTIMIZED: Use optimizedCountDocuments for efficient counting (no data transfer)
        const count = await optimizedCountDocuments("item_records", query);

        // ENHANCED: Log successful query result
        const duration = Date.now() - startTime;
        console.log(`[items] ✅ Discovery total query successful: ${count} records found (${duration}ms)`);

        // ENHANCED: Validate result and provide detailed logging
        if (count === 0) {
            console.log(`[items] ⚠️  Zero records found for ${itemName} (${itemType}) - this may cause "1st/0 server" display`);
            console.log(`[items] 🔍 Query details:`, { query, cacheKey });

            // Check if this is a new item that hasn't been discovered yet
            const { optimizedCountDocuments } = require('../../utils/database-optimizer.js');
            const anyRecords = await optimizedCountDocuments("item_records", {
                itemName: itemName,
                itemType: itemType
            });

            if (anyRecords === 0) {
                console.log(`[items] ℹ️  No records exist for this item - likely a new item`);
            } else {
                console.log(`[items] ⚠️  ${anyRecords} total records exist but none match discovery query`);
            }
        }

        // Cache the result
        liveTotalsCache.set(cacheKey, {
            count: count,
            timestamp: Date.now()
        });

        return count;
    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`[items] ❌ Error getting live discovery total (${duration}ms):`, {
            error: error.message,
            stack: error.stack,
            itemName,
            itemType,
            guildId,
            fallbackTotal
        });

        // ENHANCED: Return fallback total with detailed logging
        console.log(`[items] 🔄 Using fallback total: ${fallbackTotal} for ${itemName} (${itemType})`);
        return fallbackTotal;
    }
}

/**
 * Clear live totals cache to fix data inconsistency issues
 * ADDED: Function to clear stale cache data that might cause display bugs
 */
function clearLiveTotalsCache() {
    liveTotalsCache.clear();
    console.log('[items] 🗑️ Cleared live totals cache to fix data inconsistency');
}

/**
 * Validate server count consistency (Enterprise-Grade Data Validation)
 * ADDED: Function to detect and log data inconsistency issues
 * @param {number} rank - User's rank position
 * @param {number} total - Total count
 * @param {string} context - Context for logging
 * @returns {boolean} Whether the data is consistent
 */
function validateServerCountConsistency(rank, total, context = '') {
    if (rank > total && total > 0) {
        console.error(`[items] ❌ Data inconsistency detected: ${context} - Rank ${rank} > Total ${total}`);
        console.error(`[items] 🔧 Clearing live totals cache to fix inconsistency`);
        clearLiveTotalsCache();
        return false;
    }
    return true;
}

// REMOVED: Duplicate function declaration - using the one above with proper logging

/**
 * Get the correct article (a/an) for a word
 * @param {string} word - The word to get article for
 * @returns {string} "a" or "an"
 */
function getArticle(word) {
    if (!word || typeof word !== 'string') return 'a';

    const firstLetter = word.toLowerCase().charAt(0);

    // Use "an" for vowel sounds
    // Note: This is a simple implementation - English has exceptions (like "hour", "university")
    // but it covers most common cases
    const vowels = ['a', 'e', 'i', 'o', 'u'];

    return vowels.includes(firstLetter) ? 'an' : 'a';
}

/**
 * Add units to parameter values
 * @param {string} parameter - Parameter name
 * @param {string} value - Raw value (e.g., "2-5")
 * @returns {string} Value with units (e.g., "2-5 lbs")
 */
function addParameterUnits(parameter, value) {
    const units = {
        weight: 'lbs',
        height: 'feet',
        length: 'inches',
        age: 'years',
        purity: '%'
    };

    const unit = units[parameter];
    if (!unit) return value;

    // Handle special cases
    if (parameter === 'purity') {
        return `${value}%`;
    }

    return `${value} ${unit}`;
}

/**
 * Get placeholder text for parameter modals with unit information
 * @param {string} parameter - Parameter name
 * @returns {string} Placeholder text with unit info
 */
function getParameterPlaceholder(parameter) {
    const placeholders = {
        weight: 'e.g., 2-5 (will be shown as lbs)',
        height: 'e.g., 10-20 (will be shown as feet)',
        length: 'e.g., 6-10 (will be shown as inches)',
        age: 'e.g., 100-500 (will be shown as years)',

        purity: 'e.g., 85-95 (will be shown as %)',
        usesLeft: 'e.g., 5-10'
    };

    return placeholders[parameter] || `Enter ${parameter} value...`;
}

/**
 * Get parameter options for select menus (non-numeric parameters)
 * @param {string} parameter - Parameter name
 * @returns {Array|null} Array of options or null if should use modal
 */
function getParameterOptions(parameter) {
    const parameterOptions = {
        condition: [
            { label: 'Random', value: 'random', description: 'Randomly select condition when found', emoji: '🎲' },
            { label: 'Pristine', value: 'pristine', description: 'Perfect condition, maximum value', emoji: '✨' },
            { label: 'Excellent', value: 'excellent', description: 'Nearly perfect, high value', emoji: '🌟' },
            { label: 'Good', value: 'good', description: 'Minor wear, good value', emoji: '👍' },
            { label: 'Fair', value: 'fair', description: 'Noticeable wear, reduced value', emoji: '👌' },
            { label: 'Poor', value: 'poor', description: 'Heavy wear, low value', emoji: '👎' }
        ],
        origin: [
            { label: 'Random', value: 'random', description: 'Randomly select origin when found', emoji: '🎲' },
            { label: 'Cosmic Void', value: 'cosmic_void', description: 'From the depths of space', emoji: '🌌' },
            { label: 'Ancient Ruins', value: 'ancient_ruins', description: 'Mysterious old structures', emoji: '🏛️' },
            { label: 'Deep Ocean', value: 'deep_ocean', description: 'From oceanic trenches', emoji: '🌊' },
            { label: 'Alien World', value: 'alien_world', description: 'From distant planets', emoji: '👽' },
            { label: 'Time Rift', value: 'time_rift', description: 'From temporal anomalies', emoji: '⏰' }
        ],
        freshness: [
            { label: 'Random', value: 'random', description: 'Randomly select freshness when found', emoji: '🎲' },
            { label: 'Fresh', value: 'fresh', description: 'Recently harvested, peak quality', emoji: '🌿' },
            { label: 'Good', value: 'good', description: 'Still fresh, good quality', emoji: '🍃' },
            { label: 'Fair', value: 'fair', description: 'Starting to age, decent quality', emoji: '🍂' },
            { label: 'Wilted', value: 'wilted', description: 'Past prime, reduced quality', emoji: '🥀' },
            { label: 'Dried', value: 'dried', description: 'Preserved but different properties', emoji: '🍄' }
        ]
    };

    return parameterOptions[parameter] || null;
}

/**
 * Check if parameter should use select menu instead of modal
 * @param {string} parameter - Parameter name
 * @returns {boolean} True if should use select menu
 */
function shouldUseSelectMenu(parameter) {
    return getParameterOptions(parameter) !== null;
}

/**
 * Get recent images from user's messages (cached version for container building)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID for cache lookup (should be actual guild ID, not effective)
 * @returns {Array} Array of recent images
 */
async function getRecentImages(userId, guildId) {
    // This function returns cached images, but if cache is empty (e.g., after invalidation),
    // it indicates that fresh images should be fetched when the user interacts
    try {
        const cached = await optimizedFindOne("user_images_cache", { userId, guildId });

        // If we have cached images, return them
        if (cached?.images?.length > 0) {
            return cached.images;
        }

        // If no cached images, return empty array
        // The select menu will show "no images" but when clicked, it will fetch fresh images
        return [];
    } catch (error) {
        console.error('Error getting cached images:', error);
        return [];
    }
}



/**
 * Check if current item state has been modified from original
 * @param {Object} currentState - Current creation state
 * @returns {boolean} True if item has been modified
 */
function hasItemBeenModified(currentState) {
    if (!currentState.originalData || !currentState.editingItemId) {
        return true; // For new items, always allow creation
    }

    const original = currentState.originalData;
    const current = {
        selectedType: currentState.selectedType,
        selectedRarity: currentState.selectedRarity,
        parameters: currentState.parameters || {},
        itemName: currentState.itemName,
        itemEmote: currentState.itemEmote,
        itemEmoteId: currentState.itemEmoteId,
        itemDescription: currentState.itemDescription,
        dropLocations: currentState.dropLocations || []
    };

    // Deep comparison of all fields
    if (original.selectedType !== current.selectedType) return true;
    if (original.selectedRarity !== current.selectedRarity) return true;
    if (original.itemName !== current.itemName) return true;
    if (original.itemEmote !== current.itemEmote) return true;
    if (original.itemEmoteId !== current.itemEmoteId) return true;
    if (original.itemDescription !== current.itemDescription) return true;

    // Compare drop locations arrays
    const origLocations = original.dropLocations || [];
    const currLocations = current.dropLocations || [];
    if (origLocations.length !== currLocations.length) return true;
    if (!origLocations.every(loc => currLocations.includes(loc))) return true;
    if (!currLocations.every(loc => origLocations.includes(loc))) return true;

    // Compare parameters objects
    const origParams = original.parameters || {};
    const currParams = current.parameters || {};
    const origKeys = Object.keys(origParams);
    const currKeys = Object.keys(currParams);
    if (origKeys.length !== currKeys.length) return true;
    for (const key of origKeys) {
        if (origParams[key] !== currParams[key]) return true;
    }
    for (const key of currKeys) {
        if (origParams[key] !== currParams[key]) return true;
    }

    return false; // No changes detected
}

/**
 * Get emote for parameter type
 * @param {string} parameter - Parameter name
 * @returns {string} Emote
 */
function getParameterEmote(parameter) {
    const emotes = {
        weight: '⚖️',
        height: '📏',
        length: '📐',
        condition: '🔧',
        usesLeft: '🔢',
        rarity: '⭐',
        age: '⏰',
        origin: '🌍',

        purity: '💎',
        freshness: '🌿'
    };

    return emotes[parameter] || '⚙️';
}

/**
 * Get comprehensive cache statistics (Enterprise-Grade Cache Management)
 * OPTIMIZED: Provides detailed cache performance metrics for monitoring
 * @returns {Object} Cache statistics
 */
function getCacheStats() {
    const stats = {
        itemStateCache: itemStateCache.getStats(),
        guildItemConfigCache: guildItemConfigCache.getStats(),
        userInventoryCache: userInventoryCache.getStats(),
        itemParameterCache: itemParameterCache.getStats(),
        personalRecordCache: personalRecordCache.getStats(),
        liveTotalsCache: liveTotalsCache.getStats(),
        totalMemoryUsage: {
            itemState: itemStateCache.getStats().memoryUsage || 0,
            guildConfig: guildItemConfigCache.getStats().memoryUsage || 0,
            userInventory: userInventoryCache.getStats().memoryUsage || 0,
            itemParameter: itemParameterCache.getStats().memoryUsage || 0,
            personalRecord: personalRecordCache.getStats().memoryUsage || 0,
            liveTotals: liveTotalsCache.getStats().memoryUsage || 0
        },
        systemHealth: {
            status: 'healthy',
            lastOptimization: itemsMetrics.lastOptimization,
            uptime: Date.now() - itemsMetrics.lastOptimization
        }
    };

    // Calculate total memory usage
    stats.totalMemoryUsage.total = Object.values(stats.totalMemoryUsage).reduce((sum, usage) => sum + usage, 0);

    return stats;
}

/**
 * Invalidate all items system caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function invalidateAllItemsCaches() {
    itemStateCache.clear();
    guildItemConfigCache.clear();
    userInventoryCache.clear();
    itemParameterCache.clear();
    personalRecordCache.clear();
    liveTotalsCache.clear();
    console.log('[items] 🗑️ Cleared all items system caches');
}

/**
 * Invalidate specific user's item caches
 * @param {string} userId - User ID to invalidate
 */
function invalidateUserItemCaches(userId) {
    // Clear user-specific caches
    const userKeys = [
        `${userId}_guild_*`,
        `${userId}_global`,
        `state_${userId}_*`,
        `records_${userId}_*`
    ];

    // Note: LRU cache doesn't have pattern-based deletion, so we clear relevant caches
    userInventoryCache.delete(`${userId}_global`);
    personalRecordCache.delete(`records_${userId}`);

    console.log(`[items] 🗑️ Invalidated item caches for user ${userId}`);
}

module.exports = {
    RARITIES,
    ITEM_TYPES,
    getRarity,
    getAllRarities,
    generateRandomRarity,
    formatRarity,
    formatRarityWithWeight,
    storeCreationState,
    getCreationState,
    clearCreationState,
    createItem,
    getNextGlobalOrder,
    getNextGuildOrder,
    getItemCounts,
    getItemsForDisplay,
    buildItemsContainer,
    buildUnifiedItemContainer,
    buildMainItemContainer,
    buildItemPreviewContainer,
    generateSampleItemName,
    generateSampleParameter,
    getParameterEmote,

    // Enhanced optimization functions (matching Global Levels)
    invalidateAllItemsCaches,
    invalidateUserItemCaches,
    getCacheStats,
    performanceCleanupAndOptimization,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...itemsMetrics }),
    
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // This should not be called directly as a slash command
            // Build basic items container with status message instead of ephemeral reply
            // OPTIMIZED: Use cached guild configuration
            let guildData = await getCachedGuildItemConfig(interaction.guild.id);
            if (!guildData) guildData = { items: { enabled: true } };
            if (!guildData.items) guildData.items = { enabled: true };

            const containerResult = await buildItemsContainer({
                isOwner: false,
                guildId: interaction.guild.id,
                page: 0,
                client: interaction.client,
                member: interaction.member,
                guildData: guildData,
                statusMessage: 'Items feature is accessible through the /17 command.'
            });

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');

            // Get permission-aware toggle button
            const featureStatus = getFeatureStatus(
                interaction.guild,
                interaction.member,
                'items',
                guildData.items.enabled,
                guildData.items.enabled ? 'items-global-disable' : 'items-global-enable'
            );

            // Add status message to container if there are permission issues
            if (featureStatus.statusMessage && Array.isArray(containerResult)) {
                containerResult[0].addTextDisplayComponents(featureStatus.statusMessage);
            } else if (featureStatus.statusMessage) {
                containerResult.addTextDisplayComponents(featureStatus.statusMessage);
            }

            const globalButtonRow = new ActionRowBuilder().addComponents(featureStatus.button);

            return Array.isArray(containerResult) ? [...containerResult, selectMenu, globalButtonRow] : [containerResult, selectMenu, globalButtonRow];
        }, {
            autoDefer: true, // Auto-defer for execute function as it can take time to build containers
            ephemeral: true,
            fallbackMessage: '❌ Something went wrong loading the items interface. Please try again.'
        });
    },

    async select(interaction, args) {
        return handleUIOperation(interaction, async (interaction) => {
            // Handle item selection menu (both owner and guild contexts)
        if (interaction.customId.startsWith('items-select-')) {
            const accessContext = interaction.customId.split('-')[2]; // 'owner' or 'guild'
            const selectedValue = interaction.values[0];

            if (selectedValue === 'add-new') {
                // Start item creation workflow with context
                return await this.startItemCreation(interaction, accessContext);
            } else if (selectedValue === 'toggle-owner-dm-notifications') {
                // Bot owner DM notification toggle (immediate toggle)
                return await this.handleToggleOwnerDMNotifications(interaction);
            } else if (selectedValue === 'edit-owner-dm-message-template') {
                // Bot owner DM message template editor (show modal)
                return await this.handleEditOwnerDMMessageTemplate(interaction);
            } else if (selectedValue === 'toggle-drop-notifications') {
                // Guild drop notifications toggle (immediate toggle)
                return await this.handleToggleDropNotifications(interaction);
            } else if (selectedValue === 'set-drop-channel') {
                // Show channel select menu (cascading)
                return await this.handleShowChannelSelect(interaction);
            } else {
                // Handle existing item selection (edit/view) with context
                return await this.handleItemSelection(interaction, selectedValue, accessContext);
            }
        } else if (interaction.customId === 'items-config-select') {
            // Handle main config selection
            return await this.handleConfigSelection(interaction);
        } else if (interaction.customId === 'items-type-select') {
            // Handle type selection in creation workflow
            return await this.handleTypeSelection(interaction);
        } else if (interaction.customId === 'items-rarity-select') {
            // Handle rarity selection in creation workflow
            return await this.handleRaritySelection(interaction);
        } else if (interaction.customId.startsWith('items-param-select-')) {
            // Handle parameter select menu selection
            return await this.handleParameterSelection(interaction);
        } else if (interaction.customId === 'items-image-select') {
            // FIXED: Handle image selection for emote with enterprise-grade error handling
            try {
                return await this.handleImageSelection(interaction);
            } catch (error) {
                console.error('[items] ❌ Critical error in image selection handler:', error);
                // Return error container - Universal Interaction Manager will handle the error
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ An error occurred while processing the image selection. Please try again.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }
        } else if (interaction.customId === 'items-drop-locations-select') {
            // Handle drop locations selection
            return await this.handleDropLocationsSelection(interaction);
        }
        }, {
            autoDefer: false, // Don't auto-defer for select menus - they should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your selection. Please try again.'
        });
    },

    async startItemCreation(interaction, accessContext = 'guild') {
        const isOwner = interaction.user.id === process.env.OWNER;
        const hasAccess = isOwner || global.hasFeaturePermission(interaction.member, 'items');

        // Check permissions
        if (!hasAccess) {
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('🔒 Creating custom items requires admin permissions.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            return [errorContainer];
        }

        // Check item limit (5 items max per guild, no limit for bot owner)
        if (!isOwner) {
            try {
                // OPTIMIZED: Use optimizedCountDocuments for efficient counting (no data transfer)
                const itemCount = await optimizedCountDocuments("custom_items", { guildId: interaction.guild.id });

                if (itemCount >= 5) {
                    const errorContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('📦 **Item limit reached!**\n\nYour server has reached the maximum of **5 custom items**. Delete an existing item to create a new one.'))
                        .setAccentColor(OPERATION_COLORS.WARNING);
                    return [errorContainer];
                }
            } catch (error) {
                console.error('Error checking item count:', error);
            }
        }

        // Clear all feature states to prevent modal data persistence
        await clearAllFeatureStates(interaction.user.id, interaction.guild.id);

        // Initialize creation state
        await storeCreationState(interaction.user.id, interaction.guild.id, {
            selectedType: null,
            selectedRarity: null,
            parameters: {},
            itemName: null,
            itemEmote: null,
            stars: null, // BACKEND SUPPORT: Add stars field for future item pricing
            currentConfig: null, // What config option is currently being shown
            accessContext: accessContext // Store whether accessed through owner panel or guild
        });

        // Build unified container with type selection
        // Use correct guildId based on access context: null for owner panel, guild.id for guild access
        const isOwnerPanelAccess = accessContext === 'owner';
        const effectiveGuildId = isOwnerPanelAccess ? null : interaction.guild.id;
        // Always pass actual guild ID for image operations
        const containers = await this.buildUnifiedItemContainer(interaction.user.id, effectiveGuildId, isOwner, interaction.guild.id, interaction);

        return containers;
    },

    async handleItemSelection(interaction, itemId, accessContext = 'guild') {
        const isOwner = interaction.user.id === process.env.OWNER;

        try {
            // Load the existing item from database
            const item = await this.getItemById(itemId);

            if (!item) {
                const errorContainer = new ContainerBuilder();
                const errorText = new TextDisplayBuilder().setContent('❌ Item not found.');
                errorContainer.addTextDisplayComponents(errorText);
                return [errorContainer];
            }

            // Clear any existing creation state and set up editing state
            await clearCreationState(interaction.user.id, interaction.guild.id);

            // Pre-populate creation state with existing item data
            await storeCreationState(interaction.user.id, interaction.guild.id, {
                selectedType: item.type,
                selectedRarity: item.rarity,
                parameters: item.parameters || {},
                itemName: item.name,
                itemEmote: item.emote,
                itemEmoteId: item.emoteId,
                itemDescription: item.description,
                dropLocations: item.dropLocations || [],
                stars: item.stars || null, // BACKEND SUPPORT: Include stars field for pricing
                currentConfig: null,
                editingItemId: itemId, // Track that we're editing, not creating
                accessContext: accessContext, // Store access context for proper back navigation
                originalData: { // Store original data for comparison
                    selectedType: item.type,
                    selectedRarity: item.rarity,
                    parameters: item.parameters || {},
                    itemName: item.name,
                    itemEmote: item.emote,
                    itemEmoteId: item.emoteId,
                    itemDescription: item.description,
                    dropLocations: item.dropLocations || [],
                    stars: item.stars || null, // BACKEND SUPPORT: Include stars field for pricing
                    disabled: item.disabled || false,
                    guildId: item.guildId // Store original guild scope for LEVEL_UP logic
                }
            });

            // Build unified container with pre-populated data
            const containers = await buildUnifiedItemContainerWithState(interaction.user.id, interaction.guild.id, isOwner, interaction);
            return containers;

        } catch (error) {
            console.error('Error loading item for editing:', error);
            const errorContainer = new ContainerBuilder();
            const errorText = new TextDisplayBuilder().setContent('❌ An error occurred while loading the item.');
            errorContainer.addTextDisplayComponents(errorText);
            return [errorContainer];
        }
    },

    async handleConfigSelection(interaction) {
        try {
            const selectedConfig = interaction.values[0];
            const isOwner = interaction.user.id === process.env.OWNER;

            // Update state to show the selected config option (keep it selected)
            const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
            await storeCreationState(interaction.user.id, interaction.guild.id, {
                ...currentState,
                currentConfig: selectedConfig
            });

            // Handle parameter selection (modal or select menu)
            if (selectedConfig.startsWith('param-')) {
                const paramName = selectedConfig.replace('param-', '');

                // If parameter should use select menu, rebuild container to show it
                if (shouldUseSelectMenu(paramName)) {
                    // State is already updated with currentConfig, just rebuild
                    const containers = await buildUnifiedItemContainerWithState(interaction.user.id, interaction.guild.id, isOwner, interaction);
                    return containers;
                } else {
                    // Use modal for numeric parameters
                    await this.showParameterModal(interaction, paramName);
                    return []; // Modal shown, no components to return
                }
            }

            // Handle name/emote modals
            if (selectedConfig === 'name') {
                await this.showNameModal(interaction);
                return []; // Modal shown, no components to return
            }

            if (selectedConfig === 'emote') {
                // OPTIMIZED: Single image fetch through container building
                const containers = await buildUnifiedItemContainerWithState(interaction.user.id, interaction.guild.id, isOwner, interaction);
                return containers;
            }

            if (selectedConfig === 'description') {
                await this.showDescriptionModal(interaction);
                return []; // Modal shown, no components to return
            }

            // For rarity, rebuild container with cascading select (keeps rarity selected)
            const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
            return containers;

        } catch (error) {
            console.error('Error handling config selection:', error);
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ An error occurred while processing your selection. Please try again.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            return [errorContainer];
        }
    },

    async handleDropLocationsSelection(interaction) {
        const selectedLocations = interaction.values; // Array of selected values
        const isOwner = interaction.user.id === process.env.OWNER;

        // Get current state and update drop locations
        const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
        await storeCreationState(interaction.user.id, interaction.guild.id, {
            ...currentState,
            dropLocations: selectedLocations,
            currentConfig: null // Clear to hide the drop locations select menu
        });

        // Rebuild unified container
        const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
        return containers;
    },

    async handleTypeSelection(interaction) {
        const selectedType = interaction.values[0];
        const isOwner = interaction.user.id === process.env.OWNER;

        // Update creation state
        const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
        await storeCreationState(interaction.user.id, interaction.guild.id, {
            ...currentState,
            selectedType: selectedType,
            currentConfig: null // Clear current config to hide cascading select
        });

        // Rebuild unified container
        const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
        return containers;
    },

    async handleRaritySelection(interaction) {
        const selectedRarity = interaction.values[0];
        const isOwner = interaction.user.id === process.env.OWNER;

        // Get current state and update it (preserve currentConfig to keep rarity selected)
        const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
        await storeCreationState(interaction.user.id, interaction.guild.id, {
            ...currentState,
            selectedRarity: selectedRarity,
            currentConfig: null // Clear to hide the rarity select menu after selection
        });

        // Rebuild unified container
        const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
        return containers;
    },

    async handleParameterSelection(interaction) {
        const paramName = interaction.customId.replace('items-param-select-', '');
        const selectedValue = interaction.values[0];
        const isOwner = interaction.user.id === process.env.OWNER;

        // Get current state and update parameter
        const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
        const updatedParameters = { ...(currentState.parameters || {}), [paramName]: selectedValue };

        await storeCreationState(interaction.user.id, interaction.guild.id, {
            ...currentState,
            parameters: updatedParameters,
            currentConfig: null // Clear to hide the parameter select menu
        });

        // Rebuild unified container
        const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
        return containers;
    },

    async handleImageSelection(interaction) {
        const isOwner = interaction.user.id === process.env.OWNER;

        try {
            // FIXED: Handle "no-images" selection properly without double interaction handling
            if (interaction.values[0] === 'no-images') {
                // Just rebuild containers normally (no error message)
                const containers = await buildUnifiedItemContainerWithState(interaction.user.id, interaction.guild.id, isOwner, interaction);
                return containers;
            }

            // FIXED: Use shared image uploader utility with proper callback handling
            // The callback will handle the interaction response, so we don't do it here
            const result = await handleImageSelection(interaction, async (callbackInteraction, emoteData, selectedImage) => {
                // Update creation state with new emote
                const currentState = await getCreationState(callbackInteraction.user.id, callbackInteraction.guild.id);
                await storeCreationState(callbackInteraction.user.id, callbackInteraction.guild.id, {
                    ...currentState,
                    itemEmote: emoteData.string,
                    itemEmoteId: emoteData.id, // Store emote ID for cleanup
                    currentConfig: null // Clear to hide image select menu
                });

                // FIXED: Handle interaction response in callback to prevent double acknowledgment
                const containers = await buildUnifiedItemContainerWithState(callbackInteraction.user.id, callbackInteraction.guild.id, isOwner, callbackInteraction);
                // Note: This callback still needs to handle the interaction directly since it's called by the image uploader utility
                await callbackInteraction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: containers
                });
            }, { useApplicationEmote: true });

            // FIXED: Only handle interaction if the callback didn't already handle it
            // If result.success is true, the callback already handled the interaction
            if (!result.success && result.error) {
                // Error case - return error container
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent(`❌ ${result.error}`))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }
            return []; // Success case handled by callback
        } catch (error) {
            console.error('[items] ❌ Error in handleImageSelection:', error);
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ An error occurred while processing the image. Please try again.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            return [errorContainer];
        }
    },

    async showNameModal(interaction) {
        const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

        // Get current state to pre-populate with existing name
        const state = await getCreationState(interaction.user.id, interaction.guild.id);
        const currentName = state?.itemName || '';

        const modal = new ModalBuilder()
            .setCustomId('items-name-modal')
            .setTitle('Set Item Name');

        const nameInput = new TextInputBuilder()
            .setCustomId('item-name')
            .setLabel('Item Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter item name...')
            .setRequired(true)
            .setMaxLength(50);

        // FIXED: Only pre-populate if we're editing an existing item (not creating new)
        // This prevents modal data persistence between different item creations
        if (currentName && state?.editingItemId) {
            nameInput.setValue(currentName);
        }

        const row = new ActionRowBuilder().addComponents(nameInput);
        modal.addComponents(row);

        // CONVERTED: Show modal directly (Universal Interaction Manager compatible)
        await interaction.showModal(modal);
        return; // Modal shown, no components to return
    },



    async showParameterModal(interaction, paramName) {
        const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

        // Get current state to pre-populate with existing parameter value
        const state = await getCreationState(interaction.user.id, interaction.guild.id);
        const currentValue = state?.parameters?.[paramName] || '';

        const modal = new ModalBuilder()
            .setCustomId(`items-param-modal-${paramName}`)
            .setTitle(`Set ${paramName}`);

        // Get unit-specific placeholder text
        const placeholderText = getParameterPlaceholder(paramName);

        const paramInput = new TextInputBuilder()
            .setCustomId('param-value')
            .setLabel(paramName.charAt(0).toUpperCase() + paramName.slice(1))
            .setStyle(TextInputStyle.Short)
            .setPlaceholder(placeholderText)
            .setRequired(true)
            .setMaxLength(100);

        // FIXED: Only pre-populate if we're editing an existing item (not creating new)
        // This prevents modal data persistence between different item creations
        if (currentValue && state?.editingItemId) {
            paramInput.setValue(currentValue);
        }

        const row = new ActionRowBuilder().addComponents(paramInput);
        modal.addComponents(row);

        // CONVERTED: Show modal directly (Universal Interaction Manager compatible)
        await interaction.showModal(modal);
        return; // Modal shown, no components to return
    },

    async showDescriptionModal(interaction) {
        const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

        // Get current state to pre-populate with existing description
        const state = await getCreationState(interaction.user.id, interaction.guild.id);
        const currentDescription = state?.itemDescription || '';

        const modal = new ModalBuilder()
            .setCustomId('items-description-modal')
            .setTitle('Set Item Description');

        const descInput = new TextInputBuilder()
            .setCustomId('item-description')
            .setLabel('Item Description')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter item description...')
            .setRequired(true)
            .setMaxLength(200);

        // FIXED: Only pre-populate if we're editing an existing item (not creating new)
        // This prevents modal data persistence between different item creations
        if (currentDescription && state?.editingItemId) {
            descInput.setValue(currentDescription);
        }

        const row = new ActionRowBuilder().addComponents(descInput);
        modal.addComponents(row);

        // CONVERTED: Show modal directly (Universal Interaction Manager compatible)
        await interaction.showModal(modal);
        return; // Modal shown, no components to return
    },



    async modalSubmit(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            const isOwner = interaction.user.id === process.env.OWNER;

        if (interaction.customId === 'items-name-modal') {
            const itemName = interaction.fields.getTextInputValue('item-name');

            // Update creation state
            const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
            await storeCreationState(interaction.user.id, interaction.guild.id, {
                ...currentState,
                itemName: itemName,
                currentConfig: null // Clear to hide any cascading selects
            });

            // Rebuild containers
            const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);

            return containers;

        } else if (interaction.customId === 'items-description-modal') {
            const itemDescription = interaction.fields.getTextInputValue('item-description');

            // Update creation state
            const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
            await storeCreationState(interaction.user.id, interaction.guild.id, {
                ...currentState,
                itemDescription: itemDescription,
                currentConfig: null // Clear to hide any cascading selects
            });

            // Rebuild containers
            const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
            return containers;

        } else if (interaction.customId.startsWith('items-param-modal-')) {
            const paramName = interaction.customId.replace('items-param-modal-', '');
            const paramValue = interaction.fields.getTextInputValue('param-value');

            // Add units to the parameter value
            const paramValueWithUnits = addParameterUnits(paramName, paramValue);

            // Update creation state
            const currentState = await getCreationState(interaction.user.id, interaction.guild.id);
            const updatedParameters = { ...(currentState.parameters || {}), [paramName]: paramValueWithUnits };

            await storeCreationState(interaction.user.id, interaction.guild.id, {
                ...currentState,
                parameters: updatedParameters,
                currentConfig: null // Clear to hide any cascading selects
            });

            // Rebuild containers
            const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
            return containers;

        } else if (interaction.customId === 'items-owner-dm-message-template-modal') {
            // Handle owner DM message template modal submission
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild owner items container with status message instead of ephemeral reply
                const containerResult = await buildItemsContainer({
                    isOwner: true,
                    guildId: null, // Bot-wide items for owner
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    showBackButton: true,
                    statusMessage: 'who r u? no.'
                });

                return Array.isArray(containerResult) ? containerResult : [containerResult];
            }

            const newTemplate = interaction.fields.getTextInputValue('owner-dm-message-template-input');

            // Update the global DM message template
            await optimizedUpdateOne("item_notifications",
                { key: 'global' },
                { $set: { dmMessage: newTemplate } },
                { upsert: true }
            );

            // Rebuild owner items container with success status
            const containerResult = await buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items for owner
                page: 0,
                client: interaction.client,
                member: interaction.member,
                showBackButton: true,
                statusMessage: '**status:** DM message template updated'
            });

            return Array.isArray(containerResult) ? containerResult : [containerResult];
        }
        }, {
            autoDefer: false, // Don't auto-defer for modal submit - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your form submission. Please try again.'
        });
    },

    async buttons(interaction, args) {
        console.log(`[items-buttons] 🚀 ENTRY: buttons function called with customId: ${interaction.customId}`);
        console.log(`[items-buttons] 🔘 Button pressed: ${interaction.customId}`);

        try {
            return handleUIOperation(interaction, async (interaction) => {
                console.log(`[items-buttons] 🎯 INSIDE handleUIOperation for: ${interaction.customId}`);
            // Handle global enable/disable buttons
            if (interaction.customId === 'items-global-disable' || interaction.customId === 'items-global-enable') {
                console.log(`[items-buttons] 🎯 Processing global enable/disable button: ${interaction.customId}`);
                // Check permissions
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                if (!hasPermission) {
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                    const statusContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** You need Administrator permission to configure items.'))
                        .setAccentColor(OPERATION_COLORS.DELETE);
                    return [selectMenu, statusContainer];
                }

                // Get guild configuration
                let guildData = await getCachedGuildItemConfig(interaction.guild.id);
                if (!guildData) {
                    guildData = { items: { enabled: true } };
                }
                if (!guildData.items) {
                    guildData.items = { enabled: true };
                }

                // Toggle the items enabled state
                const newState = interaction.customId === 'items-global-enable';
                await optimizedUpdateOne("guilds",
                    { id: interaction.guild.id },
                    { $set: { 'items.enabled': newState } },
                    { upsert: true }
                );

                // Send feature toggle log
                await sendFeatureToggleLog(
                    interaction.guild.id,
                    'Items System',
                    null, // No subcomponent for global toggle
                    newState,
                    interaction.user.id,
                    interaction.client
                );

                // Update local data
                guildData.items.enabled = newState;

                // Rebuild the items interface
                const containerResult = await buildItemsContainer({
                    isOwner: false,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData
                });

                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(newState ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(newState ? 'disable' : 'enable')
                    .setStyle(newState ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(false);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                console.log(`[items-buttons] 📦 Returning ${containers.length} containers:`, containers.map(c => c.constructor.name));
                return containers;
            }

            // Handle individual item disable/enable toggle
            if (interaction.customId === 'items-toggle-disable') {
                console.log(`[items-buttons] 🎯 Processing individual item disable toggle`);
                return await this.toggleItemDisable(interaction);
            }

            // Handle creation back button
            if (interaction.customId === 'items-creation-back') {
                console.log(`[items-buttons] 🎯 Processing creation back button`);
                // Get creation state to determine access context
                const state = await getCreationState(interaction.user.id, interaction.guild.id);
                console.log(`[items-buttons] 🔍 Creation state:`, state);
                const accessContext = state?.accessContext || 'guild';
                console.log(`[items-buttons] 🔍 Access context: ${accessContext}`);
                const isOwnerPanelAccess = accessContext === 'owner';
                console.log(`[items-buttons] 🔍 Is owner panel access: ${isOwnerPanelAccess}`);

                // Clear creation state
                await clearCreationState(interaction.user.id, interaction.guild.id);

                // Get appropriate guild data based on access context
                let guildData = null;
                if (!isOwnerPanelAccess && interaction.guild.id) {
                    guildData = await getCachedGuildItemConfig(interaction.guild.id);
                }

                const containerResult = await buildItemsContainer({
                    isOwner: isOwnerPanelAccess,
                    guildId: isOwnerPanelAccess ? null : interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: isOwnerPanelAccess ? null : interaction.member,
                    showBackButton: isOwnerPanelAccess,
                    guildData: guildData
                });

                if (isOwnerPanelAccess) {
                    // Owner panel - no features menu
                    return Array.isArray(containerResult) ? containerResult : [containerResult];
                } else {
                    // Guild context - add features menu and global button
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                    const hasPermission = global.hasFeaturePermission(interaction.member, 'items');

                    if (!guildData) {
                        guildData = await getCachedGuildItemConfig(interaction.guild.id);
                        if (!guildData) guildData = { items: { enabled: true } };
                        if (!guildData.items) guildData.items = { enabled: true };
                    }

                    const globalItemsButton = new ButtonBuilder()
                        .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                        .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                        .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                        .setDisabled(!hasPermission);
                    const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                    return Array.isArray(containerResult)
                        ? [selectMenu, ...containerResult, globalButtonRow]
                        : [selectMenu, containerResult, globalButtonRow];
                }
            }

            // Handle pagination buttons
            if (interaction.customId.startsWith('items-page-')) {
                console.log(`[items-buttons] 🎯 Processing pagination button: ${interaction.customId}`);
                const parts = interaction.customId.split('-');
                const page = parseInt(parts[2]);
                const accessMode = parts[3]; // 'owner' or 'regular'

                // Use access mode from button ID
                const isOwner = accessMode === 'owner';

                const containerResult = await buildItemsContainer({
                    isOwner: isOwner,
                    guildId: isOwner ? null : interaction.guild.id,
                    page: page,
                    client: interaction.client,
                    member: interaction.member,
                    showBackButton: false
                });

                if (isOwner) {
                    // Owner mode - no features menu
                    return Array.isArray(containerResult) ? containerResult : [containerResult];
                } else {
                    // Guild mode - add features menu and global button
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                    const guildData = await getCachedGuildItemConfig(interaction.guild.id);
                    const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                    const globalItemsButton = new ButtonBuilder()
                        .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                        .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                        .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                        .setDisabled(!hasPermission);
                    const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                    return Array.isArray(containerResult)
                        ? [selectMenu, ...containerResult, globalButtonRow]
                        : [selectMenu, containerResult, globalButtonRow];
                }
            }

            // Handle delete item button
            if (interaction.customId === 'items-delete-item') {
                console.log(`[items-buttons] 🎯 Processing delete item button`);
                return await this.handleDeleteClick(interaction);
            }

            // Handle create/update final button
            if (interaction.customId === 'items-create-final') {
                console.log(`[items-buttons] 🎯 Processing create/update final button`);
                return await this.createFinalItem(interaction);
            }

            // For all other buttons, return a placeholder response
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Button functionality not yet implemented.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            return [selectMenu, errorContainer];
            }, {
                autoDefer: false,
                ephemeral: true,
                fallbackMessage: '❌ There was an error processing your button press. Please try again.'
            });
        } catch (error) {
            console.error(`[items-buttons] ❌ Error in buttons function:`, error);
            throw error;
        }
    },

    async createFinalItem(interaction) {
        const isOwner = interaction.user.id === process.env.OWNER;

        try {
            // CONVERTED: Removed manual deferUpdate - Universal Interaction Manager handles this automatically

            // Get creation state
            const state = await getCreationState(interaction.user.id, interaction.guild.id);

            if (!state || !state.selectedType || !state.selectedRarity || !state.itemName) {
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ Item configuration incomplete. Please set type, rarity, and name.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }

            // Prepare item data using access context
            const accessContext = state?.accessContext || 'guild';
            const isOwnerPanelAccess = accessContext === 'owner';

            const itemData = {
                name: state.itemName,
                type: state.selectedType,
                rarity: state.selectedRarity,
                emote: state.itemEmote || ITEM_TYPES[state.selectedType]?.emote,
                emoteId: state.itemEmoteId || null, // Store application emote ID
                parameters: state.parameters || {},
                description: state.itemDescription || ITEM_TYPES[state.selectedType]?.description,
                dropLocations: state.dropLocations || [],
                stars: state.stars || null, // BACKEND SUPPORT: Include stars field for pricing
                createdBy: interaction.user.id,
                // Use access context to determine scope: owner panel = bot-wide (null), guild = guild-specific
                guildId: isOwnerPanelAccess ? null : interaction.guild.id
            };

            let resultItem;

            if (state.editingItemId) {
                // Update existing item - preserve original guildId
                const originalItem = await this.getItemById(state.editingItemId);
                const updateData = {
                    ...itemData,
                    guildId: originalItem.guildId // Preserve original guild scope
                };
                resultItem = await this.updateItem(state.editingItemId, updateData);

                // Mark emoji as saved if it exists
                if (itemData.emoteId) {
                    markEmojiAsSaved(itemData.emoteId);
                }

                // Log item update - use different logging based on item scope
                try {
                    const changes = this.calculateItemChanges(originalItem, itemData);
                    if (originalItem.guildId === null) {
                        // Bot-wide item - use owner logging
                        const { sendOwnerItemManagementLog } = require('../../utils/sendLog.js');
                        await sendOwnerItemManagementLog('updated', interaction.user.id, itemData, changes, interaction.client);
                    } else {
                        // Guild-specific item - use guild logging
                        const { sendItemManagementLog } = require('../../utils/sendLog.js');
                        await sendItemManagementLog(interaction.guild.id, 'updated', interaction.user.id, itemData, changes, interaction.client);
                    }
                } catch (logError) {
                    console.error('Error sending item update log:', logError);
                }
            } else {
                // Create new item
                resultItem = await createItem(itemData);

                // Mark emoji as saved if it exists
                if (itemData.emoteId) {
                    markEmojiAsSaved(itemData.emoteId);
                }

                // Log item creation - use different logging based on item scope
                try {
                    if (itemData.guildId === null) {
                        // Bot-wide item - use owner logging
                        const { sendOwnerItemManagementLog } = require('../../utils/sendLog.js');
                        await sendOwnerItemManagementLog('created', interaction.user.id, itemData, null, interaction.client);
                    } else {
                        // Guild-specific item - use guild logging
                        const { sendItemManagementLog } = require('../../utils/sendLog.js');
                        await sendItemManagementLog(interaction.guild.id, 'created', interaction.user.id, itemData, null, interaction.client);
                    }
                } catch (logError) {
                    console.error('Error sending item creation log:', logError);
                }
            }

            // Get access context before clearing state
            const returnAccessContext = state?.accessContext || 'guild';

            // Clear creation state
            await clearCreationState(interaction.user.id, interaction.guild.id);

            // Return to items list using stored access context
            const isReturnOwnerPanel = returnAccessContext === 'owner';

            // Fetch guildData for guild context to ensure drop notifications are displayed
            let guildData = null;
            if (!isReturnOwnerPanel && interaction.guild.id) {
                guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            }

            const containerResult = await buildItemsContainer({
                isOwner: isReturnOwnerPanel, // Use access context, not user's owner status
                guildId: isReturnOwnerPanel ? null : interaction.guild.id, // Owner panel = bot-wide, guild = guild-specific
                page: 0,
                client: interaction.client,
                member: isReturnOwnerPanel ? null : interaction.member, // No member context for bot-wide
                showBackButton: isReturnOwnerPanel, // Show back button only for owner panel
                guildData: guildData // Pass guildData to ensure drop notifications are displayed
            });

            if (isReturnOwnerPanel) {
                // Owner panel access - just return the container (no features menu or enable/disable button)
                const containers = Array.isArray(containerResult) ? containerResult : [containerResult];
                return containers;
            } else {
                // Guild access - include features select menu and enable/disable button
                const { buildSelectMenu } = require('./featuresMenu');
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');

                // Get guild data for enable/disable button
                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: { enabled: true } };
                if (!guildData.items) guildData.items = { enabled: true };

                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                return containers;
            }

        } catch (error) {
            console.error('Error saving item:', error);

            // FIXED: Use Components v2 compatible error handling
            try {
                // Create error container using Components v2 pattern
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(
                        new TextDisplayBuilder().setContent('## Error\n❌ An error occurred while saving the item. Please try again.')
                    )
                    .setAccentColor(OPERATION_COLORS.DELETE);

                return [errorContainer];
            } catch (editError) {
                console.error('Failed to save item:', editError);
                // CONVERTED: Use ContainerBuilder error pattern instead of direct followUp
                const fallbackErrorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ An error occurred while saving the item. Please try again.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [fallbackErrorContainer];
            }
        }
    },

    async getItemById(itemId) {
        return await getItemById(itemId);
    },

    calculateItemChanges(originalItem, newItemData) {
        const changes = {};

        // Compare basic fields
        if (originalItem.name !== newItemData.name) {
            changes.name = { from: originalItem.name, to: newItemData.name };
        }

        if (originalItem.type !== newItemData.type) {
            changes.type = { from: originalItem.type, to: newItemData.type };
        }

        if (originalItem.rarity !== newItemData.rarity) {
            changes.rarity = { from: originalItem.rarity, to: newItemData.rarity };
        }

        if (originalItem.emote !== newItemData.emote) {
            changes.emote = { from: originalItem.emote, to: newItemData.emote };
        }

        if (originalItem.description !== newItemData.description) {
            changes.description = { from: originalItem.description, to: newItemData.description };
        }

        // Compare drop locations
        const originalLocations = (originalItem.dropLocations || []).sort();
        const newLocations = (newItemData.dropLocations || []).sort();
        if (JSON.stringify(originalLocations) !== JSON.stringify(newLocations)) {
            changes.dropLocations = { from: originalLocations.join(', '), to: newLocations.join(', ') };
        }

        // Compare parameters
        const originalParams = originalItem.parameters || {};
        const newParams = newItemData.parameters || {};
        for (const param in { ...originalParams, ...newParams }) {
            if (originalParams[param] !== newParams[param]) {
                changes[`parameter_${param}`] = {
                    from: originalParams[param] || 'none',
                    to: newParams[param] || 'none'
                };
            }
        }

        return changes;
    },

    async updateItem(itemId, itemData) {
        try {


            // Update the item while preserving original creation data
            const updateData = {
                ...itemData,
                updatedAt: new Date()
            };

            await optimizedUpdateOne("custom_items",
                { id: itemId },
                { $set: updateData }
            );

            // Get the updated item
            const updatedItem = await optimizedFindOne("custom_items", { id: itemId });

            // CRITICAL FIX: Invalidate item cache after update to ensure changes are visible immediately
            try {
                const { invalidateItemCache } = require('../../utils/itemCache.js');
                invalidateItemCache(itemId);
                console.log(`[items] 🔄 Invalidated item cache for updated item: ${itemId} (${updatedItem?.name || 'unknown'})`);
            } catch (cacheError) {
                console.error('[items] Error invalidating item cache after update:', cacheError);
            }

            // FIXED: Clear all relevant caches to force refresh after item update
            clearAllItemsCaches();

            return updatedItem;

        } catch (error) {
            console.error('Error updating item:', error);
            throw error;
        }
    },

    async deleteItem(itemId, client) {
        return await deleteItem(itemId, client);
    },

    async toggleItemDisable(interaction) {
        const isOwner = interaction.user.id === process.env.OWNER;

        try {
            // Get current state to find the item being edited
            const state = await getCreationState(interaction.user.id, interaction.guild.id);

            if (!state || !state.editingItemId) {
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ No item is currently being edited.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }

            // Get the current item
            const item = await this.getItemById(state.editingItemId);
            if (!item) {
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ Item not found.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }

            // Toggle the disabled state
            const newDisabledState = !item.disabled;

            // Update the item in database using optimized utilities
            await optimizedUpdateOne("custom_items",
                { id: state.editingItemId },
                { $set: { disabled: newDisabledState, updatedAt: new Date() } }
            );

            // CRITICAL FIX: Invalidate item cache after enable/disable to ensure status change is visible immediately
            try {
                const { invalidateItemCache } = require('../../utils/itemCache.js');
                invalidateItemCache(state.editingItemId);
                console.log(`[items] 🔄 Invalidated item cache for toggled item: ${state.editingItemId} (${item.name}) - disabled: ${!item.disabled}`);
            } catch (cacheError) {
                console.error('[items] Error invalidating item cache after toggle:', cacheError);
            }

            // FIXED: Clear all relevant caches to force refresh after item enable/disable
            clearAllItemsCaches();

            // Clear droppable items cache to stop/start drops immediately
            const { clearSpecificCache } = require('../../utils/itemDropsHybrid.js');
            clearSpecificCache('droppableItems');

            // Log the disable/enable action - use different logging based on item scope
            try {
                const action = newDisabledState ? 'disabled' : 'enabled';
                if (item.guildId === null) {
                    // Bot-wide item - use owner logging
                    const { sendOwnerItemManagementLog } = require('../../utils/sendLog.js');
                    await sendOwnerItemManagementLog(action, interaction.user.id, item, null, interaction.client);
                } else {
                    // Guild-specific item - use guild logging
                    const { sendItemManagementLog } = require('../../utils/sendLog.js');
                    await sendItemManagementLog(interaction.guild.id, action, interaction.user.id, item, null, interaction.client);
                }
            } catch (logError) {
                console.error('Error sending item disable/enable log:', logError);
            }

            // Update the current state to reflect the new disabled status
            await storeCreationState(interaction.user.id, interaction.guild.id, {
                ...state,
                originalData: {
                    ...state.originalData,
                    disabled: newDisabledState
                }
            });

            // Rebuild the container with updated state
            const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);

            return containers;

        } catch (error) {
            console.error('Error toggling item disable state:', error);

            // Return error container instead of direct reply
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ An error occurred while updating the item.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            return [errorContainer];
        }
    },

    async handleDeleteClick(interaction) {
        const isOwner = interaction.user.id === process.env.OWNER;

        try {
            // Get current state to find the item being edited
            const state = await getCreationState(interaction.user.id, interaction.guild.id);

            if (!state || !state.editingItemId) {
                return [];
            }

            // Get the current item
            const item = await this.getItemById(state.editingItemId);
            if (!item) {
                return [];
            }

            // Initialize or increment delete click counter
            const deleteClicks = (state.deleteClicks || 0) + 1;

            // Update state with new click count
            await storeCreationState(interaction.user.id, interaction.guild.id, {
                ...state,
                deleteClicks: deleteClicks
            });

            if (deleteClicks >= 3) {
                // Third click - actually delete the item
                const success = await this.deleteItem(state.editingItemId, interaction.client);

                if (!success) {
                    // Reset click counter on failure
                    await storeCreationState(interaction.user.id, interaction.guild.id, {
                        ...state,
                        deleteClicks: 0
                    });

                    const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
                    return containers;
                }

                // Log the deletion - use different logging based on item scope
                try {
                    if (item.guildId === null) {
                        // Bot-wide item - use owner logging
                        const { sendOwnerItemManagementLog } = require('../../utils/sendLog.js');
                        await sendOwnerItemManagementLog('deleted', interaction.user.id, item, null, interaction.client);
                    } else {
                        // Guild-specific item - use guild logging
                        const { sendItemManagementLog } = require('../../utils/sendLog.js');
                        await sendItemManagementLog(interaction.guild.id, 'deleted', interaction.user.id, item, null, interaction.client);
                    }
                } catch (logError) {
                    console.error('Error sending item deletion log:', logError);
                }

                // Get access context before clearing state
                const deleteAccessContext = state?.accessContext || 'guild';

                // Clear creation state
                await clearCreationState(interaction.user.id, interaction.guild.id);

                // Return to items list using stored access context
                const isDeleteOwnerPanel = deleteAccessContext === 'owner';

                // Fetch guildData for guild context to ensure drop notifications are displayed
                let guildData = null;
                if (!isDeleteOwnerPanel && interaction.guild.id) {
                    guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                }

                const containerResult = await buildItemsContainer({
                    isOwner: isDeleteOwnerPanel, // Use access context, not user's owner status
                    guildId: isDeleteOwnerPanel ? null : interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: isDeleteOwnerPanel ? null : interaction.member,
                    showBackButton: isDeleteOwnerPanel,
                    guildData: guildData // Pass guildData to ensure drop notifications are displayed
                });

                if (isDeleteOwnerPanel) {
                    const containers = Array.isArray(containerResult) ? containerResult : [containerResult];
                    return containers;
                } else {
                    const { buildSelectMenu } = require('./featuresMenu');
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                    const hasPermission = global.hasFeaturePermission(interaction.member, 'items');

                    let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                    if (!guildData) guildData = { items: { enabled: true } };
                    if (!guildData.items) guildData.items = { enabled: true };

                    const globalItemsButton = new ButtonBuilder()
                        .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                        .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                        .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                        .setDisabled(!hasPermission);
                    const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                    const containers = Array.isArray(containerResult)
                        ? [selectMenu, ...containerResult, globalButtonRow]
                        : [selectMenu, containerResult, globalButtonRow];

                    return containers;
                }

                console.log(`[items] 🗑️ Item "${item.name}" (${state.editingItemId}) deleted by ${interaction.user.username}`);
            } else {
                // First or second click - update the interface with click counter
                const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
                return containers;
            }

        } catch (error) {
            console.error('Error handling delete click:', error);

            // Reset delete clicks on error
            const state = await getCreationState(interaction.user.id, interaction.guild.id);
            if (state) {
                await storeCreationState(interaction.user.id, interaction.guild.id, {
                    ...state,
                    deleteClicks: 0
                });
            }

            const containers = await this.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, isOwner, interaction.guild.id, interaction);
            return containers;
        }
    },

    // Handle toggle owner DM notifications (immediate toggle, no separate page)
    async handleToggleOwnerDMNotifications(interaction) {
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild owner items container with status message instead of ephemeral reply
            const containerResult = await buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items for owner
                page: 0,
                client: interaction.client,
                member: interaction.member,
                showBackButton: true,
                statusMessage: 'who r u? no.'
            });

            return Array.isArray(containerResult) ? containerResult : [containerResult];
        }

        // Get current config and toggle
        let config = await optimizedFindOne("item_notifications", { key: 'global' });
        if (!config) {
            config = { key: 'global', enabled: true, dmMessage: 'You found {items} in **{server}**, dropped from {location}:' };
            await optimizedInsertOne("item_notifications", config);
        }

        const newState = !config.enabled;
        await optimizedUpdateOne("item_notifications",
            { key: 'global' },
            { $set: { enabled: newState } },
            { upsert: true }
        );

        // Rebuild items container with updated state (returns array for owner with preview)
        const containerResult = await this.buildItemsContainer({
            isOwner: true,
            guildId: null, // Bot-wide items
            page: 0,
            client: interaction.client,
            member: null,
            showBackButton: true // Owner panel context
        });

        return Array.isArray(containerResult) ? containerResult : [containerResult];
    },

    // Handle edit owner DM message template (show modal)
    async handleEditOwnerDMMessageTemplate(interaction) {
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild owner items container with status message instead of ephemeral reply
            const containerResult = await buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items for owner
                page: 0,
                client: interaction.client,
                member: interaction.member,
                showBackButton: true,
                statusMessage: 'who r u? no.'
            });

            return Array.isArray(containerResult) ? containerResult : [containerResult];
        }

        // Get current DM message template
        let config = await optimizedFindOne("item_notifications", { key: 'global' });
        if (!config) {
            config = { key: 'global', enabled: true, dmMessage: 'You found {emoji} {items} in {server}, dropped from {location}:' };
            // FIXED: Use optimizedInsertOne instead of deprecated col.insertOne
            await optimizedInsertOne("item_notifications", config);
        }

        // Show modal to edit the DM message template
        const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

        const msgInput = new TextInputBuilder()
            .setCustomId('owner-dm-message-template-input')
            .setLabel('Item Drop DM Message Template')
            .setPlaceholder('Available variables: {emoji}, {items}, {server}, {location}')
            .setValue(config.dmMessage)
            .setRequired(true)
            .setMinLength(1)
            .setMaxLength(200)
            .setStyle(TextInputStyle.Paragraph);

        const row = new ActionRowBuilder().addComponents(msgInput);
        const modal = new ModalBuilder()
            .setCustomId('items-owner-dm-message-template-modal')
            .setTitle('Edit Global DM Message Template')
            .addComponents(row);

        // CONVERTED: Show modal directly (Universal Interaction Manager compatible)
        await interaction.showModal(modal);
        return; // Modal shown, no components to return
    },

    // Handle toggle drop notifications (immediate toggle, no separate page)
    async handleToggleDropNotifications(interaction) {
        try {
            const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
            if (!hasPermission) {
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ You need Administrator permission to configure items.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }

            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { items: {} };
            if (!guildData.items) guildData.items = {};

            const currentState = guildData.items.dropNotificationsEnabled ?? false;
            const newState = !currentState;

            // FIXED: Use optimizedUpdateOne instead of deprecated col.updateOne
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { 'items.dropNotificationsEnabled': newState } },
                { upsert: true }
            );

            // Rebuild items container with updated state
            guildData.items.dropNotificationsEnabled = newState;
            const containerResult = await this.buildItemsContainer({
                isOwner: false,
                guildId: interaction.guild.id,
                page: 0,
                client: interaction.client,
                member: interaction.member,
                guildData: guildData
            });

            // Need to rebuild the full interface like in 17.js
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const hasPermissionForButton = global.hasFeaturePermission(interaction.member, 'items');
            const globalItemsButton = new ButtonBuilder()
                .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermissionForButton);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

            return [selectMenu, containerResult, globalButtonRow];
        } catch (error) {
            console.error('[handleToggleDropNotifications] Error:', error);
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('❌ An error occurred while toggling drop notifications.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
            return [errorContainer];
        }
    },

    // Handle show channel select (cascading menu, no separate page)
    async handleShowChannelSelect(interaction) {
        const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
        if (!hasPermission) {
            // Build container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const statusContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** You need Administrator permission to configure items.'))
                .setAccentColor(OPERATION_COLORS.DELETE);

            return [selectMenu, statusContainer];
        }

        // Get guild data
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { items: { enabled: true } };
        if (!guildData.items) guildData.items = { enabled: true };

        // Rebuild items container with channel select showing and option selected
        const containerResult = await this.buildItemsContainer({
            isOwner: false,
            guildId: interaction.guild.id,
            page: 0,
            client: interaction.client,
            member: interaction.member,
            guildData: guildData,
            selectedNotificationOption: 'set-drop-channel' // Show cascading channel select and keep option selected
        });

        // Need to rebuild the full interface like in 17.js
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
        const hasPermissionForButton = global.hasFeaturePermission(interaction.member, 'items');
        const globalItemsButton = new ButtonBuilder()
            .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
            .setLabel(guildData.items.enabled ? 'disable' : 'enable')
            .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermissionForButton);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

        return [selectMenu, containerResult, globalButtonRow];
    }
};

// Export constants for use in other modules
module.exports.RARITIES = RARITIES;
module.exports.ITEM_TYPES = ITEM_TYPES;
module.exports.DROP_LOCATIONS = DROP_LOCATIONS;
module.exports.NOTHING_WEIGHT = NOTHING_WEIGHT;

// Export parameter randomizer functions for use in other systems (like EXP item drops)
module.exports.randomizeItemParameters = PARAMETER_RANDOMIZER.randomizeItemParameters.bind(PARAMETER_RANDOMIZER);
module.exports.randomizeParameter = PARAMETER_RANDOMIZER.randomizeParameter.bind(PARAMETER_RANDOMIZER);

// Export item container builders for DMs and server notifications
module.exports.buildUnifiedItemDisplayContainer = buildUnifiedItemDisplayContainer;
module.exports.buildFoundItemContainer = buildFoundItemContainer;
module.exports.buildServerFoundItemContainer = buildServerFoundItemContainer;
module.exports.buildDynamicFoundItemContainer = buildDynamicFoundItemContainer;

// Export cache management functions
module.exports.clearLiveTotalsCache = clearLiveTotalsCache;
module.exports.clearAllItemsCaches = clearAllItemsCaches;

// Export utility functions
module.exports.getArticle = getArticle;

// Export discovery rank formatting utilities
module.exports.getOrdinalSuffix = (num) => {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
};

// Export discovery rank formatting function for consistent display across all views
module.exports.formatDiscoveryRanks = (guildRank, globalRank, options = {}) => {
    const { showTotals = true, showOrdinals = true } = options;
    const getOrdinalSuffix = module.exports.getOrdinalSuffix;

    let rankText = '';

    if (guildRank?.discoveryRank) {
        const guildOrdinal = showOrdinals ? getOrdinalSuffix(guildRank.discoveryRank) : '';
        if (showTotals && guildRank.total) {
            rankText += `${guildRank.discoveryRank}${guildOrdinal}/${guildRank.total} server`;
        } else {
            rankText += `${guildRank.discoveryRank}${guildOrdinal} server`;
        }
    }

    if (globalRank?.discoveryRank) {
        if (rankText) rankText += ', ';
        const globalOrdinal = showOrdinals ? getOrdinalSuffix(globalRank.discoveryRank) : '';
        if (showTotals && globalRank.total) {
            rankText += `${globalRank.discoveryRank}${globalOrdinal}/${globalRank.total} global`;
        } else {
            rankText += `${globalRank.discoveryRank}${globalOrdinal} global`;
        }
    }

    return rankText;
};

// Auto-report performance every 10 minutes in development (matching Global Levels)
if (process.env.NODE_ENV === 'development') {
    setInterval(async () => {
        const stats = getCacheStats();
        const perfMetrics = { ...itemsMetrics }; // Access metrics directly instead of through function

        if (perfMetrics.itemsProcessed > 0) {
            const cacheHitRate = perfMetrics.cacheHits / (perfMetrics.cacheHits + perfMetrics.cacheMisses) * 100;
            console.log(`[Items Performance] Cache: ${cacheHitRate.toFixed(1)}% hit rate, Items: ${perfMetrics.itemsProcessed}, Queries: ${perfMetrics.databaseQueries}, Avg: ${perfMetrics.averageQueryTime.toFixed(1)}ms`);

            // Log high-priority recommendations if cache hit rate is low
            if (cacheHitRate < 70) {
                console.warn('[Items Performance] Low cache hit rate detected - consider cache optimization');
            }
        }
    }, itemsMetrics.performanceReportInterval);
}
