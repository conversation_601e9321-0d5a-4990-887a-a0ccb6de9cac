const { spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffmpeg = require('fluent-ffmpeg');

/**
 * Whisper Integration Module for Discord Bot
 * Handles downloading voice messages and transcribing them using the Python Whisper script
 */

class WhisperIntegration {
    constructor(options = {}) {
        this.model = options.model || process.env.WHISPER_MODEL || 'base';
        this.language = options.language || process.env.WHISPER_LANGUAGE || null;
        this.task = options.task || 'transcribe';
        this.format = options.format || 'json';
        this.tempDir = options.tempDir || process.env.TEMP_DIR || './temp_audio';
        this.transcriptDir = options.transcriptDir || process.env.TRANSCRIPT_DIR || './transcripts';
        this.pythonPath = options.pythonPath || this.findPythonPath();
        this.transcriptPath = options.transcriptPath || process.env.TRANSCRIBER_PATH || './transcriber.py';
        
        // Ensure directories exist
        fs.ensureDirSync(this.tempDir);
        fs.ensureDirSync(this.transcriptDir);
    }

    /**
     * Find Python executable path
     */
    findPythonPath() {
        const { execSync } = require('child_process');

        // Common Python executable names to try
        const pythonCommands = process.platform === 'win32'
            ? ['python', 'python3', 'py']
            : ['python3', 'python'];

        for (const cmd of pythonCommands) {
            try {
                // Try to get the Python executable path
                const pythonPath = execSync(`where ${cmd}`, { encoding: 'utf8', timeout: 5000 }).trim().split('\n')[0];
                if (pythonPath && fs.existsSync(pythonPath)) {
                    console.log(`🐍 Found Python at: ${pythonPath}`);
                    return pythonPath;
                }
            } catch (error) {
                // Command failed, try next one
                continue;
            }
        }

        // Fallback to default command
        const fallback = process.platform === 'win32' ? 'python' : 'python3';
        console.warn(`⚠️ Could not find Python executable, using fallback: ${fallback}`);
        return fallback;
    }

    /**
     * Test if Whisper is installed and working
     */
    async testWhisperInstallation() {
        return new Promise((resolve) => {
            const { spawn } = require('child_process');
            console.log(`🧪 Testing Whisper installation...`);

            const testProcess = spawn(this.pythonPath, ['-c', 'import whisper; print("Whisper installed successfully")'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                timeout: 10000
            });

            let stdout = '';
            let stderr = '';

            testProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            testProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            testProcess.on('close', (code) => {
                if (code === 0 && stdout.includes('Whisper installed successfully')) {
                    console.log(`✅ Whisper is installed and working`);
                    resolve(true);
                } else {
                    console.error(`❌ Whisper test failed (code: ${code})`);
                    console.error(`❌ Stdout: ${stdout}`);
                    console.error(`❌ Stderr: ${stderr}`);
                    resolve(false);
                }
            });

            testProcess.on('error', (error) => {
                console.error(`❌ Whisper test error: ${error.message}`);
                resolve(false);
            });
        });
    }

    /**
     * Download voice message from Discord
     */
    async downloadVoiceMessage(attachment, messageId) {
        const audioPath = path.join(this.tempDir, `voice_${messageId}.ogg`);
        
        try {
            console.log(`📥 Downloading voice message: ${attachment.url}`);
            
            const response = await axios({
                method: 'GET',
                url: attachment.url,
                responseType: 'stream',
                timeout: 30000 // 30 second timeout
            });
            
            const writer = fs.createWriteStream(audioPath);
            response.data.pipe(writer);
            
            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    console.log(`✅ Voice message downloaded: ${audioPath}`);
                    resolve(audioPath);
                });
                writer.on('error', reject);
            });
            
        } catch (error) {
            console.error(`❌ Download error: ${error.message}`);
            throw new Error(`Failed to download voice message: ${error.message}`);
        }
    }

    /**
     * Transcribe audio file using Python Whisper script
     */
    async transcribeAudio(audioPath, options = {}) {
        const model = options.model || this.model;
        const language = options.language || this.language;
        const task = options.task || this.task;
        const format = options.format || this.format;

        // Test Whisper installation first
        console.log(`🧪 Testing Whisper installation before transcription...`);
        const whisperWorking = await this.testWhisperInstallation();
        if (!whisperWorking) {
            throw new Error('Whisper is not installed or not working. Please install with: pip install openai-whisper');
        }

        return new Promise((resolve, reject) => {
            console.log(`🎤 Starting transcription with model: ${model}`);

            // Build command arguments
            const args = [
                this.transcriptPath,
                audioPath,
                '--model', model,
                '--format', format,
                '--output', this.transcriptDir
            ];

            if (language) {
                args.push('--language', language);
            }

            if (task !== 'transcribe') {
                args.push('--task', task);
            }

            console.log(`🔧 Command: ${this.pythonPath} ${args.join(' ')}`);
            console.log(`🔧 Working directory: ${process.cwd()}`);
            console.log(`🔧 Audio file exists: ${require('fs').existsSync(audioPath)}`);
            console.log(`🔧 Python path exists: ${require('fs').existsSync(this.pythonPath)}`);
            console.log(`🔧 Transcript script exists: ${require('fs').existsSync(this.transcriptPath)}`);
            console.log(`🔧 FFmpeg path: ${ffmpegPath}`);
            console.log(`🔧 FFmpeg exists: ${require('fs').existsSync(ffmpegPath)}`);

            // Set FFmpeg path in environment for Python process
            const env = {
                ...process.env,
                FFMPEG_BINARY: ffmpegPath
            };

            let childProcess;
            try {
                childProcess = spawn(this.pythonPath, args, {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    timeout: 60000, // 60 second timeout
                    env: env
                });
                console.log(`🚀 Process spawned with PID: ${childProcess.pid}`);
            } catch (spawnError) {
                console.error(`❌ Failed to spawn process: ${spawnError.message}`);
                reject(new Error(`Failed to spawn process: ${spawnError.message}`));
                return;
            }

            let stdout = '';
            let stderr = '';

            childProcess.stdout.on('data', (data) => {
                stdout += data.toString();
                console.log(`📝 Whisper: ${data.toString().trim()}`);
            });

            childProcess.stderr.on('data', (data) => {
                stderr += data.toString();
                console.error(`⚠️ Whisper stderr: ${data.toString().trim()}`);
            });

            childProcess.on('close', async (code) => {
                console.log(`[whisper-integration] 🔍 Process closed with code: ${code}`);
                console.log(`[whisper-integration] 📝 Stdout length: ${stdout.length}`);
                console.log(`[whisper-integration] ⚠️ Stderr length: ${stderr.length}`);
                console.log(`[whisper-integration] 📝 Full stdout: ${stdout}`);
                console.log(`[whisper-integration] ⚠️ Full stderr: ${stderr}`);

                // If code is null, the process was terminated abnormally
                if (code === null) {
                    console.error(`❌ Process was terminated abnormally (code: null)`);
                    console.error(`❌ This usually means Python crashed or was killed`);
                    console.error(`❌ Check if Whisper is installed: pip install openai-whisper`);
                }

                if (code === 0) {
                    try {
                        // Read the generated transcript file
                        const audioFilename = path.basename(audioPath, path.extname(audioPath));
                        const transcriptPath = path.join(this.transcriptDir, `${audioFilename}.${format}`);
                        
                        if (await fs.pathExists(transcriptPath)) {
                            const transcriptContent = await fs.readFile(transcriptPath, 'utf8');
                            
                            let result;
                            if (format === 'json') {
                                result = JSON.parse(transcriptContent);
                            } else {
                                result = { text: transcriptContent };
                            }
                            
                            console.log(`✅ Transcription completed successfully`);
                            resolve(result);
                            
                            // Clean up transcript file
                            await fs.remove(transcriptPath);
                        } else {
                            reject(new Error('Transcript file not found'));
                        }
                    } catch (error) {
                        reject(new Error(`Failed to read transcript: ${error.message}`));
                    }
                } else {
                    reject(new Error(`Transcription failed with code ${code}: ${stderr}`));
                }
            });

            childProcess.on('error', (error) => {
                reject(new Error(`Process error: ${error.message}`));
            });
        });
    }

    /**
     * Clean up temporary files
     */
    async cleanup(audioPath) {
        try {
            if (await fs.pathExists(audioPath)) {
                await fs.remove(audioPath);
                console.log(`🗑️ Cleaned up: ${audioPath}`);
            }
        } catch (error) {
            console.error(`⚠️ Cleanup error: ${error.message}`);
        }
    }

    /**
     * Main transcription function
     */
    async transcribeVoiceMessage(attachment, messageId, options = {}) {
        console.log(`[whisper-integration] 🚀 Starting transcription for messageId: ${messageId}`);
        let audioPath = null;

        try {
            // Validate attachment
            if (!attachment || !attachment.url) {
                throw new Error('Invalid attachment');
            }
            
            // Check file size (Discord limit is 25MB)
            if (attachment.size > 25 * 1024 * 1024) {
                throw new Error('File too large (max 25MB)');
            }
            
            // Download the voice message
            audioPath = await this.downloadVoiceMessage(attachment, messageId);
            
            // Transcribe the audio
            const result = await this.transcribeAudio(audioPath, options);
            
            // Clean up the downloaded file
            await this.cleanup(audioPath);
            
            return {
                success: true,
                text: result.text,
                language: result.language || 'unknown',
                segments: result.segments || null,
                duration: result.segments ? 
                    result.segments[result.segments.length - 1]?.end : null
            };
            
        } catch (error) {
            // Clean up on error
            if (audioPath) {
                await this.cleanup(audioPath);
            }
            
            console.error(`❌ Transcription error: ${error.message}`);
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get available models
     */
    getAvailableModels() {
        return ['tiny', 'base', 'small', 'medium', 'large'];
    }

    /**
     * Get supported languages (common ones)
     */
    getSupportedLanguages() {
        return {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese',
            'ar': 'Arabic',
            'hi': 'Hindi'
        };
    }
}

// Create default instance
const whisperIntegration = new WhisperIntegration();

// Export functions for use in Discord bot
module.exports = {
    WhisperIntegration,
    transcribeVoiceMessage: (attachment, messageId, options) => 
        whisperIntegration.transcribeVoiceMessage(attachment, messageId, options),
    getAvailableModels: () => whisperIntegration.getAvailableModels(),
    getSupportedLanguages: () => whisperIntegration.getSupportedLanguages()
};
