/**
 * Event-Driven Discovery Rank System
 * 
 * Phase 3.3: Implements microservices architecture with event bus
 * for infinite scalability and non-blocking discovery rank calculations.
 * 
 * Features:
 * - Asynchronous event-driven discovery rank updates
 * - Microservices architecture with separate services
 * - Event bus for decoupled communication
 * - Queue-based processing with retry logic
 * - Real-time UI updates via WebSocket/events
 * - Horizontal scaling across multiple instances
 */

const EventEmitter = require('events');
const { Worker } = require('worker_threads');
const path = require('path');

// Event types for discovery rank system
const DISCOVERY_EVENTS = {
    ITEM_DROPPED: 'item.dropped',
    RANK_CALCULATION_REQUESTED: 'rank.calculation.requested',
    RANK_CALCULATION_COMPLETED: 'rank.calculation.completed',
    RANK_CALCULATION_FAILED: 'rank.calculation.failed',
    CACHE_INVALIDATION_REQUESTED: 'cache.invalidation.requested',
    CACHE_UPDATED: 'cache.updated',
    UI_UPDATE_REQUESTED: 'ui.update.requested'
};

// Service configuration
const SERVICE_CONFIG = {
    maxWorkers: process.env.DISCOVERY_WORKERS || 4,
    queueMaxSize: 10000,
    retryAttempts: 3,
    retryDelay: 1000, // 1 second base delay
    processingTimeout: 30000, // 30 seconds
    batchSize: 50 // Process items in batches
};

class EventDrivenDiscoverySystem extends EventEmitter {
    constructor() {
        super();
        this.setMaxListeners(0); // Remove listener limit for scalability
        
        // Service components
        this.discoveryWorkers = [];
        this.processingQueue = [];
        this.processingInProgress = new Map();
        this.retryQueue = [];
        
        // Performance metrics
        this.metrics = {
            eventsProcessed: 0,
            eventsQueued: 0,
            eventsFailed: 0,
            eventsRetried: 0,
            avgProcessingTime: 0,
            queueSize: 0,
            workersActive: 0,
            startTime: Date.now()
        };
        
        this.initializeServices();
        this.startEventProcessing();
    }

    /**
     * Initialize discovery rank worker services
     */
    async initializeServices() {
        console.log('[EventDiscovery] 🔄 Initializing event-driven discovery services...');
        
        // Create worker threads for parallel processing
        for (let i = 0; i < SERVICE_CONFIG.maxWorkers; i++) {
            try {
                const worker = new Worker(path.join(__dirname, 'discoveryWorker.js'));
                
                worker.on('message', (result) => {
                    this.handleWorkerResult(result);
                });
                
                worker.on('error', (error) => {
                    console.error(`[EventDiscovery] ❌ Worker ${i} error:`, error);
                    this.metrics.eventsFailed++;
                });
                
                worker.on('exit', (code) => {
                    if (code !== 0) {
                        console.error(`[EventDiscovery] ❌ Worker ${i} exited with code ${code}`);
                    }
                });
                
                this.discoveryWorkers.push(worker);
                console.log(`[EventDiscovery] ✅ Discovery worker ${i} initialized`);
                
            } catch (error) {
                console.error(`[EventDiscovery] ❌ Failed to create worker ${i}:`, error);
            }
        }
        
        console.log(`[EventDiscovery] 🚀 ${this.discoveryWorkers.length} discovery workers ready`);
        
        // Set up event listeners
        this.setupEventListeners();
    }

    /**
     * Set up event listeners for discovery rank system
     */
    setupEventListeners() {
        // Item drop event - trigger discovery rank calculation
        this.on(DISCOVERY_EVENTS.ITEM_DROPPED, async (eventData) => {
            await this.handleItemDropped(eventData);
        });
        
        // Rank calculation request - queue for processing
        this.on(DISCOVERY_EVENTS.RANK_CALCULATION_REQUESTED, async (eventData) => {
            await this.queueRankCalculation(eventData);
        });
        
        // Rank calculation completed - update caches and UI
        this.on(DISCOVERY_EVENTS.RANK_CALCULATION_COMPLETED, async (eventData) => {
            await this.handleRankCalculationCompleted(eventData);
        });
        
        // Cache invalidation - clear relevant caches
        this.on(DISCOVERY_EVENTS.CACHE_INVALIDATION_REQUESTED, async (eventData) => {
            await this.handleCacheInvalidation(eventData);
        });
        
        console.log('[EventDiscovery] 📡 Event listeners configured');
    }

    /**
     * Handle item dropped event
     */
    async handleItemDropped(eventData) {
        const { userId, itemData, guildId, timestamp } = eventData;
        
        console.log(`[EventDiscovery] 🎁 Item dropped: ${itemData.itemName} for ${userId}`);
        
        // Emit rank calculation request
        this.emit(DISCOVERY_EVENTS.RANK_CALCULATION_REQUESTED, {
            requestId: this.generateRequestId(),
            userId,
            itemData,
            guildId,
            timestamp,
            priority: 'normal'
        });
        
        // Emit cache invalidation for affected items
        this.emit(DISCOVERY_EVENTS.CACHE_INVALIDATION_REQUESTED, {
            itemName: itemData.itemName,
            guildId,
            scope: 'discovery_ranks'
        });
    }

    /**
     * Queue discovery rank calculation for processing
     */
    async queueRankCalculation(eventData) {
        if (this.processingQueue.length >= SERVICE_CONFIG.queueMaxSize) {
            console.error('[EventDiscovery] ❌ Processing queue full, dropping request');
            this.metrics.eventsFailed++;
            return;
        }
        
        const queueItem = {
            ...eventData,
            queuedAt: Date.now(),
            attempts: 0
        };
        
        this.processingQueue.push(queueItem);
        this.metrics.eventsQueued++;
        this.metrics.queueSize = this.processingQueue.length;
        
        console.log(`[EventDiscovery] 📋 Queued rank calculation for ${eventData.itemData.itemName} (queue: ${this.processingQueue.length})`);
    }

    /**
     * Start event processing loop
     */
    startEventProcessing() {
        setInterval(async () => {
            await this.processQueueBatch();
        }, 100); // Process every 100ms
        
        setInterval(async () => {
            await this.processRetryQueue();
        }, 5000); // Process retries every 5 seconds
        
        console.log('[EventDiscovery] 🔄 Event processing started');
    }

    /**
     * Process a batch of queued items
     */
    async processQueueBatch() {
        if (this.processingQueue.length === 0) {
            return;
        }
        
        const availableWorkers = this.discoveryWorkers.filter(worker => !worker.busy);
        if (availableWorkers.length === 0) {
            return;
        }
        
        const batchSize = Math.min(
            SERVICE_CONFIG.batchSize,
            this.processingQueue.length,
            availableWorkers.length
        );
        
        const batch = this.processingQueue.splice(0, batchSize);
        this.metrics.queueSize = this.processingQueue.length;
        
        for (let i = 0; i < batch.length; i++) {
            const item = batch[i];
            const worker = availableWorkers[i];
            
            if (worker) {
                await this.processWithWorker(worker, item);
            }
        }
    }

    /**
     * Process item with worker thread
     */
    async processWithWorker(worker, item) {
        const startTime = Date.now();
        
        try {
            worker.busy = true;
            this.metrics.workersActive++;
            
            this.processingInProgress.set(item.requestId, {
                item,
                worker,
                startTime
            });
            
            // Send work to worker
            worker.postMessage({
                type: 'CALCULATE_DISCOVERY_RANK',
                data: item
            });
            
            console.log(`[EventDiscovery] 🔄 Processing ${item.itemData.itemName} with worker`);
            
        } catch (error) {
            console.error('[EventDiscovery] ❌ Error processing with worker:', error);
            worker.busy = false;
            this.metrics.workersActive--;
            this.handleProcessingFailure(item, error);
        }
    }

    /**
     * Handle worker result
     */
    handleWorkerResult(result) {
        const { requestId, success, data, error } = result;
        const processing = this.processingInProgress.get(requestId);
        
        if (!processing) {
            console.error(`[EventDiscovery] ❌ Unknown request ID: ${requestId}`);
            return;
        }
        
        const { item, worker } = processing;
        const processingTime = Date.now() - processing.startTime;
        
        // Update metrics
        this.updateProcessingMetrics(processingTime);
        worker.busy = false;
        this.metrics.workersActive--;
        this.processingInProgress.delete(requestId);
        
        if (success) {
            console.log(`[EventDiscovery] ✅ Rank calculation completed for ${item.itemData.itemName} (${processingTime}ms)`);
            
            // Emit completion event
            this.emit(DISCOVERY_EVENTS.RANK_CALCULATION_COMPLETED, {
                ...item,
                discoveryRanks: data,
                processingTime
            });
            
            this.metrics.eventsProcessed++;
        } else {
            console.error(`[EventDiscovery] ❌ Rank calculation failed for ${item.itemData.itemName}:`, error);
            this.handleProcessingFailure(item, error);
        }
    }

    /**
     * Handle processing failure with retry logic
     */
    handleProcessingFailure(item, error) {
        item.attempts++;
        item.lastError = error;
        
        if (item.attempts < SERVICE_CONFIG.retryAttempts) {
            // Add to retry queue with exponential backoff
            const retryDelay = SERVICE_CONFIG.retryDelay * Math.pow(2, item.attempts - 1);
            item.retryAt = Date.now() + retryDelay;
            
            this.retryQueue.push(item);
            this.metrics.eventsRetried++;
            
            console.log(`[EventDiscovery] 🔄 Queued retry for ${item.itemData.itemName} (attempt ${item.attempts}/${SERVICE_CONFIG.retryAttempts})`);
        } else {
            console.error(`[EventDiscovery] ❌ Max retries exceeded for ${item.itemData.itemName}`);
            this.metrics.eventsFailed++;
            
            // Emit failure event
            this.emit(DISCOVERY_EVENTS.RANK_CALCULATION_FAILED, {
                ...item,
                finalError: error
            });
        }
    }

    /**
     * Process retry queue
     */
    async processRetryQueue() {
        const now = Date.now();
        const readyItems = this.retryQueue.filter(item => item.retryAt <= now);
        
        if (readyItems.length === 0) {
            return;
        }
        
        // Remove ready items from retry queue
        this.retryQueue = this.retryQueue.filter(item => item.retryAt > now);
        
        // Add back to processing queue
        for (const item of readyItems) {
            this.processingQueue.push(item);
            console.log(`[EventDiscovery] 🔄 Retrying ${item.itemData.itemName} (attempt ${item.attempts})`);
        }
        
        this.metrics.queueSize = this.processingQueue.length;
    }

    /**
     * Handle rank calculation completion
     */
    async handleRankCalculationCompleted(eventData) {
        const { userId, itemData, discoveryRanks, guildId } = eventData;
        
        // Update caches with new discovery ranks
        this.emit(DISCOVERY_EVENTS.CACHE_UPDATED, {
            itemName: itemData.itemName,
            userId,
            guildId,
            discoveryRanks
        });
        
        // Request UI update
        this.emit(DISCOVERY_EVENTS.UI_UPDATE_REQUESTED, {
            userId,
            itemData,
            discoveryRanks,
            guildId
        });
        
        console.log(`[EventDiscovery] 📊 Discovery ranks updated for ${itemData.itemName}: ${discoveryRanks.guildRank}/${discoveryRanks.guildTotal} server, ${discoveryRanks.globalRank}/${discoveryRanks.globalTotal} global`);
    }

    /**
     * Handle cache invalidation
     */
    async handleCacheInvalidation(eventData) {
        const { itemName, guildId, scope } = eventData;
        
        // Clear relevant caches
        const { redisDiscoveryCache } = require('./redisCache.js');
        const { clearDiscoveryCache } = require('./discoveryRanks.js');
        
        if (scope === 'discovery_ranks') {
            // Clear specific item cache
            const cacheKey = `discovery:${itemName}:*:${guildId || 'global'}`;
            await redisDiscoveryCache.clear(cacheKey);
            
            console.log(`[EventDiscovery] 🗑️ Cache invalidated for ${itemName} in ${guildId || 'global'}`);
        }
    }

    /**
     * Update processing metrics
     */
    updateProcessingMetrics(processingTime) {
        const currentAvg = this.metrics.avgProcessingTime;
        const processed = this.metrics.eventsProcessed;
        
        this.metrics.avgProcessingTime = processed > 0 
            ? ((currentAvg * processed) + processingTime) / (processed + 1)
            : processingTime;
    }

    /**
     * Generate unique request ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get comprehensive event system statistics
     */
    getEventSystemStats() {
        const uptime = Date.now() - this.metrics.startTime;
        const totalEvents = this.metrics.eventsProcessed + this.metrics.eventsFailed;
        const successRate = totalEvents > 0 ? (this.metrics.eventsProcessed / totalEvents) * 100 : 0;
        
        return {
            // Processing metrics
            eventsProcessed: this.metrics.eventsProcessed,
            eventsQueued: this.metrics.eventsQueued,
            eventsFailed: this.metrics.eventsFailed,
            eventsRetried: this.metrics.eventsRetried,
            successRate: successRate.toFixed(1) + '%',
            
            // Performance metrics
            avgProcessingTime: this.metrics.avgProcessingTime.toFixed(2) + 'ms',
            queueSize: this.metrics.queueSize,
            retryQueueSize: this.retryQueue.length,
            
            // Worker metrics
            totalWorkers: this.discoveryWorkers.length,
            workersActive: this.metrics.workersActive,
            workerUtilization: ((this.metrics.workersActive / this.discoveryWorkers.length) * 100).toFixed(1) + '%',
            
            // System info
            uptime: Math.round(uptime / 1000) + 's',
            architecture: 'Event-Driven Microservices',
            maxQueueSize: SERVICE_CONFIG.queueMaxSize,
            batchSize: SERVICE_CONFIG.batchSize
        };
    }

    /**
     * Graceful shutdown
     */
    async shutdown() {
        console.log('[EventDiscovery] 🔄 Shutting down event-driven discovery system...');
        
        // Terminate all workers
        for (const worker of this.discoveryWorkers) {
            await worker.terminate();
        }
        
        console.log('[EventDiscovery] ✅ All workers terminated');
        console.log('[EventDiscovery] ✅ Event-driven system shutdown complete');
    }
}

// Create singleton instance
const eventDrivenDiscoverySystem = new EventDrivenDiscoverySystem();

module.exports = {
    EventDrivenDiscoverySystem,
    eventDrivenDiscoverySystem,
    DISCOVERY_EVENTS
};
