/**
 * ITEMS SYSTEM FOCUSED TESTING
 * Uses shared BotTestBase for comprehensive items system validation
 * 
 * This test focuses specifically on the items system functionality,
 * including item creation, drops, inventory, and database operations.
 */

const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class ItemsSystemTester extends BotTestBase {
    constructor() {
        super('ItemsSystemTest');
    }

    /**
     * Test items system initialization
     */
    async testItemsSystemInitialization() {
        const items = require('../commands/utility/items.js');
        const interaction = this.createMockInteraction(2, 'items'); // Slash command
        interaction.options = {
            getString: () => null
        };
        
        await items.execute(interaction);
        
        console.log(`   Items initialization responses: ${interaction._responses.length}`);
        
        // Validate response structure
        const lastResponse = interaction._responses[interaction._responses.length - 1];
        if (lastResponse && lastResponse.options.components) {
            console.log(`   Components in response: ${lastResponse.options.components.length}`);
        }
        
        return interaction._responses.length > 0;
    }

    /**
     * Test items database structure and content
     */
    async testItemsDatabase() {
        const itemsCol = this.mongoClient.db('test').collection('custom_items');
        
        // Count total items
        const totalItems = await itemsCol.countDocuments();
        console.log(`   Total items in database: ${totalItems}`);
        
        // Count items by rarity
        const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary', 'mythic'];
        for (const rarity of rarities) {
            const count = await itemsCol.countDocuments({ rarity });
            if (count > 0) {
                console.log(`   ${rarity} items: ${count}`);
            }
        }
        
        // Check drop locations
        const dropLocations = ['TEXT', 'VOICE', 'LEVEL_UP', 'STARFALL'];
        for (const location of dropLocations) {
            const count = await itemsCol.countDocuments({ 
                dropLocations: location,
                disabled: { $ne: true }
            });
            console.log(`   ${location} drops: ${count}`);
        }
        
        return totalItems > 0;
    }

    /**
     * Test item creation workflow
     */
    async testItemCreation() {
        const items = require('../commands/utility/items.js');
        
        // Test create item select
        const selectInteraction = this.createMockInteraction(5, 'items-select', ['create']);
        await items.select(selectInteraction, []);
        
        console.log(`   Create select responses: ${selectInteraction._responses.length}`);
        
        // Test item type selection
        const typeInteraction = this.createMockInteraction(5, 'items-create-type-select', ['weapon']);
        await items.select(typeInteraction, []);
        
        console.log(`   Type select responses: ${typeInteraction._responses.length}`);
        
        return selectInteraction._responses.length > 0 && typeInteraction._responses.length > 0;
    }

    /**
     * Test item modal submissions
     */
    async testItemModalSubmissions() {
        const items = require('../commands/utility/items.js');
        
        // Test item creation modal
        const modalInteraction = this.createMockInteraction(6, 'items-create-modal', {
            'item-name': 'Test Sword',
            'item-description': 'A test weapon for validation',
            'item-parameter1': '100',
            'item-parameter2': '50'
        });
        
        try {
            await items.modalSubmit(modalInteraction);
            console.log(`   Modal submission responses: ${modalInteraction._responses.length}`);
        } catch (error) {
            console.log(`   Modal submission error (expected in test): ${error.message}`);
        }
        
        return true; // Modal submission might fail in test environment, that's OK
    }

    /**
     * Test item inventory system
     */
    async testItemInventory() {
        const inventoryCol = this.mongoClient.db('test').collection('user_inventories');
        
        // Check if test user has inventory
        const userInventory = await inventoryCol.findOne({ 
            userId: this.testUser.id,
            guildId: this.testGuild.id
        });
        
        console.log(`   User has inventory: ${!!userInventory}`);
        
        if (userInventory) {
            const itemCount = userInventory.items ? userInventory.items.length : 0;
            console.log(`   Items in inventory: ${itemCount}`);
            
            if (itemCount > 0) {
                const firstItem = userInventory.items[0];
                console.log(`   First item: ${firstItem.name} (${firstItem.rarity})`);
            }
        }
        
        return true;
    }

    /**
     * Test item drop system
     */
    async testItemDropSystem() {
        const { rollForItemDrop } = require('../utils/itemDrops.js');
        
        try {
            // Test drop roll for different locations
            const dropLocations = ['TEXT', 'VOICE', 'LEVEL_UP'];
            
            for (const location of dropLocations) {
                const dropResult = await rollForItemDrop(
                    this.testUser.id,
                    this.testGuild.id,
                    location,
                    1 // Force drop for testing
                );
                
                console.log(`   ${location} drop test: ${dropResult ? 'Success' : 'No drop'}`);
            }
            
            return true;
        } catch (error) {
            console.log(`   Drop system error: ${error.message}`);
            return false;
        }
    }

    /**
     * Test item caching system
     */
    async testItemCaching() {
        const { getCachedItems } = require('../utils/itemCache.js');
        
        try {
            const cachedItems = await getCachedItems(this.testGuild.id);
            console.log(`   Cached items retrieved: ${cachedItems ? cachedItems.length : 0}`);
            
            if (cachedItems && cachedItems.length > 0) {
                const firstItem = cachedItems[0];
                console.log(`   First cached item: ${firstItem.name}`);
            }
            
            return true;
        } catch (error) {
            console.log(`   Item cache error: ${error.message}`);
            return false;
        }
    }

    /**
     * Test item rarity system
     */
    async testItemRaritySystem() {
        const { RARITIES } = require('../commands/utility/items.js');
        
        console.log(`   Rarity tiers defined: ${Object.keys(RARITIES).length}`);
        
        // Validate rarity structure
        for (const [rarityName, rarityData] of Object.entries(RARITIES)) {
            const hasRequiredFields = rarityData.color !== undefined && 
                                    rarityData.weight !== undefined &&
                                    rarityData.emoji !== undefined;
            
            if (!hasRequiredFields) {
                console.log(`   ❌ Rarity ${rarityName} missing required fields`);
                return false;
            }
        }
        
        console.log(`   All rarities have required fields`);
        return true;
    }

    /**
     * Test item permissions and access control
     */
    async testItemPermissions() {
        const items = require('../commands/utility/items.js');
        
        // Test owner access
        const ownerInteraction = this.createMockInteraction(5, 'items-select', ['create']);
        ownerInteraction.user.id = process.env.OWNER; // Set as owner
        
        await items.select(ownerInteraction, []);
        console.log(`   Owner access responses: ${ownerInteraction._responses.length}`);
        
        // Test regular user access
        const userInteraction = this.createMockInteraction(5, 'items-select', ['create']);
        userInteraction.user.id = 'regular-user-id';
        
        await items.select(userInteraction, []);
        console.log(`   Regular user responses: ${userInteraction._responses.length}`);
        
        return ownerInteraction._responses.length > 0;
    }

    /**
     * Run all items system tests
     */
    async runAllTests() {
        console.log('🚀 Starting Items System focused testing...\n');
        console.log('=' .repeat(60));
        console.log('TESTING ITEMS SYSTEM WITH REAL DATABASE');
        console.log('=' .repeat(60));

        // Define items-specific tests
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Items system initialization', test: () => this.testItemsSystemInitialization() },
            { name: 'Items database structure', test: () => this.testItemsDatabase() },
            { name: 'Item creation workflow', test: () => this.testItemCreation() },
            { name: 'Item modal submissions', test: () => this.testItemModalSubmissions() },
            { name: 'Item inventory system', test: () => this.testItemInventory() },
            { name: 'Item drop system', test: () => this.testItemDropSystem() },
            { name: 'Item caching system', test: () => this.testItemCaching() },
            { name: 'Item rarity system', test: () => this.testItemRaritySystem() },
            { name: 'Item permissions', test: () => this.testItemPermissions() }
        ];

        // Run all tests using the base class helper
        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        // Print comprehensive results
        this.printResults();
        
        // Return overall success status
        const passedTests = this.testResults.filter(r => r.passed).length;
        return passedTests === this.testResults.length;
    }
}

// Main execution function
async function runItemsSystemTest() {
    const tester = new ItemsSystemTester();
    
    try {
        // Validate environment
        tester.validateEnvironment();
        
        // Initialize test environment
        await tester.initialize();
        
        // Run all items tests
        const allPassed = await tester.runAllTests();
        
        // Exit with appropriate code
        process.exit(allPassed ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Items system test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Run if this file is executed directly
if (require.main === module) {
    runItemsSystemTest();
}

module.exports = { ItemsSystemTester, runItemsSystemTest };
