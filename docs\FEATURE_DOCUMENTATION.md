# 🤖 Discord Bot Feature Documentation

This document provides comprehensive documentation for all bot features, including functionality, internal logic, testing requirements, and usage patterns.

## 📋 Table of Contents

1. [Core Commands](#core-commands)
   - [/17 - Main Bot Interface](#17---main-bot-interface)
   - [/you - User Profile System](#you---user-profile-system)
2. [Experience System](#experience-system)
3. [Items System](#items-system)
4. [Owner Administration](#owner-administration)
5. [Utility Features](#utility-features)
6. [Specialized Systems](#specialized-systems)

## 📚 Detailed Documentation

For comprehensive documentation of each feature, see the dedicated files:

- **[EXP System](features/EXP_SYSTEM.md)** - Complete EXP system documentation
- **[Items System](features/ITEMS_SYSTEM.md)** - Items management and drop mechanics
- **[Owner Administration](features/OWNER_ADMINISTRATION.md)** - Administrative functions and security
- **[/you Command System](features/YOU_COMMAND_SYSTEM.md)** - User profile and transcription features
- **[Utility Features](features/UTILITY_FEATURES.md)** - Logs, sticky, dehoist, opener, and lookup systems
- **[Specialized Systems](features/SPECIALIZED_SYSTEMS.md)** - Starfall, global levels, and transcription

---

## Core Commands

### /17 - Main Bot Interface

**Overview**: The primary entry point for all bot functionality. Provides a centralized hub with feature navigation and usage tracking.

**Key Functions**:
- `execute()` - Main slash command handler
- `select()` - Feature navigation via select menu
- Feature usage counter increment on every navigation

**Special Behaviors**:
- **Usage Tracking**: Increments counter on every interface navigation, not just command execution
- **Feature Menu Integration**: Dynamically hides current page from dropdown options
- **Components v2 Compliance**: Uses MessageFlags.IsComponentsV2 without content field

**Internal Logic**:
- Command invalidation system prevents spam
- Cached guild configuration for performance
- Feature menu state preservation during navigation

**Testing Requirements**:
- Slash command execution
- Select menu navigation
- Usage counter incrementation
- Performance benchmarking (should complete <2 seconds)

---

### /you - User Profile System

**Overview**: Comprehensive user profile interface showing EXP, levels, items, starfall rewards, and transcription features.

**Key Functions**:
- `execute()` - Main profile display
- `select()` - Profile section navigation
- `buttons()` - Action button handlers
- `modalSubmit()` - Settings modification

**Special Behaviors**:
- **Multi-Section Interface**: EXP, items, starfall, settings in cascading menus
- **Transcription Integration**: Voice message processing with Python/Whisper
- **Global vs Guild Data**: Seamless switching between server and global statistics
- **Demo Mode Support**: Shows fake data when no real data exists

**Internal Logic**:
- Optimized database queries with caching
- Parallel data fetching for performance
- Error handling for transcription service failures
- Settings persistence across sessions

**Testing Requirements**:
- Profile data display accuracy
- Transcription functionality (if Python available)
- Navigation between sections
- Settings modal submissions
- Performance optimization validation

---

## Experience System

**Overview**: Comprehensive leveling system supporting both guild-specific and global EXP tracking with customizable levels and rewards.

**Key Functions**:
- `execute()` - EXP system interface
- `select()` - Level management navigation
- `buttons()` - Level creation/editing actions
- `modalSubmit()` - Level configuration forms

**Special Behaviors**:
- **Cascading Select Menus**: First menu selects action, second menu selects specific level
- **Data Persistence**: Maintains form data between select menu interactions
- **Permission-Based Access**: Different views for admins vs regular users
- **Demo Data Fallback**: Shows example levels when none configured

**Internal Logic**:
- **Modal State Clearing**: Explicitly clears input fields to prevent leftover state
- **Level Validation**: Ensures EXP requirements are numeric and progressive
- **Cache Integration**: Uses expCache.js for performance optimization
- **Database Optimization**: Batch operations for level updates

**Testing Requirements**:
- Level creation workflow
- Modal form validation
- Permission boundary testing
- Cache system functionality
- Demo data display accuracy

---

## Items System

**Overview**: Custom item management with creation, editing, inventory tracking, and drop mechanics across multiple locations.

**Key Functions**:
- `execute()` - Items interface
- `select()` - Item management navigation
- `buttons()` - Item actions (create, edit, delete)
- `modalSubmit()` - Item configuration forms

**Special Behaviors**:
- **Multi-Step Creation**: Type selection → Icon selection → Configuration modal
- **Drop Location System**: Items can drop from TEXT, VOICE, LEVEL_UP, STARFALL
- **Rarity System**: Six tiers (common to mythic) with weights and colors
- **Image Upload Integration**: Unified image uploader for custom icons

**Internal Logic**:
- **Rarity Validation**: Ensures proper rarity structure and weights
- **Drop Rate Calculations**: Probabilistic item distribution
- **Inventory Management**: User-specific item collections per guild
- **Cache Optimization**: Item data cached for performance

**Testing Requirements**:
- Item creation workflow
- Drop system mechanics
- Inventory management
- Rarity system validation
- Image upload functionality

---

## Owner Administration

**Overview**: Comprehensive administrative interface for bot management, accessible only to the bot owner.

**Key Functions**:
- `execute()` - Owner panel access
- `select()` - Administrative feature navigation
- `buttons()` - Administrative actions
- `modalSubmit()` - Configuration forms

**Special Behaviors**:
- **Owner-Only Access**: Strict permission checking with fallback messages
- **Modular Architecture**: Separate files for different admin functions
- **Server Management**: List and manage all guilds the bot is in
- **Global Systems**: Manage global levels, items, and bot status

**Internal Logic**:
- **Permission Validation**: Multiple layers of owner verification
- **Status Message Pattern**: Uses status messages instead of ephemeral replies
- **Modular Loading**: Dynamic require() for feature-specific modules
- **Error Containment**: Graceful handling of admin operation failures

**Testing Requirements**:
- Owner permission validation
- Administrative function execution
- Server management operations
- Global system modifications
- Error handling for unauthorized access

---

## Utility Features

### Logs System
**Overview**: Event logging and audit trail management for server activities.

**Key Functions**:
- Event type configuration
- Log channel setup
- Message formatting and delivery

**Testing Requirements**:
- Log configuration interface
- Event filtering and routing
- Message formatting accuracy

### Sticky System
**Overview**: Persistent nickname and role management that survives user rejoins.

**Key Functions**:
- Nickname persistence
- Role recovery
- Configuration management

**Testing Requirements**:
- Nickname/role persistence
- Recovery on user rejoin
- Configuration interface

### Dehoist System
**Overview**: Automatic removal of special characters from usernames to prevent display manipulation.

**Key Functions**:
- Username scanning
- Character filtering
- Bulk processing

**Testing Requirements**:
- Character detection accuracy
- Bulk operation performance
- Configuration options

### Opener System
**Overview**: Thread management and auto-bumping for maintaining active discussions.

**Key Functions**:
- Thread monitoring
- Auto-bump scheduling
- Activity tracking

**Testing Requirements**:
- Thread detection and monitoring
- Bump timing accuracy
- Activity threshold management

### Lookup System
**Overview**: Public user profile lookup for social sharing and stat comparison.

**Key Functions**:
- User profile display
- Server information lookup
- EXP and level statistics
- Performance-optimized caching

**Testing Requirements**:
- Public access validation
- Self-lookup prevention
- Performance benchmarks
- Data accuracy verification

---

## Specialized Systems

### Starfall Daily Rewards
**Overview**: Daily reward system with timezone handling and real-time updates.

**Key Functions**:
- Daily reward calculation
- Timezone management
- Streak tracking

**Testing Requirements**:
- Daily reset timing
- Timezone accuracy
- Streak calculation

### Global Levels System
**Overview**: Cross-server EXP tracking and leaderboards.

**Key Functions**:
- Global EXP accumulation
- Cross-server leaderboards
- Achievement tracking

**Testing Requirements**:
- EXP synchronization
- Leaderboard accuracy
- Achievement triggers

### Transcription System
**Overview**: Voice message transcription using Python and OpenAI Whisper.

**Key Functions**:
- Audio file processing
- Python integration
- Transcription delivery

**Testing Requirements**:
- Audio file handling
- Python service availability
- Transcription accuracy

---

## 🧪 Testing Guidelines

### General Testing Patterns
1. **Use Shared Test Base**: All tests should extend `BotTestBase`
2. **Mock Interactions**: Create realistic Discord interaction objects
3. **Database Validation**: Test with live MongoDB connections
4. **Performance Benchmarks**: Validate response times <2 seconds
5. **Error Scenarios**: Test permission failures and invalid inputs

### Feature-Specific Test Requirements
- **EXP System**: Level creation, modal validation, cache functionality
- **Items System**: Creation workflow, drop mechanics, inventory management
- **Owner System**: Permission boundaries, administrative functions
- **Utility Features**: Configuration interfaces, bulk operations
- **Specialized Systems**: Timing accuracy, external service integration

### Test Coverage Standards
- Every interaction type (slash, button, select, modal)
- Permission boundary testing
- Error handling validation
- Performance optimization verification
- Database operation accuracy

---

## 📝 Development Notes

### Code Patterns
- **Components v2**: All responses use MessageFlags.IsComponentsV2
- **Status Messages**: Prefer status messages over ephemeral replies
- **Modular Architecture**: Separate files for complex features
- **Cache Integration**: Use feature-specific cache utilities
- **Error Handling**: Graceful degradation with user feedback

### Performance Considerations
- Database query optimization
- Parallel data fetching
- Cache utilization
- Response time monitoring
- Resource cleanup

### Maintenance Requirements
- Regular cache invalidation
- Database index optimization
- Error log monitoring
- Performance metric tracking
- Feature usage analytics
