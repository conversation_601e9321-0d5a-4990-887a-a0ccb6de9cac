{"name": "17", "version": "1.0.0", "description": "a bot from ??", "main": "index.js", "scripts": {"test": "node tests/test_bot_comprehensive_refactored.js", "test:legacy": "node tests/test_bot_comprehensive_real.js", "test:interaction": "node tests/test_bot_interaction_comprehensive.js", "test:deep": "node tests/comprehensive-deep-dive-test-suite.js", "test:exp": "node tests/test_exp_system_focused.js", "test:items": "node tests/test_items_system_focused.js", "postinstall": "echo '📦 Dependencies installed! For transcription to work, ensure Python and openai-whisper are installed: pip install openai-whisper'"}, "author": "", "license": "ISC", "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "axios": "^1.6.2", "discord.js": "^14.21.0", "dotenv": "^16.4.5", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^11.2.0", "mongodb": "^6.10.0", "node-fetch": "^3.3.2", "openai-whisper": "^1.0.2"}}