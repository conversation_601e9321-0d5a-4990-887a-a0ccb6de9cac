const { Events, EmbedBuilder, AttachmentBuilder } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne } = require("../utils/database-optimizer.js");
const { defaults } = require("../utils/default_db_structures");
const { sendLogContainer } = require('../utils/sendLog.js');
const { createBulkMessageDeleteContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');

module.exports = {
    name: Events.MessageBulkDelete,
    async execute(client, messages) {
        try {
            if (!messages.size) return;
            const first = messages.first();
        var guildData = await optimizedFindOne("guilds", { id: first.guild.id });
        if (guildData == null) {
            await optimizedInsertOne("guilds", defaults.guild(first.guild.id));
            guildData = await optimizedFindOne("guilds", { id: first.guild.id });
        }
        const channels = (guildData.logs.channels.filter(ch => ch.events.includes("messageDeleteBulk")) ?? []).map(l => first.guild.channels.cache.get(l.id)).filter(ch => ch);

        // Only proceed if there are channels configured for this event
        if (channels.length > 0) {
            // Create comprehensive message log for file attachment
            const messageLog = [];
            const messagePreview = [];
            let previewCount = 0;
            const channelsInvolved = new Set();
            const now = Date.now(); // Current time for relative timestamps

            // Sort messages by timestamp (oldest first)
            const sortedMessages = Array.from(messages.values()).sort((a, b) => a.createdTimestamp - b.createdTimestamp);



            console.log(`[messageDeleteBulk] Processing ${sortedMessages.length} messages`);

            for (const message of sortedMessages) {
                console.log(`[messageDeleteBulk] Message: author=${message.author?.tag}, content=${message.content?.length || 0} chars, attachments=${message.attachments?.size || 0}`);
                if (message.author) {
                    // Track channel
                    if (message.channel) {
                        channelsInvolved.add(JSON.stringify({
                            id: message.channelId,
                            name: message.channel.name || 'Unknown',
                            mention: `<#${message.channelId}>`
                        }));
                    }

                    // Create timestamp in readable format
                    const timestamp = new Date(message.createdTimestamp).toLocaleString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });

                    // Full message entry for file with channel info
                    let logEntry = `[${timestamp}] ${message.author.tag} (${message.author.id}) in #${message.channel?.name || 'Unknown'}`;
                    if (message.content) {
                        logEntry += `:\n${message.content}`;
                    } else {
                        logEntry += ': [No text content]';
                    }

                    // Add attachment info if present
                    if (message.attachments.size > 0) {
                        const attachmentList = Array.from(message.attachments.values())
                            .map(att => `  - ${att.name} (${att.url})`)
                            .join('\n');
                        logEntry += `\nAttachments:\n${attachmentList}`;
                    }

                    // Add embeds info if present
                    if (message.embeds.length > 0) {
                        logEntry += `\nEmbeds: ${message.embeds.length} embed(s)`;
                    }

                    messageLog.push(logEntry);

                    // Create preview for container (limited to 5 to save space for file attachment)
                    if (previewCount < 5) {
                        // Use Discord timestamp format with full date/time
                        const discordTimestamp = `<t:${Math.floor(message.createdTimestamp / 1000)}:f>`;
                        let content = message.content;
                        if (!content) {
                            if (message.attachments.size > 0) {
                                content = `[${message.attachments.size} attachment(s)]`;
                            } else if (message.embeds.length > 0) {
                                content = `[${message.embeds.length} embed(s)]`;
                            } else {
                                content = '[No content]';
                            }
                        } else if (content.length > 50) {
                            content = content.substring(0, 50) + '...';
                        }
                        const preview = `${discordTimestamp} **${message.author.tag}** in #${first.channel?.name || 'Unknown'}: ${content}`;
                        messagePreview.push(preview);
                        previewCount++;
                    }
                }
            }

            console.log(`[messageDeleteBulk] Created ${messageLog.length} log entries and ${messagePreview.length} preview entries`);

            // Convert channels set to array for processing
            const channelList = Array.from(channelsInvolved).map(ch => JSON.parse(ch));
            console.log(`[messageDeleteBulk] Messages spanned ${channelList.length} channel(s): ${channelList.map(ch => ch.name).join(', ')}`);

            // Get executor info for filename first (needed for both filename and executor display)
            let executorUsername = 'unknown';
            let executorId = 'unknown';
            let executor = 'Unknown';

            try {
                const auditLogs = await first.guild.fetchAuditLogs({
                    type: 73, // MESSAGE_BULK_DELETE
                    limit: 1
                });
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.executor) {
                    executorUsername = auditEntry.executor.username || auditEntry.executor.tag?.split('#')[0] || 'unknown';
                    executorId = auditEntry.executor.id;
                    executor = `<@${auditEntry.executor.id}> (${auditEntry.executor.tag || auditEntry.executor.username})`;
                }
            } catch (error) {
                console.log('[messageDeleteBulk] Could not fetch audit logs:', error.message);
            }

            // Create text file with all deleted messages (always create if we have any messages)
            let fileComponent = null;
            if (messages.size > 0) {
                // Create channel list for file header
                const channelListText = channelList.length === 1
                    ? `Channel: #${channelList[0].name} (${channelList[0].id})`
                    : `Channels: ${channelList.map(ch => `#${ch.name} (${ch.id})`).join(', ')}`;

                const fileHeader = `Bulk Message Delete Log\n` +
                    `Guild: ${first.guild.name} (${first.guild.id})\n` +
                    `${channelListText}\n` +
                    `Total Messages: ${messages.size}\n` +
                    `Timestamp: ${new Date().toISOString()}\n` +
                    `${'='.repeat(50)}\n\n`;

                const fileContent = fileHeader + (messageLog.length > 0
                    ? messageLog.join('\n\n' + '-'.repeat(30) + '\n\n')
                    : 'No message content could be retrieved (messages may have been uncached or contained only system data).');
                const buffer = Buffer.from(fileContent, 'utf8');

                // Create filename with executor info and date (username-id-date format)
                const date = new Date().toLocaleDateString('en-US', {
                    month: 'numeric',
                    day: 'numeric',
                    year: 'numeric'
                }).replace(/\//g, '');

                const filename = `${executorUsername}-${executorId}-${date}.txt`;

                // Store file data directly instead of using FileComponent
                const channelDescription = channelList.length === 1
                    ? `#${channelList[0].name}`
                    : `${channelList.length} channels`;

                fileComponent = {
                    name: filename,
                    data: buffer,
                    description: `${messages.size} deleted messages from ${channelDescription}`
                };

                console.log(`[messageDeleteBulk] Created file component: ${filename} (${buffer.length} bytes)`);
            }



            // Create Components v2 container for bulk message delete
            const channelDisplay = channelList.length === 1
                ? `<#${channelList[0].id}>`
                : `${channelList.map(ch => ch.mention).join(', ')}`;
            const channelNameDisplay = channelList.length === 1
                ? channelList[0].name
                : `${channelList.length} channels`;

            const container = createBulkMessageDeleteContainer({
                channel: channelDisplay,
                channelName: channelNameDisplay,
                count: messages.size,
                executor: executor,
                messagePreview: messagePreview,
                fileComponent: fileComponent
            });

            // Send container to all configured channels
            await sendLogContainer(first.guild.id, 'messageDeleteBulk', container, client);
        }
        } catch (error) {
            console.error('[messageDeleteBulk] Error processing bulk message delete:', error);
            logger.error('messageDeleteBulk', `Error processing bulk message delete: ${error.message}`, client);
        }
    },
};