/**
 * Update DM message template to include {emoji} placeholder
 * Changes from: 'You found {items} in **{server}**, dropped from {location}:'
 * To: 'You found {emoji} {items} in {server}, dropped from {location}:'
 */

const { optimizedUpdateOne, optimizedUpdateMany, optimizedFindOne } = require('./database-optimizer.js');

async function updateDMTemplateWithEmoji() {
    try {
        console.log('[updateDMTemplate] Starting DM message template update to include emoji...');
        
        // Update global owner config
        const ownerResult = await optimizedUpdateOne("item_notifications",
            {
                key: 'global',
                dmMessage: 'You found {items} in **{server}**, dropped from {location}:'
            },
            {
                $set: {
                    dmMessage: 'You found {emoji} {items} in {server}, dropped from {location}:'
                }
            }
        );
        
        console.log(`[updateDMTemplate] Updated global owner config: ${ownerResult.modifiedCount} documents`);
        
        // Update guild configs (if any exist with the old format)
        const guildResult = await optimizedUpdateMany("guilds",
            {
                'items.dmMessage': 'You found {items} in **{server}**, dropped from {location}:'
            },
            {
                $set: {
                    'items.dmMessage': 'You found {emoji} {items} in {server}, dropped from {location}:'
                }
            }
        );
        
        console.log(`[updateDMTemplate] Updated guild configs: ${guildResult.modifiedCount} documents`);
        
        // Verify the update
        const ownerConfig = await optimizedFindOne("item_notifications", { key: 'global' });
        
        console.log('[updateDMTemplate] Verification:');
        console.log(`  - Global config message: "${ownerConfig?.dmMessage || 'not found'}"`);
        
        if (ownerConfig?.dmMessage === 'You found {emoji} {items} in {server}, dropped from {location}:') {
            console.log('[updateDMTemplate] ✅ DM message template successfully updated with emoji!');
        } else {
            console.log('[updateDMTemplate] ⚠️  Template may need manual updating');
        }
        
        return {
            ownerConfigUpdated: ownerResult.modifiedCount,
            guildConfigsUpdated: guildResult.modifiedCount
        };
        
    } catch (error) {
        console.error('[updateDMTemplate] Error during update:', error);
        throw error;
    }
}

// Run if called directly
if (require.main === module) {
    updateDMTemplateWithEmoji()
        .then(result => {
            console.log('[updateDMTemplate] Update completed:', result);
            process.exit(0);
        })
        .catch(error => {
            console.error('[updateDMTemplate] Update failed:', error);
            process.exit(1);
        });
}

module.exports = { updateDMTemplateWithEmoji };
