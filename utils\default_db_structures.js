let defaults = {
    guild: function(guildId){
        let guild_obj = {
            id: guildId,
            dehoist: {
                enabled: false, // Disabled by default - show demo data until explicitly enabled
                names: ["Alien", "Pluto", "Neptune"],
                blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"]
            },
            sticky: {
                enabled: false, // Disabled by default - show demo data until explicitly enabled
                roles: [],
                nick: null,
            },
            logs: {
                channels: [],
                enabled: false,
            },
            exp: {
                enabled: false, // Disabled by default - show demo data until explicitly enabled
                levels: [],
                levelMsgEnabled: false,
                levelChannel: null,
                levelMsg: '{mention} leveled up to level {level} and received the {emoji} {role} role.',
                text: {
                    enabled: true,
                    expPerMin: 1,
                    cooldown: 1,
                    minChars: 4
                },
                voice: {
                    enabled: true,
                    expPerMin: 2,
                    cooldown: 1,
                    msgEnabled: false,
                    msg: '{mention} spent {duration} in voice and earned {exp} exp.'
                }
            },
            items: {
                enabled: false, // Disabled by default - show demo data until explicitly enabled
                dmMessage: 'You found {items} in **{server}**, dropped from {location}:',
                dropChannel: null,
                dropNotificationsEnabled: false
            },
            opener: {
                enabled: false, // Disabled by default - show demo data until explicitly enabled
                threads: []
            }
        }
        return guild_obj;
    },
    member: function(memberIds){
        let member_obj = {
            guildId: memberIds.guildId,
            userId: memberIds.userId,
            sticky: {
                roles: [],
                nick: null
            },
            joinedBefore: false,
            exp: {
                total: 0,
                lastText: 0,
                lastVoice: 0,
                // Voice stats
                voice: {
                    total: 0,           // Total voice EXP earned
                    timeSpent: 0,       // Total milliseconds in voice
                    longestSession: 0,  // Longest session in milliseconds
                    sessionCount: 0,    // Number of sessions where EXP was gained
                    lastActiveDay: null, // Last day with voice activity (YYYY-MM-DD)
                    currentStreak: 0    // Current consecutive days with voice activity
                },
                // Text stats
                text: {
                    total: 0,           // Total text EXP earned
                    messagesSent: 0,    // Total messages sent
                    messagesCounted: 0, // Messages that gave EXP
                    lastActiveDay: null, // Last day with text activity (YYYY-MM-DD)
                    currentStreak: 0    // Current consecutive days with text activity
                }
            }
        }

        return member_obj;
    }
}
// exports.
module.exports = { defaults };
// export { defaults_objects };