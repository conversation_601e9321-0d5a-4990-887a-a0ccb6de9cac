/**
 * EXP System Caching Utility (Enterprise-Grade Performance Optimized)
 * Provides caching for frequently accessed EXP data to improve performance
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

const { optimizedFindOne, optimizedFind, optimizedAggregate } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const expCacheMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    configsServed: 0,
    calculationsServed: 0,
    rankingsServed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes
const levelCalculationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes
const rankingCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes
const expStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for EXP statistics

// Register caches for global cleanup
registerCache(guildConfigCache);
registerCache(levelCalculationCache);
registerCache(rankingCache);
registerCache(expStatsCache);

/**
 * Get guild EXP configuration with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild EXP configuration
 */
async function getCachedGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_${guildId}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = guildConfigCache.get(cacheKey);
    if (cached) {
        expCacheMetrics.cacheHits++;
        expCacheMetrics.configsServed++;
        if (expCacheMetrics.verboseLogging) {
            console.log(`[expCache] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    expCacheMetrics.cacheMisses++;
    expCacheMetrics.databaseQueries++;

    try {
        const guildData = await optimizedFindOne("guilds",
            { id: guildId },
            { projection: { exp: 1 } } // Only fetch EXP config
        );

        const config = guildData?.exp || { enabled: true };

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        guildConfigCache.set(cacheKey, config);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        expCacheMetrics.averageQueryTime =
            (expCacheMetrics.averageQueryTime * (expCacheMetrics.databaseQueries - 1) + duration) /
            expCacheMetrics.databaseQueries;

        if (expCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[expCache] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        expCacheMetrics.configsServed++;
        return config;
    } catch (error) {
        console.error(`[expCache] ❌ Error getting guild config for ${guildId}:`, error);
        return { enabled: true };
    }
}

/**
 * Calculate user level with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {number} totalExp - User's total EXP
 * @param {Array} levels - Guild level configuration
 * @returns {Object} Level calculation result
 */
function getCachedLevelCalculation(totalExp, levels) {
    const startTime = Date.now();

    if (!levels || levels.length === 0) {
        return { currentLevel: 0, nextLevelExp: null, levelIndex: -1 };
    }

    const cacheKey = `level_${totalExp}_${levels.length}_${levels[levels.length - 1]?.exp}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = levelCalculationCache.get(cacheKey);
    if (cached) {
        expCacheMetrics.cacheHits++;
        expCacheMetrics.calculationsServed++;
        if (expCacheMetrics.verboseLogging) {
            console.log(`[expCache] ⚡ Level calculation cache hit for ${totalExp} EXP (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    expCacheMetrics.cacheMisses++;
    
    let levelIndex = -1;
    
    // Optimized level calculation
    if (levels.length <= 10) {
        // Linear search for small arrays
        for (let i = levels.length - 1; i >= 0; i--) {
            if (totalExp >= levels[i].exp) {
                levelIndex = i;
                break;
            }
        }
    } else {
        // Binary search for larger arrays
        let left = 0, right = levels.length - 1;
        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            if (levels[mid].exp <= totalExp) {
                levelIndex = mid;
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
    }
    
    // Handle special case: if first level requires 0 EXP, it represents "Level 0"
    // In this case, we need to adjust the level calculation
    let currentLevel = 0;
    if (levelIndex >= 0) {
        if (levels[0].exp === 0) {
            // Guild has Level 0 configured - use levelIndex directly as the level
            currentLevel = levelIndex;
        } else {
            // Normal level calculation - levelIndex + 1
            currentLevel = levelIndex + 1;
        }
    }

    // FIXED: Add maximum level detection similar to global level system
    const isAtMaxLevel = levelIndex >= 0 && levelIndex === levels.length - 1;

    const result = {
        currentLevel: currentLevel,
        nextLevelExp: levelIndex + 1 < levels.length ? levels[levelIndex + 1].exp : null,
        levelIndex: levelIndex,
        roleId: levelIndex >= 0 ? levels[levelIndex].roleId : null,
        isAtMaxLevel: isAtMaxLevel
    };

    // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
    levelCalculationCache.set(cacheKey, result);

    // Enhanced performance monitoring
    const duration = Date.now() - startTime;
    if (expCacheMetrics.verboseLogging || duration > 50) {
        console.log(`[expCache] ✅ Level calculation completed for ${totalExp} EXP: Level ${result.currentLevel} (${duration}ms) - cached for future access`);
    }

    expCacheMetrics.calculationsServed++;
    return result;
}

/**
 * Get cached guild rankings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @param {number} limit - Number of results
 * @returns {Promise<Array>} Cached rankings or null if not cached
 */
async function getCachedGuildRankings(guildId, limit = 10) {
    const startTime = Date.now();
    const cacheKey = `guild_rankings_${guildId}_${limit}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = rankingCache.get(cacheKey);
    if (cached) {
        expCacheMetrics.cacheHits++;
        expCacheMetrics.rankingsServed++;
        if (expCacheMetrics.verboseLogging) {
            console.log(`[expCache] ⚡ Guild rankings cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    expCacheMetrics.cacheMisses++;
    return null; // Return null if not found/expired
}

/**
 * Cache guild rankings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @param {number} limit - Number of results
 * @param {Array} rankings - Rankings data
 */
function setCachedGuildRankings(guildId, limit, rankings) {
    const cacheKey = `guild_rankings_${guildId}_${limit}`;

    // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
    rankingCache.set(cacheKey, rankings);

    if (expCacheMetrics.verboseLogging) {
        console.log(`[expCache] ✅ Guild rankings cached for ${guildId}: ${rankings.length} entries`);
    }
}

/**
 * Get cached global rankings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {number} limit - Number of results
 * @returns {Promise<Array>} Cached rankings or null if not cached
 */
async function getCachedGlobalRankings(limit = 10) {
    const startTime = Date.now();
    const cacheKey = `global_rankings_${limit}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = rankingCache.get(cacheKey);
    if (cached) {
        expCacheMetrics.cacheHits++;
        expCacheMetrics.rankingsServed++;
        if (expCacheMetrics.verboseLogging) {
            console.log(`[expCache] ⚡ Global rankings cache hit for limit ${limit} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    expCacheMetrics.cacheMisses++;
    return null; // Return null if not found/expired
}

/**
 * Cache global rankings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {number} limit - Number of results
 * @param {Array} rankings - Rankings data
 */
function setCachedGlobalRankings(limit, rankings) {
    const cacheKey = `global_rankings_${limit}`;

    // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
    rankingCache.set(cacheKey, rankings);

    if (expCacheMetrics.verboseLogging) {
        console.log(`[expCache] ✅ Global rankings cached: ${rankings.length} entries`);
    }
}

/**
 * Invalidate cache for a specific guild
 * @param {string} guildId - Guild ID
 */
function invalidateGuildCache(guildId) {
    // OPTIMIZED: Use LRU cache delete methods
    guildConfigCache.delete(`guild_${guildId}`);

    // Remove guild rankings cache - need to iterate through keys
    const rankingKeys = rankingCache.getKeysByAccessTime();
    for (const key of rankingKeys) {
        if (key.startsWith(`guild_rankings_${guildId}_`) || key.startsWith('global_rankings_')) {
            rankingCache.delete(key);
        }
    }
}

/**
 * Batch get multiple guild configurations (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing with Promise.allSettled for comprehensive error recovery
 * @param {Array} guildIds - Array of guild IDs to fetch
 * @returns {Promise<Array>} Array of guild configurations (null for failed fetches)
 */
async function batchGetGuildConfigs(guildIds) {
    const startTime = Date.now();
    expCacheMetrics.parallelOperations++;

    try {
        // OPTIMIZED: Use Promise.allSettled for comprehensive error handling
        const configResults = await Promise.allSettled(
            guildIds.map(guildId => getCachedGuildConfig(guildId))
        );

        const configs = configResults.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            } else {
                expCacheMetrics.partialFailures++;
                if (expCacheMetrics.verboseLogging) {
                    console.warn(`[expCache] ⚠️  Failed to fetch config for guild ${guildIds[index]}: ${result.reason}`);
                }
                return { enabled: true }; // Fallback config
            }
        });

        const duration = Date.now() - startTime;
        const successCount = configs.filter(config => config !== null).length;
        const failureCount = configs.length - successCount;

        if (expCacheMetrics.verboseLogging || failureCount > 0) {
            console.log(`[expCache] ✅ Batch fetched ${successCount}/${configs.length} guild configs (${failureCount} failures) in ${duration}ms`);
        }

        return configs;
    } catch (error) {
        console.error('[expCache] ❌ Error in batch get guild configs:', error);
        return guildIds.map(() => ({ enabled: true }));
    }
}

/**
 * Get EXP statistics with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Aggregation pipeline with intelligent caching
 * @param {string} guildId - Guild ID (null for global)
 * @returns {Promise<Object>} EXP statistics
 */
async function getCachedExpStats(guildId = null) {
    const startTime = Date.now();
    const cacheKey = `exp_stats_${guildId || 'global'}`;

    // Check cache first
    const cached = expStatsCache.get(cacheKey);
    if (cached) {
        expCacheMetrics.cacheHits++;
        if (expCacheMetrics.verboseLogging) {
            console.log(`[expCache] ⚡ EXP stats cache hit for ${guildId || 'global'} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    expCacheMetrics.cacheMisses++;
    expCacheMetrics.databaseQueries++;

    try {
        // OPTIMIZED: Use aggregation pipeline for efficient statistics
        const pipeline = [
            {
                $match: {
                    "exp.total": { $gt: 0 },
                    ...(guildId && { guildId: guildId })
                }
            },
            {
                $group: {
                    _id: null,
                    totalUsers: { $sum: 1 },
                    totalExp: { $sum: "$exp.total" },
                    avgExp: { $avg: "$exp.total" },
                    maxExp: { $max: "$exp.total" },
                    minExp: { $min: "$exp.total" }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalUsers: 1,
                    totalExp: 1,
                    avgExp: { $round: ["$avgExp", 2] },
                    maxExp: 1,
                    minExp: 1
                }
            }
        ];

        const statsResult = await optimizedAggregate('member', pipeline);
        const stats = statsResult.length > 0 ? statsResult[0] : {
            totalUsers: 0,
            totalExp: 0,
            avgExp: 0,
            maxExp: 0,
            minExp: 0
        };

        // Cache the result
        expStatsCache.set(cacheKey, stats);

        const duration = Date.now() - startTime;
        if (expCacheMetrics.verboseLogging || duration > 150) {
            console.log(`[expCache] ✅ EXP stats calculated for ${guildId || 'global'}: ${duration}ms - cached for future access`);
        }

        return stats;
    } catch (error) {
        console.error(`[expCache] ❌ Error getting EXP stats for ${guildId || 'global'}:`, error);
        return {
            totalUsers: 0,
            totalExp: 0,
            avgExp: 0,
            maxExp: 0,
            minExp: 0
        };
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = expCacheMetrics.cacheHits + expCacheMetrics.cacheMisses > 0 ?
        (expCacheMetrics.cacheHits / (expCacheMetrics.cacheHits + expCacheMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: expCacheMetrics.cacheHits,
            cacheMisses: expCacheMetrics.cacheMisses,
            databaseQueries: expCacheMetrics.databaseQueries,
            averageQueryTime: `${expCacheMetrics.averageQueryTime.toFixed(2)}ms`,
            configsServed: expCacheMetrics.configsServed,
            calculationsServed: expCacheMetrics.calculationsServed,
            rankingsServed: expCacheMetrics.rankingsServed,
            parallelOperations: expCacheMetrics.parallelOperations,
            partialFailures: expCacheMetrics.partialFailures,
            lastOptimization: new Date(expCacheMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: guildConfigCache.getStats(),
            levelCalculation: levelCalculationCache.getStats(),
            rankings: rankingCache.getStats(),
            expStats: expStatsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildConfig: guildConfigCache.getStats().memoryUsage,
            levelCalculation: levelCalculationCache.getStats().memoryUsage,
            rankings: rankingCache.getStats().memoryUsage,
            expStats: expStatsCache.getStats().memoryUsage,
            total: guildConfigCache.getStats().memoryUsage +
                   levelCalculationCache.getStats().memoryUsage +
                   rankingCache.getStats().memoryUsage +
                   expStatsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, expCacheMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 150) {
        recommendations.push('EXP operations are slow - investigate database performance');
    }

    if (metrics.partialFailures > metrics.parallelOperations * 0.1) {
        recommendations.push('High partial failure rate - investigate system reliability');
    }

    if (metrics.parallelOperations < metrics.databaseQueries * 0.3) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (metrics.calculationsServed > metrics.configsServed * 5) {
        recommendations.push('High calculation to config ratio - consider level calculation optimizations');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    expCacheMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[expCache] 📊 Performance Report:`);
    console.log(`[expCache]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[expCache]   Configs Served: ${stats.performance.configsServed}`);
    console.log(`[expCache]   Calculations Served: ${stats.performance.calculationsServed}`);
    console.log(`[expCache]   Rankings Served: ${stats.performance.rankingsServed}`);
    console.log(`[expCache]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[expCache]   Partial Failures: ${stats.performance.partialFailures}`);
    console.log(`[expCache]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[expCache]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[expCache]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[expCache] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all EXP caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllExpCaches() {
    guildConfigCache.clear();
    levelCalculationCache.clear();
    rankingCache.clear();
    expStatsCache.clear();

    console.log('[expCache] 🗑️ Cleared all EXP caches');
}

/**
 * Batch invalidate multiple guild caches (Enterprise-Grade Optimization)
 * OPTIMIZED: Efficient batch cache invalidation for bulk operations
 * @param {Array} guildIds - Array of guild IDs to invalidate
 */
function batchInvalidateGuildCaches(guildIds) {
    let totalInvalidated = 0;

    guildIds.forEach(guildId => {
        // Invalidate guild config
        if (guildConfigCache.delete(`guild_${guildId}`)) totalInvalidated++;

        // Invalidate guild rankings
        const rankingKeys = Array.from(rankingCache.keys()).filter(key =>
            key.startsWith(`guild_rankings_${guildId}_`) || key.startsWith('global_rankings_')
        );
        rankingKeys.forEach(key => {
            if (rankingCache.delete(key)) totalInvalidated++;
        });

        // Invalidate EXP stats
        if (expStatsCache.delete(`exp_stats_${guildId}`)) totalInvalidated++;
    });

    if (expCacheMetrics.verboseLogging) {
        console.log(`[expCache] 🗑️ Batch invalidated ${totalInvalidated} cache entries for ${guildIds.length} guilds`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, expCacheMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getCachedGuildConfig,
    getCachedLevelCalculation,
    getCachedGuildRankings,
    setCachedGuildRankings,
    getCachedGlobalRankings,
    setCachedGlobalRankings,
    invalidateGuildCache,

    // Enhanced optimization functions
    batchGetGuildConfigs,
    getCachedExpStats,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllExpCaches,
    batchInvalidateGuildCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...expCacheMetrics })
};
