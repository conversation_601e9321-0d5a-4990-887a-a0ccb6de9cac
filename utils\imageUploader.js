const { StringSelectMenuBuilder, ActionRowBuilder } = require('discord.js');
const { optimizedDeleteOne, optimizedFindOne, optimizedUpdateOne, optimizedAggregate } = require('./database-optimizer.js');
const { trackEmojiUsage } = require('./emojiCleanup.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * Shared Image Uploader Utility (Enterprise-Grade Performance Optimized)
 * Eliminates code duplication across owner.js, items.js, exp.js with enterprise-grade optimization
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const imageUploaderMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    imagesServed: 0,
    uploadsProcessed: 0,
    validationsPerformed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: false, // Disabled - image upload system working correctly
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const imageCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for recent images
const uploadStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for statistics

// Register caches for global cleanup
registerCache(imageCache);
registerCache(uploadStatsCache);

/**
 * Invalidate image cache for a specific user and guild (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 */
async function invalidateImageCache(userId, guildId) {
    const startTime = Date.now();

    try {
        // Invalidate both database cache and LRU cache
        const cacheKey = `${userId}_${guildId}`;
        imageCache.delete(cacheKey);

        await optimizedDeleteOne('user_images_cache', { userId, guildId });

        const duration = Date.now() - startTime;
        if (imageUploaderMetrics.verboseLogging || duration > 100) {
            console.log(`[imageUploader] 🗑️ Invalidated image cache for user ${userId} in guild ${guildId} (${duration}ms)`);
        }
    } catch (error) {
        console.error('[imageUploader] ❌ Error invalidating image cache:', error);
    }
}

/**
 * Force refresh image cache for a specific user and guild (Enterprise-Grade Optimized)
 * FIXED: Added to ensure fresh image data after uploads
 * @param {Object} interaction - Discord interaction
 * @returns {Promise<Array>} Fresh image data
 */
async function forceRefreshImageCache(interaction) {
    const startTime = Date.now();

    try {
        // First invalidate existing cache
        await invalidateImageCache(interaction.user.id, interaction.guild.id);

        // Then fetch fresh data (bypasses cache)
        const freshImages = await getRecentImagesFromChannel(interaction, { limit: 8, cacheMinutes: 2 });

        const duration = Date.now() - startTime;
        if (imageUploaderMetrics.verboseLogging || duration > 200) {
            console.log(`[imageUploader] 🔄 Force refreshed image cache for user ${interaction.user.id}: ${freshImages.length} images found in ${duration}ms`);
        }

        return freshImages;
    } catch (error) {
        console.error('[imageUploader] ❌ Error force refreshing image cache:', error);
        return [];
    }
}

/**
 * Get recent images from channel with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with LRU cache and performance monitoring
 * @param {Object} interaction - Discord interaction
 * @param {Object} options - Configuration options
 * @param {number} options.limit - Number of images to fetch (default: 8)
 * @param {number} options.cacheMinutes - Cache duration in minutes (default: 2)
 * @returns {Array} Array of image objects
 */
async function getRecentImagesFromChannel(interaction, options = {}) {
    const startTime = Date.now();
    const { limit = 8, cacheMinutes = 2 } = options;
    const cacheKey = `${interaction.user.id}_${interaction.guild.id}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = imageCache.get(cacheKey);
        if (cached) {
            imageUploaderMetrics.cacheHits++;
            imageUploaderMetrics.imagesServed++;
            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] ⚡ Image cache hit for ${interaction.user.id} (${Date.now() - startTime}ms)`);
            }
            return cached.slice(0, limit);
        }

        imageUploaderMetrics.cacheMisses++;
        imageUploaderMetrics.databaseQueries++;

        // Check database cache as fallback
        const cacheData = await optimizedFindOne('user_images_cache', {
            userId: interaction.user.id,
            guildId: interaction.guild.id
        });

        const cacheAge = cacheData?.updatedAt ?
            (Date.now() - cacheData.updatedAt.getTime()) :
            Infinity;

        if (cacheData?.images?.length > 0 && cacheAge < (cacheMinutes * 60 * 1000)) {
            // Cache in LRU for faster future access
            imageCache.set(cacheKey, cacheData.images);

            const duration = Date.now() - startTime;
            if (imageUploaderMetrics.verboseLogging || duration > 100) {
                console.log(`[imageUploader] ✅ Images fetched from database cache for ${interaction.user.id}: ${duration}ms - cached in LRU`);
            }

            imageUploaderMetrics.imagesServed++;
            return cacheData.images.slice(0, limit);
        }

        // OPTIMIZED: Fetch fresh images with improved search logic and performance monitoring
        const images = [];
        let lastMessageId = null;
        const maxSearchMessages = 10; // Search up to 10 messages to find recent images
        let searchedMessages = 0;
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] 🔍 Searching for recent images (last 24 hours) for user ${interaction.user.id} in channel ${interaction.channel.id}`);
        }

        // Keep fetching messages until we find enough images or hit the search limit
        while (images.length < limit && searchedMessages < maxSearchMessages) {
            const fetchOptions = { limit: 20 }; // OPTIMIZED: Larger batches = fewer API calls
            if (lastMessageId) {
                fetchOptions.before = lastMessageId;
            }

            const messages = await interaction.channel.messages.fetch(fetchOptions);

            if (messages.size === 0) {
                if (imageUploaderMetrics.verboseLogging) {
                    console.log(`[imageUploader] No more messages to search`);
                }
                break; // No more messages
            }

            searchedMessages += messages.size;

            // Check if ALL messages in this batch are older than 24 hours
            const allMessagesOld = messages.every(msg => msg.createdTimestamp < oneDayAgo);
            if (allMessagesOld) {
                if (imageUploaderMetrics.verboseLogging) {
                    console.log(`[imageUploader] All messages in batch are older than 24 hours, stopping search`);
                }
                break;
            }

            // Filter for user's messages with image attachments from last 24 hours
            const userImageMessages = messages.filter(msg =>
                msg.author.id === interaction.user.id &&
                msg.createdTimestamp >= oneDayAgo && // Only last 24 hours
                msg.attachments.size > 0 &&
                Array.from(msg.attachments.values()).some(att => att.contentType?.startsWith('image/'))
            );

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] Found ${userImageMessages.size} user messages with recent images in batch of ${messages.size}`);
            }

            // Extract images from these messages
            for (const message of userImageMessages.values()) {
                for (const attachment of message.attachments.values()) {
                    if (attachment.contentType?.startsWith('image/')) {
                        const imageAge = Date.now() - message.createdTimestamp;
                        const hoursAgo = Math.floor(imageAge / (1000 * 60 * 60));
                        const minutesAgo = Math.floor(imageAge / (1000 * 60));

                        let ageText;
                        if (hoursAgo > 0) {
                            ageText = `${hoursAgo} hour${hoursAgo > 1 ? 's' : ''} ago`;
                        } else if (minutesAgo > 0) {
                            ageText = `${minutesAgo} minute${minutesAgo > 1 ? 's' : ''} ago`;
                        } else {
                            ageText = 'just now';
                        }

                        images.push({
                            filename: attachment.name,
                            url: attachment.url,
                            size: attachment.size,
                            messageId: message.id,
                            timestamp: message.createdTimestamp
                        });

                        if (imageUploaderMetrics.verboseLogging) {
                            console.log(`[imageUploader] Found recent image: ${attachment.name} (${ageText})`);
                        }

                        if (images.length >= limit) {
                            break;
                        }
                    }
                }
                if (images.length >= limit) {
                    break;
                }
            }

            // Set up for next iteration
            const lastMessage = messages.last();
            lastMessageId = lastMessage?.id;
        }

        // Sort by most recent first and limit
        const sortedImages = images
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        imageUploaderMetrics.averageQueryTime =
            (imageUploaderMetrics.averageQueryTime * (imageUploaderMetrics.databaseQueries - 1) + duration) /
            imageUploaderMetrics.databaseQueries;

        if (imageUploaderMetrics.verboseLogging || duration > 200) {
            console.log(`[imageUploader] ✅ Found ${sortedImages.length} recent images (last 24 hours) for user (searched ${searchedMessages} messages) in ${duration}ms`);
        }

        // OPTIMIZED: Update both database cache and LRU cache
        await optimizedUpdateOne('user_images_cache',
            { userId: interaction.user.id, guildId: interaction.guild.id },
            {
                $set: {
                    images: sortedImages,
                    updatedAt: new Date()
                }
            },
            { upsert: true }
        );

        // Cache in LRU for faster future access
        imageCache.set(cacheKey, sortedImages);

        imageUploaderMetrics.imagesServed++;
        return sortedImages;

    } catch (error) {
        console.error('[imageUploader] ❌ Error fetching recent images:', error);
        return [];
    }
}

/**
 * Find a unique guild emote name (Enterprise-Grade Duplicate Handling)
 * ENHANCED: Robust duplicate name detection for guild emotes
 * @param {Object} guild - Discord guild
 * @param {string} baseEmoteName - Base name for the emote
 * @param {boolean} forceRefresh - Force refresh of emoji cache
 * @returns {Promise<string>} Unique emote name
 */
async function findUniqueGuildEmoteName(guild, baseEmoteName, forceRefresh = false) {
    try {
        // ENHANCED: Force refresh emoji cache if requested
        const existingEmojis = forceRefresh ?
            await guild.emojis.fetch({ force: true }) :
            guild.emojis.cache;

        const existingNames = new Set(existingEmojis.map(emoji => emoji.name));

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] 🔍 Checking against ${existingNames.size} existing guild emojis`);
        }

        // Find a unique name with automatic incrementing
        let emoteName = baseEmoteName;
        let counter = 1;

        while (existingNames.has(emoteName)) {
            const suffix = `_${counter}`;
            // Ensure total length doesn't exceed Discord's 32 character limit
            const maxBaseLength = 32 - suffix.length;
            emoteName = baseEmoteName.substring(0, maxBaseLength) + suffix;
            counter++;

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] 🔄 Guild name "${baseEmoteName}" taken, trying "${emoteName}"`);
            }

            // Safety check to prevent infinite loop
            if (counter > 1000) {
                emoteName = `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
                console.warn(`[imageUploader] 🚨 Exceeded 1000 guild attempts, using fallback name: ${emoteName}`);
                break;
            }
        }

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] ✅ Found unique guild name: ${emoteName} (after ${counter - 1} attempts)`);
        }

        return emoteName;
    } catch (error) {
        console.error('[imageUploader] ❌ Error finding unique guild emote name:', error);
        // Fallback to timestamp-based name
        const fallbackName = `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
        console.warn(`[imageUploader] 🔧 Using fallback guild name due to error: ${fallbackName}`);
        return fallbackName;
    }
}

/**
 * Find a unique application emote name (Enterprise-Grade Duplicate Handling)
 * ENHANCED: Robust duplicate name detection with automatic incrementing
 * @param {Object} client - Discord client
 * @param {string} baseEmoteName - Base name for the emote
 * @param {boolean} forceRefresh - Force refresh of emoji cache
 * @returns {Promise<string>} Unique emote name
 */
async function findUniqueApplicationEmoteName(client, baseEmoteName, forceRefresh = false) {
    try {
        // ENHANCED: Force refresh emoji cache if requested (handles race conditions)
        let existingEmojis;
        if (forceRefresh) {
            try {
                existingEmojis = await client.application.emojis.fetch();
            } catch (fetchError) {
                console.warn('[imageUploader] Failed to fetch application emojis, using cache:', fetchError.message);
                existingEmojis = client.application.emojis.cache;
            }
        } else {
            existingEmojis = client.application.emojis.cache;
        }

        const existingNames = new Set(existingEmojis.map(emoji => emoji.name).filter(name => typeof name === 'string'));

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] 🔍 Checking against ${existingNames.size} existing application emojis`);
            console.log(`[imageUploader] 🔍 Looking for unique name for base: "${baseEmoteName}"`);
        }

        // Find a unique name with automatic incrementing
        let emoteName = baseEmoteName;
        let counter = 1;

        while (existingNames.has(emoteName)) {
            const suffix = `_${counter}`;
            // Ensure total length doesn't exceed Discord's 32 character limit
            const maxBaseLength = 32 - suffix.length;
            emoteName = baseEmoteName.substring(0, maxBaseLength) + suffix;
            counter++;

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] 🔄 Name "${baseEmoteName}" taken, trying "${emoteName}"`);
            }

            // Safety check to prevent infinite loop
            if (counter > 1000) {
                emoteName = `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
                console.warn(`[imageUploader] 🚨 Exceeded 1000 attempts, using fallback name: ${emoteName}`);
                break;
            }
        }

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] ✅ Found unique name: ${emoteName} (after ${counter - 1} attempts)`);
        }

        return emoteName;
    } catch (error) {
        console.error('[imageUploader] ❌ Error finding unique application emote name:', error);
        // Fallback to timestamp-based name
        const fallbackName = `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
        console.warn(`[imageUploader] 🔧 Using fallback name due to error: ${fallbackName}`);
        return fallbackName;
    }
}

/**
 * Upload image as custom emote (Enterprise-Grade Optimized)
 * ENHANCED: Robust duplicate name handling with automatic incrementing and retry logic
 * @param {string} imageUrl - URL of the image to upload
 * @param {string} filename - Original filename
 * @param {string} guildId - Guild ID
 * @param {Object} client - Discord client
 * @param {Object} options - Upload options
 * @param {boolean} options.useApplicationEmote - Use application emote instead of guild emote (default: true)
 * @returns {Object|null} Emote data or null if failed
 */
async function uploadImageAsEmote(imageUrl, filename, guildId, client, options = {}) {
    const startTime = Date.now();
    // CHANGED: Default to application emojis for all bot operations
    const { useApplicationEmote = true } = options;

    try {
        imageUploaderMetrics.uploadsProcessed++;

        // Download the image
        const response = await fetch(imageUrl);
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // ENHANCED: Create base emote name from filename with proper length handling
        let baseEmoteName = filename
            .replace(/\.[^/.]+$/, '') // Remove extension
            .replace(/[^a-zA-Z0-9_]/g, '_') // Replace invalid chars
            .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
            .replace(/_+/g, '_') // Replace multiple underscores with single
            .substring(0, 25); // Leave room for suffix (_999 = 4 chars, so 32-4-3=25 for safety)

        // Ensure name starts with letter or number (Discord requirement)
        if (baseEmoteName && /^[^a-zA-Z0-9]/.test(baseEmoteName)) {
            baseEmoteName = 'img_' + baseEmoteName;
        }

        if (!baseEmoteName || baseEmoteName.length < 2) {
            baseEmoteName = 'custom_emote';
        }

        // Ensure base name doesn't exceed safe length for suffix addition
        if (baseEmoteName.length > 25) {
            baseEmoteName = baseEmoteName.substring(0, 25);
        }

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] 🎯 Upload mode: ${useApplicationEmote ? 'APPLICATION' : 'GUILD'} emoji`);
            console.log(`[imageUploader] 📝 Original filename: "${filename}" → Base name: "${baseEmoteName}"`);
        }

        if (useApplicationEmote) {
            // ENHANCED: Application emote mode with robust duplicate name handling
            let emoteName = await findUniqueApplicationEmoteName(client, baseEmoteName);

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] 🎯 Using unique emote name: ${emoteName}`);
            }

            // Upload to Discord as application emote with retry logic
            let emote;
            let createAttempts = 0;
            const maxCreateAttempts = 3;

            while (createAttempts < maxCreateAttempts) {
                try {
                    emote = await client.application.emojis.create({
                        attachment: buffer,
                        name: emoteName
                    });
                    break; // Success, exit retry loop
                } catch (createError) {
                    createAttempts++;

                    if (createError.code === 50035) { // APPLICATION_EMOJI_NAME_ALREADY_TAKEN
                        console.warn(`[imageUploader] ⚠️ Emoji name "${emoteName}" still taken after duplicate check, retrying with new name (attempt ${createAttempts})`);

                        // Force refresh and find a new unique name
                        emoteName = await findUniqueApplicationEmoteName(client, baseEmoteName, true);

                        if (createAttempts >= maxCreateAttempts) {
                            // Last resort: use timestamp-based name
                            emoteName = `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
                            console.warn(`[imageUploader] 🚨 Using fallback name: ${emoteName}`);
                        }
                    } else {
                        // Different error, don't retry
                        throw createError;
                    }
                }
            }

            if (!emote) {
                throw new Error(`Failed to create application emote after ${maxCreateAttempts} attempts`);
            }

            // Track emoji for cleanup system
            trackEmojiUsage(emote.id, 'item_creation', 'unknown');

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] ✅ Successfully created application emote: ${emote.name} (${emote.id})`);
            }

            return {
                id: emote.id,
                name: emote.name,
                animated: emote.animated,
                string: `<${emote.animated ? 'a' : ''}:${emote.name}:${emote.id}>`,
                isApplicationEmote: true
            };

        } else {
            // ENHANCED: Guild emote mode with improved duplicate handling
            const guild = client.guilds.cache.get(guildId);
            if (!guild) {
                throw new Error('Guild not found');
            }

            // Find unique name for guild emotes
            let emoteName = await findUniqueGuildEmoteName(guild, baseEmoteName);

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] 🎯 Using unique guild emote name: ${emoteName}`);
            }

            // Create guild emote with retry logic
            let emote;
            let createAttempts = 0;
            const maxCreateAttempts = 3;

            while (createAttempts < maxCreateAttempts) {
                try {
                    emote = await guild.emojis.create({
                        attachment: buffer,
                        name: emoteName
                    });
                    break; // Success, exit retry loop
                } catch (createError) {
                    createAttempts++;

                    if (createError.code === 50035) { // EMOJI_NAME_ALREADY_TAKEN
                        console.warn(`[imageUploader] ⚠️ Guild emoji name "${emoteName}" still taken after duplicate check, retrying with new name (attempt ${createAttempts})`);

                        // Find a new unique name
                        emoteName = await findUniqueGuildEmoteName(guild, baseEmoteName, true);

                        if (createAttempts >= maxCreateAttempts) {
                            // Last resort: use timestamp-based name
                            emoteName = `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
                            console.warn(`[imageUploader] 🚨 Using fallback guild name: ${emoteName}`);
                        }
                    } else {
                        // Different error, don't retry
                        throw createError;
                    }
                }
            }

            if (!emote) {
                throw new Error(`Failed to create guild emote after ${maxCreateAttempts} attempts`);
            }

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] ✅ Successfully created guild emote: ${emote.name} (${emote.id})`);
            }

            return {
                id: emote.id,
                name: emote.name,
                animated: emote.animated,
                url: emote.imageURL(),
                string: `<${emote.animated ? 'a' : ''}:${emote.name}:${emote.id}>`,
                identifier: emote.identifier
            };
        }

        throw new Error(`Failed to create emote after ${maxAttempts} attempts`);

    } catch (error) {
        console.error('[imageUploader] ❌ Error uploading image as emote:', error);

        // ENHANCED: Detailed error logging for debugging
        if (error.code === 50035) {
            console.error('[imageUploader] 🚨 APPLICATION_EMOJI_NAME_ALREADY_TAKEN error details:');
            console.error(`   - Attempted name: ${error.message}`);
            console.error(`   - Base filename: ${filename}`);
            console.error(`   - Use application emote: ${options.useApplicationEmote}`);
        }

        imageUploaderMetrics.uploadsFailed++;
        return null;
    } finally {
        // Track performance metrics
        const duration = Date.now() - startTime;
        imageUploaderMetrics.totalUploadTime += duration;

        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] ⏱️ Upload completed in ${duration}ms`);
        }
    }
}

/**
 * Build image selection UI components
 * @param {Array} images - Array of image objects
 * @param {string} customId - Custom ID for the select menu
 * @param {string} placeholder - Placeholder text
 * @param {string} selectedValue - Currently selected value (optional)
 * @returns {ActionRowBuilder|null} Action row with select menu or null if no images
 */
function buildImageSelectMenu(images, customId, placeholder = 'select image for emote', selectedValue = null) {
    if (!images || images.length === 0) {
        return null;
    }

    const imageSelect = new StringSelectMenuBuilder()
        .setCustomId(customId)
        .setPlaceholder(placeholder);

    images.forEach((image, index) => {
        // Clean up filename - remove extension and limit length
        const cleanName = image.filename
            .replace(/\.[^/.]+$/, '') // Remove extension
            .substring(0, 25); // Limit length for better display

        const optionValue = `image-${index}`;
        imageSelect.addOptions({
            label: cleanName,
            value: optionValue,
            description: `${(image.size / 1024).toFixed(1)}KB - ${new Date(image.timestamp).toLocaleDateString()}`,
            emoji: '🖼️',
            default: selectedValue === optionValue
        });
    });

    return new ActionRowBuilder().addComponents(imageSelect);
}

/**
 * Build "no images" select menu for consistent UX
 * @param {string} customId - Custom ID for the select menu
 * @param {string} placeholder - Placeholder text
 * @returns {ActionRowBuilder} Action row with disabled select menu
 */
function buildNoImagesSelectMenu(customId, placeholder = 'select image for emote') {
    const imageSelect = new StringSelectMenuBuilder()
        .setCustomId(customId)
        .setPlaceholder(placeholder)
        .addOptions({
            label: 'No recent images found',
            value: 'no-images',
            description: 'Upload an image in this channel first',
            emoji: '📷'
        });
        // Removed .setDisabled(true) so users can interact with it

    return new ActionRowBuilder().addComponents(imageSelect);
}

/**
 * Handle image selection from select menu
 * @param {Object} interaction - Discord interaction
 * @param {Function} callback - Callback function to handle successful upload
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Promise that resolves with result object: { success: boolean, error?: string }
 */
async function handleImageSelection(interaction, callback, options = {}) {
    try {
        // Handle "no-images" selection - just do nothing (user clicked disabled option)
        if (interaction.values[0] === 'no-images') {
            return { success: false, error: null }; // No error, just no action
        }

        const selectedImageIndex = parseInt(interaction.values[0].replace('image-', ''));

        // Get fresh images
        const recentImages = await getRecentImagesFromChannel(interaction, options);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            return { success: false, error: 'Selected image not found. Please try again.' };
        }

        // Upload image as custom emote
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            options
        );

        if (!emoteData) {
            return { success: false, error: 'Failed to upload image as emote. Please try again.' };
        }

        // Call the callback with the emote data
        await callback(interaction, emoteData, selectedImage);

        // FIXED: Invalidate cache first, then refresh to ensure newly uploaded images appear immediately
        try {
            // First, invalidate the cache to remove stale data
            await invalidateImageCache(interaction.user.id, interaction.guild.id);

            // Then refresh with fresh data (this will bypass cache and fetch new images)
            await getRecentImagesFromChannel(interaction, { limit: 8, cacheMinutes: 2 });

            if (imageUploaderMetrics.verboseLogging) {
                console.log(`[imageUploader] 🔄 Invalidated and refreshed image cache after upload for user ${interaction.user.id}`);
            }
        } catch (refreshError) {
            console.error('[imageUploader] ❌ Error refreshing cache after upload:', refreshError);
            // Ensure cache is invalidated even if refresh fails
            await invalidateImageCache(interaction.user.id, interaction.guild.id);
        }

        return { success: true };

    } catch (error) {
        console.error('[imageUploader] ❌ Error handling image selection:', error);
        return { success: false, error: 'An error occurred while processing the image. Please try again.' };
    }
}

/**
 * Batch validate multiple images (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing with Promise.allSettled for comprehensive error recovery
 * @param {Array} imageUrls - Array of image URLs to validate
 * @returns {Promise<Array>} Array of validation results
 */
async function batchValidateImages(imageUrls) {
    const startTime = Date.now();
    imageUploaderMetrics.parallelOperations++;

    try {
        // OPTIMIZED: Use Promise.allSettled for comprehensive error handling
        const validationResults = await Promise.allSettled(
            imageUrls.map(async (url) => {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    const contentType = response.headers.get('content-type');
                    const contentLength = parseInt(response.headers.get('content-length') || '0');

                    return {
                        url,
                        valid: response.ok && contentType?.startsWith('image/'),
                        contentType,
                        size: contentLength,
                        error: null
                    };
                } catch (error) {
                    return {
                        url,
                        valid: false,
                        contentType: null,
                        size: 0,
                        error: error.message
                    };
                }
            })
        );

        const results = validationResults.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            } else {
                imageUploaderMetrics.partialFailures++;
                return {
                    url: imageUrls[index],
                    valid: false,
                    contentType: null,
                    size: 0,
                    error: result.reason
                };
            }
        });

        const duration = Date.now() - startTime;
        const validCount = results.filter(r => r.valid).length;
        const invalidCount = results.length - validCount;

        if (imageUploaderMetrics.verboseLogging || invalidCount > 0) {
            console.log(`[imageUploader] ✅ Batch validated ${validCount}/${results.length} images (${invalidCount} invalid) in ${duration}ms`);
        }

        imageUploaderMetrics.validationsPerformed += results.length;
        return results;
    } catch (error) {
        console.error('[imageUploader] ❌ Error in batch image validation:', error);
        return imageUrls.map(url => ({
            url,
            valid: false,
            contentType: null,
            size: 0,
            error: 'Validation failed'
        }));
    }
}

/**
 * Get image upload statistics with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Aggregation pipeline with intelligent caching
 * @param {string} guildId - Guild ID (null for global)
 * @returns {Promise<Object>} Upload statistics
 */
async function getCachedUploadStats(guildId = null) {
    const startTime = Date.now();
    const cacheKey = `upload_stats_${guildId || 'global'}`;

    // Check cache first
    const cached = uploadStatsCache.get(cacheKey);
    if (cached) {
        imageUploaderMetrics.cacheHits++;
        if (imageUploaderMetrics.verboseLogging) {
            console.log(`[imageUploader] ⚡ Upload stats cache hit for ${guildId || 'global'} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    imageUploaderMetrics.cacheMisses++;
    imageUploaderMetrics.databaseQueries++;

    try {
        // OPTIMIZED: Use aggregation pipeline for efficient statistics
        const pipeline = [
            {
                $match: {
                    ...(guildId && { guildId: guildId })
                }
            },
            {
                $group: {
                    _id: null,
                    totalCacheEntries: { $sum: 1 },
                    avgImagesPerUser: { $avg: { $size: "$images" } },
                    recentUploads: {
                        $sum: {
                            $cond: [{ $gte: ["$updatedAt", new Date(Date.now() - 24 * 60 * 60 * 1000)] }, 1, 0]
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalCacheEntries: 1,
                    avgImagesPerUser: { $round: ["$avgImagesPerUser", 2] },
                    recentUploads: 1
                }
            }
        ];

        const statsResult = await optimizedAggregate('user_images_cache', pipeline);
        const stats = statsResult.length > 0 ? statsResult[0] : {
            totalCacheEntries: 0,
            avgImagesPerUser: 0,
            recentUploads: 0
        };

        // Cache the result
        uploadStatsCache.set(cacheKey, stats);

        const duration = Date.now() - startTime;
        if (imageUploaderMetrics.verboseLogging || duration > 150) {
            console.log(`[imageUploader] ✅ Upload stats calculated for ${guildId || 'global'}: ${duration}ms - cached for future access`);
        }

        return stats;
    } catch (error) {
        console.error(`[imageUploader] ❌ Error getting upload stats for ${guildId || 'global'}:`, error);
        return {
            totalCacheEntries: 0,
            avgImagesPerUser: 0,
            recentUploads: 0
        };
    }
}

/**
 * Get comprehensive image uploader statistics (Enterprise-Grade)
 * @returns {Object} Comprehensive image uploader statistics
 */
function getImageUploaderStats() {
    const cacheHitRate = imageUploaderMetrics.cacheHits + imageUploaderMetrics.cacheMisses > 0 ?
        (imageUploaderMetrics.cacheHits / (imageUploaderMetrics.cacheHits + imageUploaderMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: imageUploaderMetrics.cacheHits,
            cacheMisses: imageUploaderMetrics.cacheMisses,
            databaseQueries: imageUploaderMetrics.databaseQueries,
            averageQueryTime: `${imageUploaderMetrics.averageQueryTime.toFixed(2)}ms`,
            imagesServed: imageUploaderMetrics.imagesServed,
            uploadsProcessed: imageUploaderMetrics.uploadsProcessed,
            validationsPerformed: imageUploaderMetrics.validationsPerformed,
            parallelOperations: imageUploaderMetrics.parallelOperations,
            partialFailures: imageUploaderMetrics.partialFailures,
            lastOptimization: new Date(imageUploaderMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            images: imageCache.getStats(),
            uploadStats: uploadStatsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            images: imageCache.getStats().memoryUsage,
            uploadStats: uploadStatsCache.getStats().memoryUsage,
            total: imageCache.getStats().memoryUsage + uploadStatsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    imageUploaderMetrics.lastOptimization = Date.now();

    const stats = getImageUploaderStats();
    if (imageUploaderMetrics.verboseLogging) {
        console.log(`[imageUploader] 📊 Performance Report:`);
        console.log(`[imageUploader]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[imageUploader]   Images Served: ${stats.performance.imagesServed}`);
        console.log(`[imageUploader]   Uploads Processed: ${stats.performance.uploadsProcessed}`);
        console.log(`[imageUploader]   Validations Performed: ${stats.performance.validationsPerformed}`);
        console.log(`[imageUploader]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[imageUploader]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[imageUploader]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[imageUploader]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[imageUploader]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, imageUploaderMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getRecentImagesFromChannel,
    uploadImageAsEmote,
    buildImageSelectMenu,
    buildNoImagesSelectMenu,
    handleImageSelection,
    invalidateImageCache,
    forceRefreshImageCache,

    // Enhanced optimization functions
    batchValidateImages,
    getCachedUploadStats,
    getImageUploaderStats,
    performanceCleanupAndOptimization,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...imageUploaderMetrics })
};
