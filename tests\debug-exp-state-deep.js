require('dotenv').config();

const { Client, GatewayIntentBits } = require('discord.js');

async function debugExpStateDeep() {
    console.log('🔍 Deep Debugging EXP State Issues...\n');

    const client = new Client({ intents: [GatewayIntentBits.Guilds] });
    await client.login(process.env.TOKEN);

    const testGuildId = process.env.GUILDIDTWO;
    const guild = client.guilds.cache.get(testGuildId);
    
    if (!guild) {
        console.log('❌ Test guild not found');
        process.exit(1);
    }

    // Fetch a member for testing
    if (guild.members.cache.size === 0) {
        await guild.members.fetch({ limit: 1 });
    }
    const member = guild.members.cache.first();

    const { optimizedFindOne, optimizedUpdateOne } = require('../utils/database-optimizer.js');
    const exp = require('../commands/utility/exp.js');
    
    console.log(`📊 Testing with guild: ${guild.name}`);
    console.log(`👤 Testing with member: ${member ? member.user.username : 'NONE'}`);

    // Step 1: Check current database state
    console.log('\n1️⃣ Current Database State:');
    let guildData = await optimizedFindOne('guilds', { id: testGuildId });
    console.log(`   EXP Object: ${JSON.stringify(guildData?.exp || {}, null, 2)}`);
    console.log(`   EXP Enabled: ${guildData?.exp?.enabled}`);
    console.log(`   EXP Levels: ${guildData?.exp?.levels?.length || 0}`);

    // Step 2: Test hasRealExpData function
    console.log('\n2️⃣ Testing hasRealExpData function:');
    function hasRealExpData(guildData) {
        if (!guildData?.exp) return false;
        if (guildData.exp.levels && guildData.exp.levels.length > 0) return true;
        if (guildData.exp.levelMsgEnabled === true) return true;
        if (guildData.exp.levelChannel) return true;
        return false;
    }
    
    const hasRealData = hasRealExpData(guildData);
    console.log(`   Has Real Data: ${hasRealData}`);
    console.log(`   Levels Count: ${guildData?.exp?.levels?.length || 0}`);
    console.log(`   Level Messages: ${guildData?.exp?.levelMsgEnabled}`);
    console.log(`   Level Channel: ${guildData?.exp?.levelChannel}`);

    // Step 3: Test demo logic with different scenarios
    console.log('\n3️⃣ Testing Demo Logic:');
    
    const scenarios = [
        { enabled: true, hasPermission: true, description: 'Enabled + Permission' },
        { enabled: false, hasPermission: true, description: 'Disabled + Permission' },
        { enabled: true, hasPermission: false, description: 'Enabled + No Permission' },
        { enabled: false, hasPermission: false, description: 'Disabled + No Permission' }
    ];
    
    scenarios.forEach(scenario => {
        const shouldShowDemo = !scenario.hasPermission || (!scenario.enabled && !hasRealData) || (scenario.enabled && !hasRealData);
        const isShowingDemo = Boolean(shouldShowDemo && guild && member);
        
        console.log(`   ${scenario.description}:`);
        console.log(`     Should Show Demo: ${shouldShowDemo}`);
        console.log(`     Is Showing Demo: ${isShowingDemo}`);
        console.log(`     Logic: !${scenario.hasPermission} || (!${scenario.enabled} && !${hasRealData}) || (${scenario.enabled} && !${hasRealData})`);
    });

    // Step 4: Test what the CORRECT logic should be
    console.log('\n4️⃣ What the CORRECT logic should be:');
    scenarios.forEach(scenario => {
        // CORRECT LOGIC: Show demo only if no permission OR disabled
        const correctShouldShowDemo = !scenario.hasPermission || !scenario.enabled;
        const correctIsShowingDemo = Boolean(correctShouldShowDemo && guild && member);
        
        console.log(`   ${scenario.description} (CORRECT):`);
        console.log(`     Should Show Demo: ${correctShouldShowDemo}`);
        console.log(`     Is Showing Demo: ${correctIsShowingDemo}`);
        console.log(`     Logic: !${scenario.hasPermission} || !${scenario.enabled}`);
    });

    // Step 5: Test actual buildExpContainer with current logic
    console.log('\n5️⃣ Testing buildExpContainer with current (broken) logic:');
    try {
        const container = await exp.buildExpContainer({
            subcomponent: 'levels',
            enabled: guildData?.exp?.enabled ?? false,
            guildData: guildData,
            guild: guild,
            statusMessage: null,
            hasPermission: true,
            member: member,
            commandChannel: null,
            user: member?.user,
            selectedLevelOption: null
        });
        
        console.log('   ✅ Container built successfully');
        
        // Check if it's showing demo data
        const containerData = JSON.stringify(container.data);
        const hasLevelText = containerData.includes('level 0:') || containerData.includes('level 1:');
        const hasDemoRoles = containerData.includes('@') && !containerData.includes('<@&');
        
        console.log(`   Has Level Text: ${hasLevelText}`);
        console.log(`   Has Demo Roles: ${hasDemoRoles}`);
        console.log(`   EXP Enabled in DB: ${guildData?.exp?.enabled}`);
        
    } catch (error) {
        console.log(`   ❌ Container building error: ${error.message}`);
    }

    // Step 6: Test button state logic
    console.log('\n6️⃣ Testing Button State Logic:');
    const currentEnabled = guildData?.exp?.enabled ?? false;
    const buttonId = currentEnabled ? 'exp-global-disable' : 'exp-global-enable';
    const buttonLabel = currentEnabled ? 'disable' : 'enable';
    
    console.log(`   Current Enabled: ${currentEnabled}`);
    console.log(`   Button ID: ${buttonId}`);
    console.log(`   Button Label: ${buttonLabel}`);
    console.log(`   Expected: If enabled=true, button should say 'disable' and have ID 'exp-global-disable'`);

    // Step 7: Simulate enable/disable cycle
    console.log('\n7️⃣ Simulating Enable/Disable Cycle:');
    
    // Force to disabled state
    console.log('   Setting to DISABLED...');
    await optimizedUpdateOne("guilds", { id: testGuildId }, { $set: { 'exp.enabled': false } });
    guildData = await optimizedFindOne('guilds', { id: testGuildId });
    console.log(`   Database state: ${guildData?.exp?.enabled}`);
    
    // Force to enabled state
    console.log('   Setting to ENABLED...');
    await optimizedUpdateOne("guilds", { id: testGuildId }, { $set: { 'exp.enabled': true } });
    guildData = await optimizedFindOne('guilds', { id: testGuildId });
    console.log(`   Database state: ${guildData?.exp?.enabled}`);

    console.log('\n🎯 Issues Found:');
    console.log('❌ Demo logic is wrong: shows demo even when enabled=true and no real data');
    console.log('❌ Should only show demo when: no permission OR disabled');
    console.log('❌ When enabled=true with no levels, should show "no levels created" not demo');
    
    console.log('\n💡 Required Fixes:');
    console.log('1. Fix demo logic: shouldShowDemo = !hasPermission || !enabled');
    console.log('2. Remove the "enabled && !hasRealData" condition');
    console.log('3. When enabled=true with no data, show empty state not demo');

    process.exit(0);
}

debugExpStateDeep().catch(console.error);
