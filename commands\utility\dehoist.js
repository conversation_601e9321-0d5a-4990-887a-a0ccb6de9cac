const { Container<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, MessageFlags } = require('discord.js');

const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require('../../utils/database-optimizer.js');
const { buildSelectMenu } = require('./featuresMenu');
const { safeBulkScan, checkBulkScanCooldown } = require('../../utils/dehoistRateLimit');
const { sendFeatureToggleLog, sendDehoistScanCompletedLog } = require("../../utils/sendLog.js");
const { getCachedGuildDehoistConfig, invalidateGuildDehoistConfig } = require('../../utils/dehoistCache.js');
const { defaults } = require("../../utils/default_db_structures.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');
const { getFeatureStatus } = require('../../utils/permissionHandler.js');

// Helper function to determine if guild has real dehoist data (not just defaults)
function hasRealDehoistData(dehoist) {
    if (!dehoist) return false;

    // Default values to compare against
    const defaultNames = ["Alien", "Pluto", "Neptune"];
    const defaultBlocked = ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "`", "{", "|", "}", "~"];

    // Check if names are customized
    if (dehoist.names && JSON.stringify(dehoist.names) !== JSON.stringify(defaultNames)) return true;

    // Check if blocked characters are customized
    if (dehoist.blocked && JSON.stringify(dehoist.blocked) !== JSON.stringify(defaultBlocked)) return true;

    // Check if there's scan history
    if (dehoist.lastScan) return true;

    return false; // No real data, just defaults
}

function buildDehoistContainer({ dehoist, enabled = true, hasPermission = true, member = null, commandChannel = null, guild = null, statusMessage = null }) {
    // Determine if we should show demo data
    const shouldShowDemo = !hasPermission || (!enabled && !hasRealDehoistData(dehoist));
    const isShowingDemo = shouldShowDemo && guild && member;

    if (isShowingDemo) {
        const { getDehoistDemoData } = require('../../utils/demoData.js');
        dehoist = getDehoistDemoData(guild, member, commandChannel);
        // Don't change enabled state - keep it as is for proper button disabling
    }
    // Ensure dehoist object has all required properties with defaults
    const safeDehost = {
        lastScan: null,
        dehoisted: 0,
        failed: 0,
        blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"],
        enabled: false, // Use standard enabled field
        ...dehoist // Override with actual values if they exist
    };

    const heading = new TextDisplayBuilder().setContent('# dehoist');
    const description = new TextDisplayBuilder().setContent('> remove hoisted chars from nicknames');
    // Show scan line above chars
    let scanLine = '**scan:** never';
    if (safeDehost.lastScan) {
        scanLine = `**scan:** <t:${Math.floor(safeDehost.lastScan / 1000)}:R>`;
        if (typeof safeDehost.dehoisted === 'number') {
            scanLine += ` dehoisted ${safeDehost.dehoisted}`;
        }
        if (typeof safeDehost.failed === 'number' && safeDehost.failed > 0) {
            scanLine += ` failed ${safeDehost.failed}`;
        }
    }
    const charsDisplay = new TextDisplayBuilder().setContent(`${scanLine}\n**char(s):**\n> ${safeDehost.blocked.join(' ')}`);
    // Add char(s) and scan all members buttons at the bottom of the container
    const charsButton = new ButtonBuilder()
        .setCustomId('dehoist-editchars')
        .setLabel('char(s)')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(!enabled || !safeDehost.enabled || !hasPermission || isShowingDemo); // Disable when showing demo data

    // Check if scan is on cooldown (24 hours)
    const scanButton = new ButtonBuilder()
        .setCustomId('dehoist-scan')
        .setLabel('scan all members')
        .setStyle(ButtonStyle.Primary)
        .setDisabled(!enabled || !safeDehost.enabled || !hasPermission || isShowingDemo); // Disable when showing demo data

    // Use the new rate limiting system for cooldown check
    const cooldownCheck = checkBulkScanCooldown(safeDehost.lastScan);
    if (!cooldownCheck.allowed) {
        scanButton
            .setDisabled(true)
            .setLabel(`scan all members (${cooldownCheck.timeLeft}h)`);
    }

    const buttonRow = new ActionRowBuilder().addComponents(charsButton, scanButton);
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, description, charsDisplay)
        .addSeparatorComponents(separator)
        .addActionRowComponents(buttonRow);

    // Add status message at the bottom if present (temporary feedback)
    if (statusMessage) {
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
        container.addTextDisplayComponents(statusDisplay);
    }

    container.setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    hasRealDehoistData,
    //data: new SlashCommandBuilder()
    //    .setName('dehoist')
    //    .setDescription('Configure Dehoist Filter!'),
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');

        // Use cached dehoist configuration for better performance
        const { getCachedGuildDehoistConfig } = require('../../utils/dehoistCache.js');
        let data = await getCachedGuildDehoistConfig(interaction.guild.id);

        // Ensure the configuration exists in database if it's using defaults
        if (!data.lastScan) {
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });

            if (!guildData || !guildData.dehoist) {
                const defaultDehoist = {
                    enabled: false, // Use standard enabled field
                    names: ["Alien", "Pluto", "Neptune"],
                    blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "`", "{", "|", "}", "~"],
                    lastScan: null
                };

                if (!guildData) {
                    await optimizedInsertOne("guilds", {
                        id: interaction.guild.id,
                        dehoist: defaultDehoist
                    });
                } else {
                    await optimizedUpdateOne("guilds",
                        { id: interaction.guild.id },
                        { $set: { dehoist: defaultDehoist } }
                    );
                }

                // Update cached data
                data = defaultDehoist;
            }
        }
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
        const container = buildDehoistContainer({
            dehoist: data,
            enabled: data.enabled, // Use standard enabled field
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            guild: interaction.guild
        });

        // Get permission-aware toggle button
        const featureStatus = getFeatureStatus(
            interaction.guild,
            interaction.member,
            'dehoist',
            data.enabled,
            'dehoist-disable'
        );

        // Add status message to container if there are permission issues
        if (featureStatus.statusMessage) {
            container.addTextDisplayComponents(featureStatus.statusMessage);
        }

        return [
            selectMenu,
            container,
            new ActionRowBuilder().addComponents(featureStatus.button)
        ];
        }, {
            autoDefer: true, // Auto-defer for execute function as it may be slow
            ephemeral: true,
            fallbackMessage: '❌ Something went wrong loading the dehoist interface. Please try again.'
        });
    },

    /**
     * @param { ButtonInteraction<"cached"> } interaction
     * @param { string[] } args
    */
    async buttons(interaction, args) {
        return handleUIOperation(interaction, async (interaction) => {
            const [action] = args;
        console.log('[dehoist.buttons] Action:', action);

        // Check permissions for all button actions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');
        if (!hasPermission) {
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({
                dehoist: {}, // Will be replaced by demo data
                enabled: true,
                hasPermission: false,
                member: interaction.member,
                commandChannel: interaction.channel,
                guild: interaction.guild
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            return [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ];
        }
        // PERFORMANCE OPTIMIZATION: Use optimized database operations with monitoring and retry logic
        var guildData = await optimizedFindOne('guilds', { id: interaction.guild.id });
        if (guildData == null) {
            await optimizedInsertOne('guilds', {
                    id: interaction.guild.id,
                    dehoist: {
                        enabled: false, // Use standard enabled field
                        names: ["Alien", "Pluto", "Neptune"],
                        blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"]
                    },
            });
            guildData = await optimizedFindOne('guilds', { id: interaction.guild.id });
        }

        // Ensure dehoist object exists with defaults
        if (!guildData.dehoist) {
            const defaultDehoist = {
                enabled: false, // Use standard enabled field
                names: ["Alien", "Pluto", "Neptune"],
                blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"]
            };
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { dehoist: defaultDehoist } }
            );
            guildData.dehoist = defaultDehoist;
        }

        let data = guildData.dehoist;
        const charsButton = new ButtonBuilder()
            .setCustomId('dehoist-editchars')
            .setLabel('char(s)')
            .setStyle(ButtonStyle.Secondary);
        const scanButton = new ButtonBuilder()
            .setCustomId('dehoist-scan')
            .setLabel('scan all members')
            .setStyle(ButtonStyle.Primary);
        if (action === 'disable') {
            data.enabled = false; // Use standard enabled field
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'dehoist.enabled': false } });

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Dehoist',
                null,
                false,
                interaction.user.id,
                interaction.client
            );
            const enableButton = new ButtonBuilder()
                .setCustomId('dehoist-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: data, enabled: data.enabled }); // Use standard enabled field
            return [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(enableButton)
            ];
        } else if (action === 'enable') {
            data.enabled = true; // Use standard enabled field
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'dehoist.enabled': true } });

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Dehoist',
                null,
                true,
                interaction.user.id,
                interaction.client
            );
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: data, enabled: data.enabled }); // Use standard enabled field
            return [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ];
        } else if (action === 'scan') {
            // Check if dehoist is enabled for this guild
            if (!data.enabled) { // Use standard enabled field
                // Feature is disabled, don't process the scan
                const container = buildDehoistContainer({ dehoist: data, enabled: false });
                const enableButton = new ButtonBuilder()
                    .setCustomId('dehoist-enable')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
            }

            // Check cooldown using new rate limiting system
            const cooldownCheck = checkBulkScanCooldown(data.lastScan);
            if (!cooldownCheck.allowed) {
                // Build container with status message instead of ephemeral reply
                const container = buildDehoistContainer({ dehoist: data, enabled: true });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                const statusContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent(`**status:** Please wait ${cooldownCheck.timeLeft} hours before scanning again.`))
                    .setAccentColor(OPERATION_COLORS.WARNING);

                return [selectMenu, container, statusContainer];
            }

            console.log('[dehoist] Starting enhanced bulk scan with rate limiting...');
            // CONVERTED: Removed manual deferUpdate - Universal Interaction Manager handles this automatically

            // Use the new safe bulk scan function
            const results = await safeBulkScan(interaction.guild, data, (progress) => {
                // Log progress every 5 batches to avoid spam
                if (progress.batch % 5 === 0) {
                    console.log(`[dehoist] Progress: ${progress.processed}/${progress.total} (${progress.dehoisted} dehoisted, ${progress.failed} failed)`);
                }
            });

            // Update lastScan timestamp and results in DB
            const now = Date.now();
            await optimizedUpdateOne("guilds", {
                id: interaction.guild.id
            }, {
                $set: {
                    'dehoist.lastScan': now,
                    'dehoist.dehoisted': results.dehoisted,
                    'dehoist.failed': results.failed
                }
            });
            data.lastScan = now;
            data.dehoisted = results.dehoisted;
            data.failed = results.failed;

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            // Send dehoist scan completed log
            await sendDehoistScanCompletedLog(
                interaction.guild.id,
                interaction.user.id,
                results.totalMembers,
                results.dehoisted,
                results.failed,
                interaction.client
            );
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: data, enabled: data.enabled }); // Use standard enabled field
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            return [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ];
        } else if (action === 'editchars') {
            // Check if dehoist is enabled for this guild
            if (!data.enabled) { // Use standard enabled field
                // Feature is disabled, don't process the edit
                const container = buildDehoistContainer({ dehoist: data, enabled: false });
                const enableButton = new ButtonBuilder()
                    .setCustomId('dehoist-enable')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
            }

            console.log('[dehoist.buttons] Showing edit chars modal');
            const modal = new ModalBuilder()
                .setCustomId('dehoist-editchars-modal')
                .setTitle('Edit Hoisted Characters')
                .addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('chars')
                            .setLabel('Characters (space separated)')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(data.blocked.join(' '))
                            .setRequired(true)
                    )
                );
            await interaction.showModal(modal);
            return;
        }
        // Update UI
        const container = buildDehoistContainer({ dehoist: data, enabled: data.enabled }); // Use standard enabled field
        const disableButton = new ButtonBuilder()
            .setCustomId('dehoist-disable')
            .setLabel('disable')
            .setStyle(ButtonStyle.Danger);
        return [
            container,
            new ActionRowBuilder().addComponents(disableButton)
        ];
        }, {
            autoDefer: false, // Don't auto-defer for button presses - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your button press. Please try again.'
        });
    },
    async modal(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.customId === 'dehoist-editchars-modal') {
            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');
            if (!hasPermission) {
                // Show demo mode if no permission
                const container = buildDehoistContainer({
                    dehoist: {}, // Will be replaced by demo data
                    enabled: true,
                    hasPermission: false,
                    member: interaction.member,
                    commandChannel: interaction.channel,
                    guild: interaction.guild
                });
                const disableButton = new ButtonBuilder()
                    .setCustomId('dehoist-disable')
                    .setLabel('disable')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(true);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                return [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)];
            }

            const chars = interaction.fields.getTextInputValue('chars').split(/\s+/).filter(Boolean);
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const data = guildData.dehoist;

            // Check if dehoist is enabled for this guild
            if (!data.enabled) { // Use standard enabled field
                // Feature is disabled, don't process the modal
                const container = buildDehoistContainer({ dehoist: data, enabled: false });
                const enableButton = new ButtonBuilder()
                    .setCustomId('dehoist-enable')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
            }

            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'dehoist.blocked': chars } });

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            const updatedGuildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const updatedData = updatedGuildData.dehoist;
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: updatedData, enabled: updatedData.enabled }); // Use standard enabled field
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            return [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ];
        }
        }, {
            autoDefer: false, // Don't auto-defer for modal submissions - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your form submission. Please try again.'
        });
    },

    async selectMenu(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');

        var guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (guildData == null) {
            await optimizedInsertOne("guilds", defaults.guild(interaction.guild.id))
            guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        }

        // Initialize dehoist data if it doesn't exist
        if (!guildData.dehoist) {
            const defaultDehoist = {
                enabled: false, // Use standard enabled field
                names: ["Alien", "Pluto", "Neptune"], // Add missing names field
                blocked: ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '=', '+', '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/', '~', '`'],
                lastScan: null
            };
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { dehoist: defaultDehoist } }
            );
            guildData.dehoist = defaultDehoist;
        }

        let data = guildData.dehoist;
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
        const container = buildDehoistContainer({
            dehoist: data,
            enabled: data.enabled, // Use standard enabled field
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            guild: interaction.guild
        });
        const disableButton = new ButtonBuilder()
            .setCustomId('dehoist-disable')
            .setLabel('disable')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!hasPermission);
        return [
            selectMenu,
            container,
            new ActionRowBuilder().addComponents(disableButton)
        ];
        }, {
            autoDefer: false, // Don't auto-defer for select menus - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your selection. Please try again.'
        });
    },

    // Export container builder for testing purposes
    buildDehoistContainer: buildDehoistContainer
};