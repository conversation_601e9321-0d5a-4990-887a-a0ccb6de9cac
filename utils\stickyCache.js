/**
 * Sticky System Caching Utility (Enterprise-Grade Performance Optimized)
 * Provides caching for frequently accessed sticky data to improve performance
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

const { optimizedFindOne, optimizedBulkWrite, optimizedFind, optimizedAggregate } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const stickyCacheMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    configsServed: 0,
    memberDataServed: 0,
    roleValidationsServed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildStickyConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes
const memberStickyDataCache = CacheFactory.createUserCache();   // 2000 entries, 5 minutes
const roleValidationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes
const stickyStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes

// Register caches for global cleanup
registerCache(guildStickyConfigCache);
registerCache(memberStickyDataCache);
registerCache(roleValidationCache);
registerCache(stickyStatsCache);

// Cache TTL settings (now handled by LRU cache instances)
const CACHE_TTL = {
    GUILD_CONFIG: 10 * 60 * 1000,    // 10 minutes
    MEMBER_DATA: 5 * 60 * 1000,      // 5 minutes
    ROLE_VALIDATION: 15 * 60 * 1000  // 15 minutes
};

/**
 * Get cached guild sticky configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild sticky configuration
 */
async function getCachedGuildStickyConfig(guildId) {
    const startTime = Date.now();

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = guildStickyConfigCache.get(guildId);
    if (cached) {
        stickyCacheMetrics.cacheHits++;
        stickyCacheMetrics.configsServed++;
        if (stickyCacheMetrics.verboseLogging) {
            console.log(`[stickyCache] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    stickyCacheMetrics.cacheMisses++;
    stickyCacheMetrics.databaseQueries++;

    try {
        const guildData = await optimizedFindOne("guilds",
            { id: guildId },
            { projection: { sticky: 1 } } // Only fetch sticky configuration
        );

        // Default sticky configuration - consistent with database structure
        const defaultConfig = {
            enabled: false, // Disabled by default - consistent with new pattern
            roles: [],
            nick: null // Use null to match database structure
        };

        const config = guildData?.sticky || defaultConfig;

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        guildStickyConfigCache.set(guildId, config);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        stickyCacheMetrics.averageQueryTime =
            (stickyCacheMetrics.averageQueryTime * (stickyCacheMetrics.databaseQueries - 1) + duration) /
            stickyCacheMetrics.databaseQueries;

        if (stickyCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[stickyCache] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        stickyCacheMetrics.configsServed++;
        return config;
    } catch (error) {
        console.error(`[stickyCache] ❌ Error getting guild config for ${guildId}:`, error);
        return {
            enabled: false, // Consistent default
            roles: [],
            nick: null // Use null to match database structure
        };
    }
}

/**
 * Get cached member sticky data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Member sticky data
 */
async function getCachedMemberStickyData(userId, guildId) {
    const startTime = Date.now();
    const cacheKey = `${userId}_${guildId}`;

    const cached = memberStickyDataCache.get(cacheKey);
    if (cached) {
        stickyCacheMetrics.cacheHits++;
        stickyCacheMetrics.memberDataServed++;
        if (stickyCacheMetrics.verboseLogging) {
            console.log(`[stickyCache] ⚡ Member data cache hit for ${userId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    stickyCacheMetrics.cacheMisses++;
    stickyCacheMetrics.databaseQueries++;

    try {
        const memberData = await optimizedFindOne("member",
            { userId: userId, guildId: guildId },
            { projection: { sticky: 1 } } // Only fetch sticky data
        );

        // Default member sticky data
        const defaultData = {
            roles: [],
            nick: null
        };

        const data = memberData?.sticky || defaultData;

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        memberStickyDataCache.set(cacheKey, data);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        stickyCacheMetrics.averageQueryTime =
            (stickyCacheMetrics.averageQueryTime * (stickyCacheMetrics.databaseQueries - 1) + duration) /
            stickyCacheMetrics.databaseQueries;

        if (stickyCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[stickyCache] ✅ Member data fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        stickyCacheMetrics.memberDataServed++;
        return data;
    } catch (error) {
        console.error(`[stickyCache] ❌ Error getting member data for ${userId}:`, error);
        return {
            roles: [],
            nick: null
        };
    }
}

/**
 * Get cached role validation data for a guild (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @param {Object} guild - Discord guild object
 * @returns {Object} Role validation data
 */
function getCachedRoleValidation(guildId, guild) {
    const startTime = Date.now();

    const cached = roleValidationCache.get(guildId);
    if (cached) {
        stickyCacheMetrics.cacheHits++;
        stickyCacheMetrics.roleValidationsServed++;
        if (stickyCacheMetrics.verboseLogging) {
            console.log(`[stickyCache] ⚡ Role validation cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    stickyCacheMetrics.cacheMisses++;

    try {
        const botMember = guild.members.me;
        const botRole = botMember?.roles?.highest;

        if (!botRole) {
            return { validRoles: new Map(), botPosition: 0 };
        }

        const validRoles = new Map();

        // Cache role validation data
        guild.roles.cache.forEach(role => {
            const isValid = role.editable &&
                           role.id !== guild.id &&
                           botRole.position > role.position;

            validRoles.set(role.id, {
                valid: isValid,
                name: role.name,
                position: role.position,
                editable: role.editable
            });
        });

        const validationData = {
            validRoles: validRoles,
            botPosition: botRole.position,
            guildId: guild.id
        };

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        roleValidationCache.set(guildId, validationData);

        const duration = Date.now() - startTime;
        if (stickyCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[stickyCache] ✅ Role validation for ${guildId}: ${validRoles.size} roles (${duration}ms) - cached for future access`);
        }

        stickyCacheMetrics.roleValidationsServed++;
        return validationData;
    } catch (error) {
        console.error(`[stickyCache] ❌ Error validating roles for guild ${guildId}:`, error);
        return { validRoles: new Map(), botPosition: 0 };
    }
}

/**
 * Filter assignable roles using cached validation data
 * @param {Array} roleIds - Array of role IDs to filter
 * @param {string} guildId - Guild ID
 * @param {Object} guild - Discord guild object
 * @returns {Array} Array of assignable role IDs
 */
function getAssignableRoles(roleIds, guildId, guild) {
    const validation = getCachedRoleValidation(guildId, guild);
    
    return roleIds.filter(roleId => {
        const roleData = validation.validRoles.get(roleId);
        return roleData && roleData.valid;
    });
}

/**
 * Batch update member sticky data
 * @param {Array} updates - Array of {userId, guildId, stickyData} objects
 * @returns {Promise<void>}
 */
async function batchUpdateMemberStickyData(updates) {
    if (updates.length === 0) return;
    
    try {
        // Use bulk operations for better performance
        const bulkOps = updates.map(({ userId, guildId, stickyData }) => ({
            updateOne: {
                filter: { userId: userId, guildId: guildId },
                update: { $set: { sticky: stickyData } },
                upsert: true
            }
        }));

        await optimizedBulkWrite("member", bulkOps);
        
        // Invalidate caches for updated members
        updates.forEach(({ userId, guildId }) => {
            const cacheKey = `${userId}_${guildId}`;
            memberStickyDataCache.delete(cacheKey);
        });
        
        console.log(`[stickyCache] Batch updated ${updates.length} member sticky data records`);
    } catch (error) {
        console.error('[stickyCache] Error in batch update:', error);
    }
}

/**
 * Invalidate guild sticky config cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildStickyConfig(guildId) {
    guildStickyConfigCache.delete(guildId);
}

/**
 * Invalidate member sticky data cache
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 */
function invalidateMemberStickyData(userId, guildId) {
    const cacheKey = `${userId}_${guildId}`;
    memberStickyDataCache.delete(cacheKey);
}

/**
 * Invalidate role validation cache
 * @param {string} guildId - Guild ID
 */
function invalidateRoleValidation(guildId) {
    roleValidationCache.delete(guildId);
}

// REMOVED: Manual cleanup function no longer needed
// LRUCache handles automatic cleanup and TTL management

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
function getCacheStats() {
    return {
        guildConfig: {
            size: guildStickyConfigCache.size,
            maxAge: CACHE_TTL.GUILD_CONFIG
        },
        memberData: {
            size: memberStickyDataCache.size,
            maxAge: CACHE_TTL.MEMBER_DATA
        },
        roleValidation: {
            size: roleValidationCache.size,
            maxAge: CACHE_TTL.ROLE_VALIDATION
        }
    };
}

/**
 * Batch get multiple guild configurations (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing with Promise.allSettled for comprehensive error recovery
 * @param {Array} guildIds - Array of guild IDs to fetch
 * @returns {Promise<Array>} Array of guild configurations (default config for failed fetches)
 */
async function batchGetGuildConfigs(guildIds) {
    const startTime = Date.now();
    stickyCacheMetrics.parallelOperations++;

    try {
        // OPTIMIZED: Use Promise.allSettled for comprehensive error handling
        const configResults = await Promise.allSettled(
            guildIds.map(guildId => getCachedGuildStickyConfig(guildId))
        );

        const configs = configResults.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            } else {
                stickyCacheMetrics.partialFailures++;
                if (stickyCacheMetrics.verboseLogging) {
                    console.warn(`[stickyCache] ⚠️  Failed to fetch config for guild ${guildIds[index]}: ${result.reason}`);
                }
                return { enabled: false, roles: [], nick: null }; // Consistent default
            }
        });

        const duration = Date.now() - startTime;
        const successCount = configs.filter(config => config !== null).length;
        const failureCount = configs.length - successCount;

        if (stickyCacheMetrics.verboseLogging || failureCount > 0) {
            console.log(`[stickyCache] ✅ Batch fetched ${successCount}/${configs.length} guild configs (${failureCount} failures) in ${duration}ms`);
        }

        return configs;
    } catch (error) {
        console.error('[stickyCache] ❌ Error in batch get guild configs:', error);
        return guildIds.map(() => ({ enabled: false, roles: [], nick: null })); // Consistent default
    }
}

/**
 * Get sticky statistics with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Aggregation pipeline with intelligent caching
 * @param {string} guildId - Guild ID (null for global)
 * @returns {Promise<Object>} Sticky statistics
 */
async function getCachedStickyStats(guildId = null) {
    const startTime = Date.now();
    const cacheKey = `sticky_stats_${guildId || 'global'}`;

    // Check cache first
    const cached = stickyStatsCache.get(cacheKey);
    if (cached) {
        stickyCacheMetrics.cacheHits++;
        if (stickyCacheMetrics.verboseLogging) {
            console.log(`[stickyCache] ⚡ Sticky stats cache hit for ${guildId || 'global'} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    stickyCacheMetrics.cacheMisses++;
    stickyCacheMetrics.databaseQueries++;

    try {
        // OPTIMIZED: Use aggregation pipeline for efficient statistics
        const pipeline = [
            {
                $match: {
                    "sticky.enabled": true,
                    ...(guildId && { id: guildId })
                }
            },
            {
                $group: {
                    _id: null,
                    totalGuilds: { $sum: 1 },
                    avgRoles: { $avg: { $size: "$sticky.roles" } },
                    guildsWithNick: {
                        $sum: {
                            $cond: [{ $eq: ["$sticky.nick", true] }, 1, 0]
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalGuilds: 1,
                    avgRoles: { $round: ["$avgRoles", 2] },
                    guildsWithNick: 1
                }
            }
        ];

        const statsResult = await optimizedAggregate('guilds', pipeline);
        const stats = statsResult.length > 0 ? statsResult[0] : {
            totalGuilds: 0,
            avgRoles: 0,
            guildsWithNick: 0
        };

        // Cache the result
        stickyStatsCache.set(cacheKey, stats);

        const duration = Date.now() - startTime;
        if (stickyCacheMetrics.verboseLogging || duration > 150) {
            console.log(`[stickyCache] ✅ Sticky stats calculated for ${guildId || 'global'}: ${duration}ms - cached for future access`);
        }

        return stats;
    } catch (error) {
        console.error(`[stickyCache] ❌ Error getting sticky stats for ${guildId || 'global'}:`, error);
        return {
            totalGuilds: 0,
            avgRoles: 0,
            guildsWithNick: 0
        };
    }
}

/**
 * Preload member data for active members (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced parallel processing with comprehensive error handling
 * @param {Array} members - Array of {userId, guildId} objects
 */
async function preloadMemberData(members) {
    const startTime = Date.now();

    try {
        stickyCacheMetrics.databaseQueries++;
        stickyCacheMetrics.parallelOperations++;

        const memberQueries = members.map(({ userId, guildId }) => ({ userId, guildId }));

        const memberData = await optimizedFind("member",
            { $or: memberQueries },
            { projection: { userId: 1, guildId: 1, sticky: 1 } }
        );

        const defaultData = { roles: [], nick: null };

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        memberData.forEach(member => {
            const cacheKey = `${member.userId}_${member.guildId}`;
            memberStickyDataCache.set(cacheKey, member.sticky || defaultData);
        });

        // Cache default data for members not found
        members.forEach(({ userId, guildId }) => {
            const cacheKey = `${userId}_${guildId}`;
            if (!memberStickyDataCache.has(cacheKey)) {
                memberStickyDataCache.set(cacheKey, defaultData);
            }
        });

        const duration = Date.now() - startTime;
        stickyCacheMetrics.averageQueryTime =
            (stickyCacheMetrics.averageQueryTime * (stickyCacheMetrics.databaseQueries - 1) + duration) /
            stickyCacheMetrics.databaseQueries;

        if (stickyCacheMetrics.verboseLogging || duration > 200) {
            console.log(`[stickyCache] ✅ Preloaded ${members.length} member data records in ${duration}ms`);
        }
    } catch (error) {
        console.error('[stickyCache] ❌ Error preloading member data:', error);
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = stickyCacheMetrics.cacheHits + stickyCacheMetrics.cacheMisses > 0 ?
        (stickyCacheMetrics.cacheHits / (stickyCacheMetrics.cacheHits + stickyCacheMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: stickyCacheMetrics.cacheHits,
            cacheMisses: stickyCacheMetrics.cacheMisses,
            databaseQueries: stickyCacheMetrics.databaseQueries,
            averageQueryTime: `${stickyCacheMetrics.averageQueryTime.toFixed(2)}ms`,
            configsServed: stickyCacheMetrics.configsServed,
            memberDataServed: stickyCacheMetrics.memberDataServed,
            roleValidationsServed: stickyCacheMetrics.roleValidationsServed,
            parallelOperations: stickyCacheMetrics.parallelOperations,
            partialFailures: stickyCacheMetrics.partialFailures,
            lastOptimization: new Date(stickyCacheMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: guildStickyConfigCache.getStats(),
            memberData: memberStickyDataCache.getStats(),
            roleValidation: roleValidationCache.getStats(),
            stickyStats: stickyStatsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildConfig: guildStickyConfigCache.getStats().memoryUsage,
            memberData: memberStickyDataCache.getStats().memoryUsage,
            roleValidation: roleValidationCache.getStats().memoryUsage,
            stickyStats: stickyStatsCache.getStats().memoryUsage,
            total: guildStickyConfigCache.getStats().memoryUsage +
                   memberStickyDataCache.getStats().memoryUsage +
                   roleValidationCache.getStats().memoryUsage +
                   stickyStatsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, stickyCacheMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 150) {
        recommendations.push('Sticky operations are slow - investigate database performance');
    }

    if (metrics.partialFailures > metrics.parallelOperations * 0.1) {
        recommendations.push('High partial failure rate - investigate system reliability');
    }

    if (metrics.parallelOperations < metrics.databaseQueries * 0.3) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (metrics.memberDataServed > metrics.configsServed * 5) {
        recommendations.push('High member data to config ratio - consider member-specific optimizations');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    stickyCacheMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[stickyCache] 📊 Performance Report:`);
    console.log(`[stickyCache]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[stickyCache]   Configs Served: ${stats.performance.configsServed}`);
    console.log(`[stickyCache]   Member Data Served: ${stats.performance.memberDataServed}`);
    console.log(`[stickyCache]   Role Validations Served: ${stats.performance.roleValidationsServed}`);
    console.log(`[stickyCache]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[stickyCache]   Partial Failures: ${stats.performance.partialFailures}`);
    console.log(`[stickyCache]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[stickyCache]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[stickyCache]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[stickyCache] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all sticky caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllStickyCaches() {
    guildStickyConfigCache.clear();
    memberStickyDataCache.clear();
    roleValidationCache.clear();
    stickyStatsCache.clear();

    console.log('[stickyCache] 🗑️ Cleared all sticky caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, stickyCacheMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getCachedGuildStickyConfig,
    getCachedMemberStickyData,
    getCachedRoleValidation,
    getAssignableRoles,
    batchUpdateMemberStickyData,
    invalidateGuildStickyConfig,
    invalidateMemberStickyData,
    invalidateRoleValidation,
    preloadMemberData,

    // Enhanced optimization functions
    batchGetGuildConfigs,
    getCachedStickyStats,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllStickyCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...stickyCacheMetrics })
};
