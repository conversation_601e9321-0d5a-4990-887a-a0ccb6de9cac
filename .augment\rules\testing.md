---
type: "always_apply"
---

# 🧪 Bot Testing Standards - Use Shared Test Base

**IMPORTANT**: This repository uses a shared test infrastructure to eliminate code duplication and ensure consistent testing. Always use the `BotTestBase` class for new tests.

## 🚀 Creating New Tests

### 1. **Use the Shared Test Base** (Required)
```javascript
const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class MyFeatureTester extends BotTestBase {
    constructor() {
        super('MyFeatureTest'); // Descriptive test name
    }

    async testMyFeature() {
        // Your test logic here
        const interaction = this.createMockInteraction(2, 'my-command');
        const command = require('../commands/utility/my-command.js');
        await command.execute(interaction);
        return interaction._responses.length > 0;
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'My feature', test: () => this.testMyFeature() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
    }
}
```

### 2. **What BotTestBase Provides** (Automatic Setup)
- ✅ **Discord client login** with proper intents using `process.env.TOKEN`
- ✅ **MongoDB connection** using `./mongo/client.js`
- ✅ **Test environment** with `process.env.GUILDIDTWO` and `process.env.OWNER`
- ✅ **Mock interactions** with Components v2 validation
- ✅ **Error handling** and result tracking
- ✅ **Resource cleanup** and proper teardown

4. Follow the examples in `docs/comprehensive-testing-guide.md` (see lines 34‑59 for the setup code) when building interaction objects and test flows

5. Place new tests in the `tests/` directory and ensure they run via `npm test` as shown in `tests/README.md`

These instructions ensure each test uses the existing `.env` values, logs into Discord, and matches the repository’s comprehensive testing approach.

### 3. **Mock Interaction Types**
```javascript
// Slash command (type 2)
const slashInteraction = this.createMockInteraction(2, 'command-name');

// Button click (type 3)
const buttonInteraction = this.createMockInteraction(3, 'button-custom-id');

// Select menu (type 5)
const selectInteraction = this.createMockInteraction(5, 'select-custom-id', ['option1']);

// Modal submit (type 6)
const modalInteraction = this.createMockInteraction(6, 'modal-custom-id', {
    'field-id': 'field-value'
});
```

### 4. **Test Execution Pattern**
```javascript
// Main execution function
async function runMyFeatureTest() {
    const tester = new MyFeatureTester();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runMyFeatureTest();
}
```

### 5. **Available Test Scripts**
```bash
# Run comprehensive tests (recommended)
npm test

# Run feature-specific tests
npm run test:exp      # EXP system testing
npm run test:items    # Items system testing

# Create new feature tests in tests/ directory
# Follow examples in tests/examples/test_example_feature.js
```

**Key Points:**
- Use the shared `BotTestBase` class for all new tests
- Place new tests in the `tests/` directory
- Follow examples in `tests/shared/README.md` and `tests/examples/`
- Tests automatically connect to Discord and MongoDB using `.env` values

## 📋 Test Coverage Requirements

6. All new code changes must be covered by an interaction test.

- Every slash command, button, select menu, and modal must have a corresponding mock interaction in the comprehensive test suites.
- Tests should cover both successful operations and error scenarios.
- Any pull request or change without test coverage will be flagged by Augment.

To verify:
```bash
npm test
echo $?  # 0 = All tested and passed
```

This guarantees every interaction is testable, reproducible, and safe to deploy.
