# 🌍 Guild Context Flow for Global Items

## 📋 Overview

This document explains how guild context is preserved and used for global items in the hybrid item drop system, ensuring that DM messages show the correct server name where global items were found.

## 🎯 Core Principle

**Global items (guildId: null) should show the actual server name in DMs, not "Server"**

Example:
- ❌ **Wrong**: "You found a 🧠 **Memory Core** in Server, dropped from text chat:"
- ✅ **Correct**: "You found a 🧠 **Memory Core** in **19 testing**, dropped from text chat:"

## 🔄 Context Flow Patterns

### **Pattern 1: Regular Item Drops (TEXT/VOICE)**

```javascript
// events/messageCreate.js & events/voiceStateUpdate.js
const droppedItems = await processItemDrops(
    userId, 
    guildId,        // ← Guild where drop occurred
    location, 
    expGained, 
    client
);

// Inside processItemDrops → processItemNotifications
if (droppedItem.guildId === null) {
    // Global item: Use guildId as contextGuildId for server name
    await sendItemDropDM(userId, guildId, [droppedItem], location, client);
}
```

### **Pattern 2: Starfall Items**

```javascript
// utils/starfall.js - processStarfallItemDrop
async function processStarfallItemDrop(userId, guildId = null, client = null) {
    // ... item selection logic ...
    
    // FIXED: Pass guildId as contextGuildId
    await processItemNotifications(userId, guildId, droppedItem, 'STARFALL', client);
    //                                    ↑
    //                            Guild where starfall was claimed
}

// Called from starfall claim interaction
const itemResult = await processStarfallItemDrop(
    userId, 
    interaction.guild?.id,  // ← Guild context from interaction
    interaction.client
);
```

### **Pattern 3: Global Level-Up Items**

```javascript
// utils/globalLevelNotifications.js
async function processGlobalLevelUp(userId, levelUpData, client, contextGuildId = null) {
    // ... notification logic ...
    
    // FIXED: Pass contextGuildId for server name display
    await processItemNotifications(userId, contextGuildId, awardedItem, 'LEVEL_UP', client);
    //                                    ↑
    //                            Guild where level-up occurred
}

// Called from EXP events with guild context
await processGlobalLevelUp(
    userId, 
    globalResult, 
    client, 
    message.guild.id  // ← Guild context from message/voice event
);
```

## 🏗️ Technical Implementation

### **Key Data Structure**

```javascript
const inventoryItem = {
    guildId: itemData.guildId,        // Where item belongs (null = global)
    foundInGuild: contextGuildId,     // Where item was found (server context)
    // ... other fields
};
```

### **DM Message Building**

```javascript
// utils/itemDropsHybrid.js - buildItemDMMessage
async function buildItemDMMessage(droppedItem, contextGuildId, location, client) {
    let serverName = 'Server'; // Fallback
    
    if (contextGuildId && client) {
        try {
            const guild = await client.guilds.fetch(contextGuildId);
            serverName = guild.name; // ← Actual server name from context
        } catch (error) {
            console.error('Could not fetch guild name:', error);
            serverName = 'Server';
        }
    }
    
    // Template: "You found {items} in {server}, dropped from {location}:"
    const contextMessage = dmMessageTemplate
        .replace('{server}', serverName)  // ← Uses actual server name
        // ... other replacements
}
```

## 🛡️ Safety Mechanisms

### **Separation Logic Integrity**

```javascript
// Global items NEVER go to guild channels, only DMs + notification center
if (droppedItem.guildId === null) {
    await Promise.allSettled([
        addItemDropNotification(userId, null, [droppedItem], location),
        sendItemDropDM(userId, contextGuildId, [droppedItem], location, client)
        //                    ↑
        //            Uses context for server name, but item stays global
    ]);
    return; // SAFETY: Never reaches guild channel logic
}
```

### **Cross-Contamination Prevention**

- Global items (guildId: null) are stored as global in inventory
- Guild context (contextGuildId) is only used for display purposes
- Items cannot "leak" between guilds due to context confusion

## 📊 Context Sources by Drop Type

| Drop Type | Context Source | Example |
|-----------|----------------|---------|
| **TEXT EXP** | `message.guild.id` | User types in #general → guild context |
| **VOICE EXP** | `guild.id` from voice state | User joins voice channel → guild context |
| **STARFALL** | `interaction.guild?.id` | User claims starfall → guild context |
| **LEVEL_UP** | Passed from EXP event | Level-up occurs in guild → guild context |

## 🔧 Migration Checklist

When implementing guild context flow:

1. **✅ Identify Context Source**: Where does the guild ID come from?
2. **✅ Update Function Signatures**: Add contextGuildId parameter if needed
3. **✅ Pass Context Through**: Ensure guild ID flows to processItemNotifications
4. **✅ Test Server Name Display**: Verify DMs show correct server name
5. **✅ Verify Separation Logic**: Ensure global items don't go to guild channels

## 🧪 Testing Guild Context Flow

```javascript
// Test that global items show correct server name
const mockGlobalItem = {
    guildId: null,              // Global item
    itemName: 'Test Item',
    // ... other fields
};

// Should show actual server name in DM
await processItemNotifications(
    userId, 
    testGuildId,                // ← Context guild for server name
    mockGlobalItem, 
    'TEST', 
    client
);
```

## 🎯 Best Practices

1. **Always Pass Guild Context**: Even for global items, pass the guild where they were found
2. **Use Descriptive Parameter Names**: `contextGuildId` is clearer than just `guildId`
3. **Provide Fallbacks**: Handle cases where guild context might be unavailable
4. **Test Edge Cases**: What happens if guild is deleted or bot is kicked?
5. **Document Context Flow**: Make it clear where guild context comes from

## 🚨 Common Pitfalls

1. **❌ Passing null for Global Items**: Don't pass null just because item is global
2. **❌ Confusing Item Guild vs Context Guild**: Item's guildId ≠ contextGuildId
3. **❌ Missing Client Parameter**: Can't fetch server name without Discord client
4. **❌ Not Handling Guild Fetch Errors**: Always have fallback server name

The guild context flow ensures users always see meaningful server names in their DMs, improving the user experience while maintaining the integrity of the global/guild item separation system.
