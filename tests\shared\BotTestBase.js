/**
 * Shared Bot Test Base - Reusable Testing Infrastructure
 * 
 * This module provides a reusable base for all bot tests, including:
 * - Discord client initialization and login
 * - MongoDB connection setup
 * - Mock interaction creation with Components v2 validation
 * - Test environment configuration
 * - Common test utilities and helpers
 */

require('dotenv').config();

const { Client, GatewayIntentBits, MessageFlags } = require('discord.js');

/**
 * Test Configuration Constants
 */
const TEST_CONFIG = {
    GUILD_ID: process.env.GUILDIDTWO || '417175807795134475',
    USER_ID: process.env.OWNER || '97757532835033088',
    BOT_TOKEN: process.env.TOKEN,
    MONGO_URI: process.env.MONGO,
    TIMEOUT: 30000, // 30 seconds for test operations
    INTERACTION_TIMEOUT: 3000 // Discord's 3-second interaction limit
};

/**
 * Base class for all bot tests
 * Provides common setup, teardown, and utility methods
 */
class BotTestBase {
    constructor(testName = 'BotTest') {
        this.testName = testName;
        this.client = null;
        this.testGuild = null;
        this.testUser = null;
        this.testChannel = null;
        this.mongoClient = null;
        this.testResults = [];
        this.startTime = null;
        this.initialized = false;
    }

    /**
     * Initialize the test environment
     * Sets up Discord client, MongoDB connection, and test entities
     */
    async initialize() {
        console.log(`🤖 Initializing ${this.testName}...`);
        this.startTime = Date.now();

        try {
            // Initialize MongoDB connection
            await this.initializeMongoDB();
            
            // Initialize Discord client
            await this.initializeDiscordClient();
            
            // Setup test entities
            await this.setupTestEntities();
            
            this.initialized = true;
            console.log(`✅ ${this.testName} initialization complete`);
            
        } catch (error) {
            console.error(`❌ ${this.testName} initialization failed:`, error.message);
            throw error;
        }
    }

    /**
     * Initialize MongoDB connection
     */
    async initializeMongoDB() {
        try {
            const { mongoClient } = require('../../mongo/client.js');
            this.mongoClient = mongoClient;
            
            // Test the connection
            await this.mongoClient.db('test').admin().ping();
            console.log('✅ MongoDB connection established');
            
        } catch (error) {
            throw new Error(`Failed to connect to MongoDB: ${error.message}`);
        }
    }

    /**
     * Initialize Discord client and login
     */
    async initializeDiscordClient() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers,
                GatewayIntentBits.GuildVoiceStates
            ]
        });

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Discord client login timeout'));
            }, TEST_CONFIG.TIMEOUT);

            this.client.once('ready', () => {
                clearTimeout(timeout);
                console.log(`✅ Bot logged in as ${this.client.user.tag}`);
                resolve();
            });

            this.client.login(TEST_CONFIG.BOT_TOKEN).catch(reject);
        });
    }

    /**
     * Setup test entities (guild, user, channel)
     */
    async setupTestEntities() {
        try {
            // Get test guild
            this.testGuild = await this.client.guilds.fetch(TEST_CONFIG.GUILD_ID);
            console.log(`✅ Test guild: ${this.testGuild.name}`);

            // Get test user (bot owner)
            this.testUser = await this.client.users.fetch(TEST_CONFIG.USER_ID);
            console.log(`✅ Test user: ${this.testUser.username}`);

            // Get a test channel
            this.testChannel = this.testGuild.channels.cache.find(c => c.type === 0);
            if (this.testChannel) {
                console.log(`✅ Test channel: ${this.testChannel.name}`);
            } else {
                console.log('⚠️  No text channel found in test guild');
            }

            // Log database info
            const dbName = this.mongoClient.db('test').databaseName;
            console.log(`✅ Database: Connected to ${dbName}`);

        } catch (error) {
            throw new Error(`Failed to setup test entities: ${error.message}`);
        }
    }

    /**
     * Create a realistic mock interaction object
     * @param {number} type - Interaction type (3 = Button, 5 = StringSelect, 6 = Modal)
     * @param {string} customId - Custom ID for the interaction
     * @param {Array|Object} values - Values for select menus or modal fields
     * @returns {Object} Mock interaction object
     */
    createMockInteraction(type, customId, values = null) {
        const interaction = {
            id: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: type,
            customId: customId,
            user: this.testUser,
            member: null, // Can be set by specific tests if needed
            guild: this.testGuild,
            channel: this.testChannel,
            client: this.client,
            token: `test-token-${Date.now()}`,
            version: 1,
            applicationId: this.client.user.id,
            replied: false,
            deferred: false,
            ephemeral: null,
            webhook: null,
            
            // Response tracking for test validation
            _responses: [],
            _statusMessages: [],

            // Mock interaction methods with validation
            reply: async function(options) {
                console.log(`📤 REPLY: ${customId}`);
                console.log(`   Content: ${options.content || 'None'}`);
                console.log(`   Flags: ${options.flags || 'None'}`);
                console.log(`   Components: ${options.components?.length || 0}`);
                console.log(`   Ephemeral: ${options.ephemeral || false}`);
                
                // Validate Components v2 constraints
                if (options.flags && (options.flags & MessageFlags.IsComponentsV2)) {
                    if (options.content) {
                        throw new Error('❌ CRITICAL: Cannot use content field with MessageFlags.IsComponentsV2');
                    }
                    console.log(`   ✅ Components v2 validation passed`);
                }
                
                this.replied = true;
                this._responses.push({ type: 'reply', options, timestamp: Date.now() });
                return { id: `reply-${Date.now()}` };
            },

            followUp: async function(options) {
                console.log(`📤 FOLLOW-UP: ${customId}`);
                console.log(`   Content: ${options.content || 'None'}`);
                console.log(`   Ephemeral: ${options.ephemeral || false}`);
                
                // Validate Components v2 constraints
                if (options.flags && (options.flags & MessageFlags.IsComponentsV2)) {
                    if (options.content) {
                        throw new Error('❌ CRITICAL: Cannot use content field with MessageFlags.IsComponentsV2');
                    }
                    console.log(`   ✅ Components v2 validation passed`);
                }
                
                this._responses.push({ type: 'followUp', options, timestamp: Date.now() });
                return { id: `followup-${Date.now()}` };
            },

            update: async function(options) {
                console.log(`📤 UPDATE: ${customId}`);
                console.log(`   Components: ${options.components?.length || 0}`);
                
                this._responses.push({ type: 'update', options, timestamp: Date.now() });
                return { id: `update-${Date.now()}` };
            },

            editReply: async function(options) {
                console.log(`📤 EDIT-REPLY: ${customId}`);
                console.log(`   Components: ${options.components?.length || 0}`);
                
                this._responses.push({ type: 'editReply', options, timestamp: Date.now() });
                return { id: `editreply-${Date.now()}` };
            },

            deferUpdate: async function() {
                console.log(`📤 DEFER-UPDATE: ${customId}`);
                this.deferred = true;
                this._responses.push({ type: 'deferUpdate', timestamp: Date.now() });
                return { id: `defer-${Date.now()}` };
            },

            deferReply: async function(options = {}) {
                console.log(`📤 DEFER-REPLY: ${customId}`);
                console.log(`   Ephemeral: ${options.ephemeral || false}`);
                this.deferred = true;
                this._responses.push({ type: 'deferReply', options, timestamp: Date.now() });
                return { id: `defer-reply-${Date.now()}` };
            }
        };

        // Add values for select menus
        if (values && Array.isArray(values)) {
            interaction.values = values;
        }

        // Add fields for modal submissions
        if (values && typeof values === 'object' && !Array.isArray(values)) {
            interaction.fields = {
                getTextInputValue: (fieldId) => values[fieldId] || ''
            };
        }

        // Add interaction type helpers
        interaction.isButton = () => type === 3;
        interaction.isStringSelectMenu = () => type === 5;
        interaction.isModalSubmit = () => type === 6;
        interaction.isChatInputCommand = () => type === 2;
        interaction.isContextMenuCommand = () => false;
        interaction.isUserContextMenuCommand = () => false;
        interaction.isMessageContextMenuCommand = () => false;
        interaction.isAutocomplete = () => false;

        // Add modal methods for button interactions
        interaction.showModal = async function(modal) {
            console.log(`📤 SHOW-MODAL: ${customId}`);
            console.log(`   Modal title: ${modal.data?.title || 'Unknown'}`);
            this._responses.push({ type: 'showModal', modal, timestamp: Date.now() });
            return { id: `modal-${Date.now()}` };
        };

        return interaction;
    }

    /**
     * Test database connectivity and basic operations
     */
    async testDatabaseConnection() {
        console.log('\n🧪 Testing database connection...');
        
        try {
            // Test basic connectivity
            await this.mongoClient.db('test').admin().ping();
            
            // Test collections access
            const collections = await this.mongoClient.db('test').listCollections().toArray();
            console.log(`✅ Database accessible with ${collections.length} collections`);
            
            // Test a basic query
            const guildsCol = this.mongoClient.db('test').collection('guilds');
            const guildCount = await guildsCol.countDocuments();
            console.log(`✅ Found ${guildCount} guild documents`);
            
            return true;
        } catch (error) {
            console.log(`❌ Database test failed: ${error.message}`);
            return false;
        }
    }

    /**
     * Record a test result
     * @param {string} testName - Name of the test
     * @param {boolean} passed - Whether the test passed
     * @param {string} details - Additional details about the test
     */
    recordTestResult(testName, passed, details = '') {
        this.testResults.push({
            name: testName,
            passed,
            details,
            timestamp: Date.now()
        });
    }

    /**
     * Run a test with error handling and result recording
     * @param {string} testName - Name of the test
     * @param {Function} testFunction - Test function to execute
     * @returns {boolean} Whether the test passed
     */
    async runTest(testName, testFunction) {
        console.log(`\n🧪 Running test: ${testName}`);
        
        try {
            const result = await testFunction();
            const passed = result !== false;
            this.recordTestResult(testName, passed);
            
            if (passed) {
                console.log(`✅ ${testName} - PASSED`);
            } else {
                console.log(`❌ ${testName} - FAILED`);
            }
            
            return passed;
        } catch (error) {
            console.log(`❌ ${testName} - ERROR: ${error.message}`);
            this.recordTestResult(testName, false, error.message);
            return false;
        }
    }

    /**
     * Print comprehensive test results
     */
    printResults() {
        const duration = Date.now() - this.startTime;
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;

        console.log('\n📊 Test Results Summary:');
        console.log('=' .repeat(80));
        console.log(`Test Suite: ${this.testName}`);
        console.log(`Duration: ${(duration / 1000).toFixed(2)}s`);
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`);
        console.log('=' .repeat(80));

        // Print individual test results
        this.testResults.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            const details = result.details ? ` - ${result.details}` : '';
            console.log(`${status} ${result.name}${details}`);
        });

        console.log('=' .repeat(80));

        if (passedTests === totalTests && totalTests > 0) {
            console.log('🎯 ALL TESTS PASSED! System is ready for production.');
        } else if (totalTests === 0) {
            console.log('⚠️  No tests were run.');
        } else {
            console.log(`⚠️  ${failedTests} test(s) failed. Review and fix issues before deployment.`);
        }
    }

    /**
     * Clean up resources
     */
    async cleanup() {
        console.log(`\n🧹 Cleaning up ${this.testName}...`);
        
        if (this.client) {
            await this.client.destroy();
            console.log('🔌 Discord client disconnected');
        }

        // MongoDB connection is shared, so we don't close it here
        console.log('✅ Cleanup complete');
    }

    /**
     * Validate that the test environment is properly configured
     */
    validateEnvironment() {
        const required = ['BOT_TOKEN', 'MONGO_URI'];
        const missing = required.filter(key => !TEST_CONFIG[key]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
        }

        console.log('✅ Environment validation passed');
    }
}

/**
 * Utility functions for common test operations
 */
const TestUtils = {
    /**
     * Wait for a specified amount of time
     * @param {number} ms - Milliseconds to wait
     */
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * Generate a random test ID
     * @returns {string} Random test ID
     */
    generateTestId() {
        return `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    },

    /**
     * Validate Components v2 response
     * @param {Object} response - Response object to validate
     * @returns {boolean} Whether the response is valid
     */
    validateComponentsV2Response(response) {
        if (response.flags && (response.flags & MessageFlags.IsComponentsV2)) {
            if (response.content) {
                throw new Error('Components v2 responses cannot have content field');
            }
        }
        return true;
    },

    /**
     * Create a timeout promise for testing
     * @param {number} ms - Timeout in milliseconds
     * @returns {Promise} Promise that rejects after timeout
     */
    createTimeout(ms) {
        return new Promise((_, reject) => {
            setTimeout(() => reject(new Error(`Test timeout after ${ms}ms`)), ms);
        });
    }
};

module.exports = {
    BotTestBase,
    TestUtils,
    TEST_CONFIG
};
