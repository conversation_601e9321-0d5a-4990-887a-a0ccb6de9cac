const { ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

function buildSelectMenu(show17 = false, userId = null, currentSelection = null) {
    const options = [];
    // Only add owner option if user is the owner and it's not currently selected
    if (userId === process.env.OWNER && currentSelection !== '1') {
        options.push({ label: 'owner', description: 'not for your eye', value: '1' });
    }
    // Add opener option if it's not currently selected
    if (currentSelection !== 'opener') {
        options.push({ label: 'opener', description: 'keep em open', value: '2' });
    }
    // Add logs option if not currently selected
    if (currentSelection !== 'logs') {
        options.push({ label: 'logs', description: '17 sees all', value: 'logs' });
    }
    // Add sticky option if not currently selected
    if (currentSelection !== 'sticky') {
        options.push({ label: 'sticky', description: 'sticky stuff', value: 'sticky' });
    }
    // Add dehoist option if not currently selected
    if (currentSelection !== 'dehoist') {
        options.push({ label: 'dehoist', description: 'get the !@#$ outta here', value: 'dehoist' });
    }
    // Add exp option if not currently selected
    if (currentSelection !== 'exp') {
        options.push({ label: 'exp', description: 'level up level up', value: 'exp' });
    }
    // Add items option if not currently selected
    if (currentSelection !== 'items') {
        options.push({
            label: 'items', description: 'what\'re those?', value: 'items' });
    }
    if (show17) {
        options.push({ label: '17', description: 'back to main', value: '17' });
    }
    const selectMenu = new StringSelectMenuBuilder({
        custom_id: '17-select',
        placeholder: 'features',
        options: options,
    });
    return new ActionRowBuilder().addComponents(selectMenu);
}

module.exports = { buildSelectMenu }; 