/**
 * Graceful Role Assignment Error Handler
 * Provides consistent error handling for Discord role assignment operations
 * with proper logging and user feedback for permission-related failures
 */

const { DiscordAPIError } = require('discord.js');

/**
 * Safely assign roles to a member with graceful error handling
 * @param {GuildMember} member - Discord guild member
 * @param {string|Array<string>} roleIds - Role ID(s) to assign
 * @param {Object} context - Context information for logging
 * @param {string} context.operation - Operation name (e.g., 'sticky_restore', 'level_up')
 * @param {string} context.source - Source of the operation (e.g., 'guildMemberAdd', 'messageCreate')
 * @param {Function} [context.statusCallback] - Optional callback for user status messages
 * @returns {Promise<Object>} Result object with success status and details
 */
async function safeAssignRoles(member, roleIds, context = {}) {
    const {
        operation = 'role_assignment',
        source = 'unknown',
        statusCallback = null
    } = context;

    // Normalize roleIds to array
    const rolesArray = Array.isArray(roleIds) ? roleIds : [roleIds];
    
    if (rolesArray.length === 0) {
        return { success: true, assigned: [], failed: [], reason: 'no_roles' };
    }

    const results = {
        success: false,
        assigned: [],
        failed: [],
        errors: [],
        reason: null
    };

    try {
        // Get bot's highest role for hierarchy checking
        const botMember = member.guild.members.me;
        const botRole = botMember?.roles?.highest;
        
        if (!botRole) {
            console.warn(`[roleAssignmentHandler] ⚠️ Bot has no roles in guild ${member.guild.id} - cannot assign roles`);
            results.reason = 'bot_no_roles';
            return results;
        }

        // Pre-validate roles for hierarchy issues
        const validRoles = [];
        const hierarchyFailures = [];

        for (const roleId of rolesArray) {
            const role = member.guild.roles.cache.get(roleId);
            
            if (!role) {
                hierarchyFailures.push({
                    roleId,
                    reason: 'role_not_found',
                    error: `Role ${roleId} not found in guild`
                });
                continue;
            }

            if (role.position >= botRole.position) {
                hierarchyFailures.push({
                    roleId,
                    roleName: role.name,
                    reason: 'hierarchy_error',
                    error: `Role @${role.name} (pos: ${role.position}) is higher than bot role @${botRole.name} (pos: ${botRole.position})`
                });
                continue;
            }

            if (!role.editable) {
                hierarchyFailures.push({
                    roleId,
                    roleName: role.name,
                    reason: 'not_editable',
                    error: `Role @${role.name} is not editable by bot`
                });
                continue;
            }

            validRoles.push(roleId);
        }

        // Log hierarchy failures with context
        if (hierarchyFailures.length > 0) {
            console.warn(`[roleAssignmentHandler] ⚠️ ${operation} hierarchy failures for ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}:`);
            hierarchyFailures.forEach(failure => {
                console.warn(`[roleAssignmentHandler]   - ${failure.error}`);
            });

            results.failed = hierarchyFailures;
            
            // Provide user feedback if callback provided
            if (statusCallback && typeof statusCallback === 'function') {
                const failedRoleNames = hierarchyFailures
                    .filter(f => f.roleName)
                    .map(f => `@${f.roleName}`)
                    .join(', ');
                
                if (failedRoleNames) {
                    statusCallback(`Some roles could not be assigned due to hierarchy restrictions: ${failedRoleNames}`);
                }
            }
        }

        // Attempt to assign valid roles
        if (validRoles.length > 0) {
            try {
                await member.roles.add(validRoles);
                
                results.assigned = validRoles;
                results.success = true;
                
                console.log(`[roleAssignmentHandler] ✅ ${operation} success: assigned ${validRoles.length} roles to ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}`);
                
                // Log assigned role names for debugging
                const assignedRoleNames = validRoles
                    .map(roleId => member.guild.roles.cache.get(roleId)?.name)
                    .filter(name => name)
                    .join(', ');
                
                if (assignedRoleNames) {
                    console.log(`[roleAssignmentHandler]   - Assigned roles: ${assignedRoleNames}`);
                }

            } catch (error) {
                // Handle Discord API errors gracefully
                if (error instanceof DiscordAPIError) {
                    if (error.code === 50013) {
                        // Missing Permissions
                        console.error(`[roleAssignmentHandler] ❌ ${operation} permission error for ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}:`);
                        console.error(`[roleAssignmentHandler]   - Error: ${error.message}`);
                        console.error(`[roleAssignmentHandler]   - Attempted roles: ${validRoles.join(', ')}`);
                        
                        results.reason = 'permission_error';
                        results.errors.push({
                            code: error.code,
                            message: error.message,
                            roles: validRoles
                        });

                        // Provide user feedback
                        if (statusCallback && typeof statusCallback === 'function') {
                            statusCallback('Some roles could not be assigned due to permission restrictions');
                        }

                    } else if (error.code === 10011) {
                        // Unknown Role
                        console.error(`[roleAssignmentHandler] ❌ ${operation} unknown role error for ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}:`);
                        console.error(`[roleAssignmentHandler]   - Error: ${error.message}`);
                        
                        results.reason = 'unknown_role';
                        results.errors.push({
                            code: error.code,
                            message: error.message,
                            roles: validRoles
                        });

                    } else {
                        // Other Discord API errors
                        console.error(`[roleAssignmentHandler] ❌ ${operation} Discord API error for ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}:`);
                        console.error(`[roleAssignmentHandler]   - Code: ${error.code}, Message: ${error.message}`);
                        
                        results.reason = 'api_error';
                        results.errors.push({
                            code: error.code,
                            message: error.message,
                            roles: validRoles
                        });
                    }
                } else {
                    // Non-Discord API errors
                    console.error(`[roleAssignmentHandler] ❌ ${operation} unexpected error for ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}:`, error);
                    
                    results.reason = 'unexpected_error';
                    results.errors.push({
                        message: error.message,
                        stack: error.stack,
                        roles: validRoles
                    });
                }

                results.failed = results.failed.concat(validRoles.map(roleId => ({
                    roleId,
                    roleName: member.guild.roles.cache.get(roleId)?.name,
                    reason: results.reason,
                    error: error.message
                })));
            }
        }

        // Set overall success if any roles were assigned
        if (results.assigned.length > 0) {
            results.success = true;
        }

        return results;

    } catch (error) {
        console.error(`[roleAssignmentHandler] ❌ ${operation} critical error for ${member.user.tag} (${member.user.id}) in guild ${member.guild.id}:`, error);
        
        results.reason = 'critical_error';
        results.errors.push({
            message: error.message,
            stack: error.stack
        });
        
        return results;
    }
}

/**
 * Safely edit member data (roles and nickname) with graceful error handling
 * @param {GuildMember} member - Discord guild member
 * @param {Object} editData - Data to edit (roles, nick)
 * @param {Object} context - Context information for logging
 * @returns {Promise<Object>} Result object with success status and details
 */
async function safeEditMember(member, editData, context = {}) {
    const {
        operation = 'member_edit',
        source = 'unknown',
        statusCallback = null
    } = context;

    const results = {
        success: false,
        rolesResult: null,
        nicknameResult: null,
        reason: null
    };

    try {
        // Handle roles separately using safeAssignRoles if present
        if (editData.roles && editData.roles.length > 0) {
            results.rolesResult = await safeAssignRoles(member, editData.roles, {
                ...context,
                operation: `${operation}_roles`
            });
        }

        // Handle nickname separately
        if (editData.nick !== undefined) {
            try {
                await member.setNickname(editData.nick);
                results.nicknameResult = { success: true, nickname: editData.nick };
                console.log(`[roleAssignmentHandler] ✅ ${operation} nickname success: set nickname for ${member.user.tag} (${member.user.id}) to "${editData.nick}"`);
            } catch (error) {
                if ((error instanceof DiscordAPIError || error.code === 50013) && error.code === 50013) {
                    console.error(`[roleAssignmentHandler] ❌ ${operation} nickname permission error for ${member.user.tag} (${member.user.id}): ${error.message}`);
                    results.nicknameResult = { success: false, reason: 'permission_error', error: error.message };

                    if (statusCallback && typeof statusCallback === 'function') {
                        statusCallback('Nickname could not be set due to permission restrictions');
                    }
                } else {
                    console.error(`[roleAssignmentHandler] ❌ ${operation} nickname error for ${member.user.tag} (${member.user.id}):`, error);
                    results.nicknameResult = { success: false, reason: 'unexpected_error', error: error.message };
                }
            }
        }

        // Determine overall success
        const rolesSuccess = !results.rolesResult || results.rolesResult.success;
        const nicknameSuccess = !results.nicknameResult || results.nicknameResult.success;
        results.success = rolesSuccess && nicknameSuccess;

        return results;

    } catch (error) {
        console.error(`[roleAssignmentHandler] ❌ ${operation} critical error for ${member.user.tag} (${member.user.id}):`, error);
        results.reason = 'critical_error';
        return results;
    }
}

module.exports = {
    safeAssignRoles,
    safeEditMember
};
