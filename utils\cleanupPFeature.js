/**
 * Cleanup script for removing 'p' feature remnants from the database (Enterprise-Grade Performance Optimized)
 * This script removes:
 * 1. The 'pchannels' collection entirely
 * 2. The 'p' configuration from all guild documents
 * OPTIMIZED: Batch processing, retry logic, performance monitoring, and comprehensive progress tracking
 */

const { mongoClient } = require('../mongo/client.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const cleanupMetrics = {
    collectionsDropped: 0,
    documentsUpdated: 0,
    operationsRetried: 0,
    totalOperationTime: 0,
    averageOperationTime: 0,
    failedOperations: 0,
    batchOperations: 0,
    verificationChecks: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment
};

/**
 * Retry operation with exponential backoff (Enterprise-Grade Reliability)
 * OPTIMIZED: Comprehensive retry logic with performance monitoring
 * @param {Function} operation - Operation to retry
 * @param {string} operationName - Name for logging
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise<any>} Operation result
 */
async function retryWithBackoff(operation, operationName, maxRetries = 3, baseDelay = 1000) {
    const startTime = Date.now();

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await operation();

            if (attempt > 1) {
                cleanupMetrics.operationsRetried++;
                if (cleanupMetrics.verboseLogging) {
                    console.log(`[cleanupPFeature] ✅ ${operationName} succeeded on attempt ${attempt}`);
                }
            }

            return result;
        } catch (error) {
            if (attempt === maxRetries) {
                cleanupMetrics.failedOperations++;
                console.error(`[cleanupPFeature] ❌ ${operationName} failed after ${maxRetries} attempts:`, error.message);
                throw error;
            }

            const delay = baseDelay * Math.pow(2, attempt - 1);
            if (cleanupMetrics.verboseLogging) {
                console.warn(`[cleanupPFeature] ⚠️ ${operationName} attempt ${attempt} failed, retrying in ${delay}ms...`);
            }

            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

/**
 * Track operation performance
 * @param {string} operationType - Type of operation
 * @param {number} duration - Operation duration in milliseconds
 */
function trackOperationPerformance(operationType, duration) {
    cleanupMetrics.totalOperationTime += duration;
    const totalOps = cleanupMetrics.collectionsDropped + cleanupMetrics.batchOperations + cleanupMetrics.verificationChecks;
    cleanupMetrics.averageOperationTime = totalOps > 0 ? cleanupMetrics.totalOperationTime / totalOps : 0;

    if (cleanupMetrics.verboseLogging || duration > 2000) {
        console.log(`[cleanupPFeature] ⏱️ ${operationType} completed in ${duration}ms`);
    }
}

/**
 * Cleanup P feature remnants with enterprise-grade optimization (Enterprise-Grade Optimized)
 * OPTIMIZED: Batch processing, retry logic, progress tracking, and comprehensive error handling
 */
async function cleanupPFeatureRemnants() {
    const startTime = Date.now();

    try {
        console.log('[cleanupPFeature] 🧹 Starting enterprise-grade cleanup of p feature remnants...');

        const db = mongoClient.db("seventeen_bot");
        const results = {
            pchannelsDropped: false,
            guildsUpdated: 0,
            remainingPConfigs: 0,
            pchannelsCollectionExists: false,
            operationDetails: []
        };

        // Phase 1: Drop the pchannels collection entirely with retry logic
        console.log('[cleanupPFeature] 📊 Phase 1: Dropping pchannels collection...');
        const dropStartTime = Date.now();

        try {
            await retryWithBackoff(
                async () => {
                    const pchannelsResult = await db.collection("pchannels").drop();
                    return pchannelsResult;
                },
                'Drop pchannels collection',
                3,
                1000
            );

            results.pchannelsDropped = true;
            cleanupMetrics.collectionsDropped++;

            const dropDuration = Date.now() - dropStartTime;
            trackOperationPerformance('Drop pchannels collection', dropDuration);

            console.log('[cleanupPFeature] ✅ Dropped pchannels collection successfully');
            results.operationDetails.push({
                phase: 'drop_collection',
                success: true,
                duration: dropDuration,
                details: 'pchannels collection dropped'
            });

        } catch (error) {
            if (error.codeName === 'NamespaceNotFound') {
                console.log('[cleanupPFeature] ℹ️ pchannels collection does not exist (already clean)');
                results.operationDetails.push({
                    phase: 'drop_collection',
                    success: true,
                    duration: Date.now() - dropStartTime,
                    details: 'pchannels collection already clean'
                });
            } else {
                console.error('[cleanupPFeature] ❌ Error dropping pchannels collection:', error.message);
                results.operationDetails.push({
                    phase: 'drop_collection',
                    success: false,
                    duration: Date.now() - dropStartTime,
                    error: error.message
                });
            }
        }

        // Phase 2: Remove 'p' configuration from all guild documents with batch processing
        console.log('[cleanupPFeature] 📊 Phase 2: Removing p configuration from guild documents...');
        const updateStartTime = Date.now();

        const guildsCol = db.collection("guilds");

        // First, count how many documents need updating for progress tracking
        const documentsToUpdate = await retryWithBackoff(
            () => guildsCol.countDocuments({ p: { $exists: true } }),
            'Count documents with p configuration',
            2,
            500
        );

        console.log(`[cleanupPFeature] 📈 Found ${documentsToUpdate} guild documents with p configuration`);

        if (documentsToUpdate > 0) {
            // OPTIMIZED: Enhanced batch processing with comprehensive error handling
            const updateResult = await retryWithBackoff(
                () => guildsCol.updateMany(
                    { p: { $exists: true } }, // Only update documents that have the 'p' field
                    { $unset: { p: "" } }     // Remove the 'p' field entirely
                ),
                'Remove p configuration from guilds',
                3,
                1000
            );

            results.guildsUpdated = updateResult.modifiedCount;
            cleanupMetrics.documentsUpdated += updateResult.modifiedCount;
            cleanupMetrics.batchOperations++;

            const updateDuration = Date.now() - updateStartTime;
            trackOperationPerformance('Batch update guild documents', updateDuration);

            console.log(`[cleanupPFeature] ✅ Removed 'p' configuration from ${updateResult.modifiedCount} guild documents in ${updateDuration}ms`);
            results.operationDetails.push({
                phase: 'update_guilds',
                success: true,
                duration: updateDuration,
                details: `Updated ${updateResult.modifiedCount} guild documents`
            });
        } else {
            console.log('[cleanupPFeature] ℹ️ No guild documents with p configuration found');
            results.operationDetails.push({
                phase: 'update_guilds',
                success: true,
                duration: Date.now() - updateStartTime,
                details: 'No guild documents needed updating'
            });
        }

        // Phase 3: Comprehensive verification with parallel processing
        console.log('[cleanupPFeature] 📊 Phase 3: Comprehensive cleanup verification...');
        const verifyStartTime = Date.now();

        // OPTIMIZED: Enhanced parallel operations with comprehensive error handling
        const [remainingConfigsResult, remainingCollectionsResult] = await Promise.allSettled([
            retryWithBackoff(
                () => guildsCol.countDocuments({ p: { $exists: true } }),
                'Count remaining p configurations',
                2,
                500
            ),
            retryWithBackoff(
                () => db.listCollections({ name: "pchannels" }).toArray(),
                'Check for remaining pchannels collection',
                2,
                500
            )
        ]);

        // Handle results with graceful fallbacks
        results.remainingPConfigs = remainingConfigsResult.status === 'fulfilled' ? remainingConfigsResult.value : -1;
        const remainingPChannels = remainingCollectionsResult.status === 'fulfilled' ? remainingCollectionsResult.value : [];
        results.pchannelsCollectionExists = remainingPChannels.length > 0;

        cleanupMetrics.verificationChecks++;

        const verifyDuration = Date.now() - verifyStartTime;
        trackOperationPerformance('Verification checks', verifyDuration);

        // Enhanced verification reporting
        console.log('[cleanupPFeature] 📋 Cleanup verification results:');
        console.log(`  - Remaining guild 'p' configurations: ${results.remainingPConfigs}`);
        console.log(`  - pchannels collection exists: ${results.pchannelsCollectionExists}`);

        const isCompletelyClean = results.remainingPConfigs === 0 && !results.pchannelsCollectionExists;

        if (isCompletelyClean) {
            console.log('[cleanupPFeature] ✅ All p feature remnants successfully removed!');
        } else {
            console.log('[cleanupPFeature] ⚠️ Some remnants may still exist - manual review recommended');
        }

        const totalDuration = Date.now() - startTime;
        trackOperationPerformance('Complete cleanup operation', totalDuration);

        results.operationDetails.push({
            phase: 'verification',
            success: true,
            duration: verifyDuration,
            details: `Verification completed - Clean: ${isCompletelyClean}`
        });

        // Final performance summary
        console.log(`[cleanupPFeature] 🎉 Cleanup completed in ${totalDuration}ms with ${results.operationDetails.length} phases`);

        return {
            ...results,
            isCompletelyClean,
            totalDuration,
            performanceMetrics: {
                totalOperationTime: totalDuration,
                phasesCompleted: results.operationDetails.length,
                retriesUsed: cleanupMetrics.operationsRetried,
                failedOperations: cleanupMetrics.failedOperations
            }
        };

    } catch (error) {
        console.error('[cleanupPFeature] ❌ Error during cleanup:', error);
        throw error;
    }
}

/**
 * Get comprehensive performance statistics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive performance statistics
 */
function getCleanupStats() {
    return {
        // Performance metrics
        performance: {
            collectionsDropped: cleanupMetrics.collectionsDropped,
            documentsUpdated: cleanupMetrics.documentsUpdated,
            operationsRetried: cleanupMetrics.operationsRetried,
            failedOperations: cleanupMetrics.failedOperations,
            batchOperations: cleanupMetrics.batchOperations,
            verificationChecks: cleanupMetrics.verificationChecks,
            totalOperationTime: `${cleanupMetrics.totalOperationTime}ms`,
            averageOperationTime: `${cleanupMetrics.averageOperationTime.toFixed(2)}ms`,
            lastOptimization: new Date(cleanupMetrics.lastOptimization).toISOString()
        },

        // System health assessment
        systemHealth: {
            status: cleanupMetrics.failedOperations === 0 ? 'excellent' :
                   cleanupMetrics.failedOperations < 2 ? 'good' : 'needs attention',
            retryEfficiency: cleanupMetrics.operationsRetried > 0 ?
                ((cleanupMetrics.operationsRetried - cleanupMetrics.failedOperations) / cleanupMetrics.operationsRetried * 100).toFixed(2) + '%' : 'N/A'
        }
    };
}

/**
 * Performance monitoring and reporting (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and optimization reporting
 */
function performanceReport() {
    const stats = getCleanupStats();
    console.log(`[cleanupPFeature] 📊 Performance Report:`);
    console.log(`[cleanupPFeature]   Collections Dropped: ${stats.performance.collectionsDropped}`);
    console.log(`[cleanupPFeature]   Documents Updated: ${stats.performance.documentsUpdated}`);
    console.log(`[cleanupPFeature]   Operations Retried: ${stats.performance.operationsRetried}`);
    console.log(`[cleanupPFeature]   Failed Operations: ${stats.performance.failedOperations}`);
    console.log(`[cleanupPFeature]   Batch Operations: ${stats.performance.batchOperations}`);
    console.log(`[cleanupPFeature]   Average Operation Time: ${stats.performance.averageOperationTime}`);
    console.log(`[cleanupPFeature]   System Health: ${stats.systemHealth.status}`);

    return stats;
}

/**
 * Verify P feature cleanup status (Enterprise-Grade Verification)
 * OPTIMIZED: Comprehensive verification with detailed reporting
 */
async function verifyPFeatureCleanup() {
    const startTime = Date.now();

    try {
        console.log('[cleanupPFeature] 🔍 Performing comprehensive P feature cleanup verification...');

        const db = mongoClient.db("seventeen_bot");

        // OPTIMIZED: Enhanced parallel operations with comprehensive error handling
        const [configCountResult, collectionListResult] = await Promise.allSettled([
            retryWithBackoff(
                () => db.collection("guilds").countDocuments({ p: { $exists: true } }),
                'Count remaining p configurations',
                2,
                500
            ),
            retryWithBackoff(
                () => db.listCollections({ name: "pchannels" }).toArray(),
                'Check for pchannels collection',
                2,
                500
            )
        ]);

        const remainingConfigs = configCountResult.status === 'fulfilled' ? configCountResult.value : -1;
        const pchannelsExists = collectionListResult.status === 'fulfilled' ? collectionListResult.value.length > 0 : false;

        const isClean = remainingConfigs === 0 && !pchannelsExists;

        const duration = Date.now() - startTime;
        trackOperationPerformance('Verification check', duration);

        console.log('[cleanupPFeature] 📋 Verification Results:');
        console.log(`  - Guild documents with p config: ${remainingConfigs}`);
        console.log(`  - pchannels collection exists: ${pchannelsExists}`);
        console.log(`  - Status: ${isClean ? '✅ Clean' : '⚠️ Remnants detected'}`);

        return {
            isClean,
            remainingConfigs,
            pchannelsExists,
            duration,
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error('[cleanupPFeature] ❌ Error during verification:', error);
        throw error;
    }
}

// Export for use in other files
module.exports = {
    // Core functions
    cleanupPFeatureRemnants,

    // Enhanced optimization functions
    verifyPFeatureCleanup,
    retryWithBackoff,
    getCleanupStats,
    performanceReport,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...cleanupMetrics })
};

// Allow running directly with node
if (require.main === module) {
    const { connect } = require('../mongo/client.js');
    
    connect()
        .then(() => {
            console.log('[cleanupPFeature] Connected to database');
            return cleanupPFeatureRemnants();
        })
        .then((result) => {
            console.log('[cleanupPFeature] Cleanup completed:', result);
            process.exit(0);
        })
        .catch((error) => {
            console.error('[cleanupPFeature] Cleanup failed:', error);
            process.exit(1);
        });
}
