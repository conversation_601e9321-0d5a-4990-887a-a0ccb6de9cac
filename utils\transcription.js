/**
 * Voice Message Transcription System for /you Command
 * Integrates with existing Whisper API for voice message transcription
 * Features weekly limits, Monday resets, and comprehensive tracking
 */

const { optimizedFindOne, optimizedUpdateOne, optimizedFind } = require('./database-optimizer.js');
const { transcribeVoiceMessage } = require('../whisper-integration.js');
const { ContainerBuilder, TextDisplayBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, ButtonBuilder, ButtonStyle, ActionRowBuilder, MessageFlags, SeparatorBuilder, SeparatorSpacingSize } = require('discord.js');
const { OPERATION_COLORS, LOG_COLORS } = require('./colors.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Transcription limits and configuration
const TRANSCRIPTION_CONFIG = {
    WEEKLY_LIMIT: 5,                    // 5 transcriptions per week for regular users
    OWNER_UNLIMITED: true,              // Bot owner has unlimited transcriptions
    RESET_DAY: 1,                       // Monday (0 = Sunday, 1 = Monday, etc.)
    MAX_VOICE_MESSAGES: 10,             // Maximum voice messages to search through
    CACHE_TTL: 10 * 60 * 1000,         // 10 minutes cache TTL
    MODELS: {
        OWNER: 'base',                  // Fast and accurate model for bot owner
        USER: 'small'                    // Fast and accurate model for regular users
    }
};

// Performance monitoring
const transcriptionMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    transcriptionsProcessed: 0,
    averageProcessingTime: 0,
    weeklyResets: 0,
    voiceMessagesScanned: 0,
    verboseLogging: isDevelopment
};

// LRU Caches for performance optimization
const transcriptionDataCache = CacheFactory.createUserCache(); // User transcription data
const voiceMessageCache = CacheFactory.createHighFrequencyCache(); // Voice message search results
const transcriptionStatsCache = CacheFactory.createComputationCache(); // Transcription statistics

// Register caches for global cleanup
registerCache(transcriptionDataCache);
registerCache(voiceMessageCache);
registerCache(transcriptionStatsCache);

/**
 * Normalize duration to match Discord's rounding behavior (always round up)
 * @param {number} duration - Duration in seconds
 * @returns {number} Normalized duration in seconds
 */
function normalizeDuration(duration) {
    return Math.ceil(duration);
}

/**
 * Get next Monday reset timestamp
 * @returns {number} Timestamp of next Monday at midnight UTC
 */
function getNextMondayReset() {
    const now = new Date();
    const nextMonday = new Date(now);
    
    // Calculate days until next Monday
    const daysUntilMonday = (8 - now.getUTCDay()) % 7 || 7;
    nextMonday.setUTCDate(now.getUTCDate() + daysUntilMonday);
    
    // Set to midnight UTC
    nextMonday.setUTCHours(0, 0, 0, 0);
    
    return Math.floor(nextMonday.getTime() / 1000);
}

/**
 * Get user's transcription data with caching
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User transcription data
 */
async function getUserTranscriptionData(userId) {
    const startTime = Date.now();
    
    // Check cache first
    const cached = transcriptionDataCache.get(userId);
    if (cached) {
        transcriptionMetrics.cacheHits++;
        return cached;
    }
    
    transcriptionMetrics.cacheMisses++;
    
    try {
        const userData = await optimizedFindOne('user_transcriptions', { userId });
        
        // Initialize data if user doesn't exist
        if (!userData) {
            const newUserData = {
                userId,
                transcriptionsUsed: 0,
                weeklyResetTime: getNextMondayReset(),
                totalWordsTranscribed: 0,
                totalMinutesTranscribed: 0,
                transcriptionHistory: [],
                createdAt: new Date()
            };
            
            await optimizedUpdateOne('user_transcriptions', 
                { userId }, 
                { $set: newUserData }, 
                { upsert: true }
            );
            
            // Cache the new data
            transcriptionDataCache.set(userId, newUserData);
            return newUserData;
        }
        
        // Check if weekly reset is needed
        const now = Math.floor(Date.now() / 1000);
        if (now >= userData.weeklyResetTime) {
            userData.transcriptionsUsed = 0;
            userData.weeklyResetTime = getNextMondayReset();
            
            await optimizedUpdateOne('user_transcriptions', 
                { userId }, 
                { 
                    $set: { 
                        transcriptionsUsed: 0, 
                        weeklyResetTime: userData.weeklyResetTime 
                    } 
                }
            );
            
            transcriptionMetrics.weeklyResets++;
        }
        
        // Cache the data
        transcriptionDataCache.set(userId, userData);
        
        const duration = Date.now() - startTime;
        if (transcriptionMetrics.verboseLogging || duration > 100) {
            console.log(`[transcription] ✅ User data fetched for ${userId}: ${duration}ms`);
        }
        
        return userData;
        
    } catch (error) {
        console.error(`[transcription] ❌ Error getting user data for ${userId}:`, error);
        
        // Return default data on error
        return {
            userId,
            transcriptionsUsed: 0,
            weeklyResetTime: getNextMondayReset(),
            totalWordsTranscribed: 0,
            totalMinutesTranscribed: 0,
            transcriptionHistory: []
        };
    }
}

/**
 * Check if user can transcribe (has remaining quota)
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Quota check result
 */
async function checkTranscriptionQuota(userId) {
    // Bot owner has unlimited transcriptions
    if (userId === process.env.OWNER) {
        return {
            canTranscribe: true,
            remaining: 'unlimited',
            resetTime: null,
            isOwner: true
        };
    }
    
    const userData = await getUserTranscriptionData(userId);
    const remaining = Math.max(0, TRANSCRIPTION_CONFIG.WEEKLY_LIMIT - userData.transcriptionsUsed);
    
    return {
        canTranscribe: remaining > 0,
        remaining,
        resetTime: userData.weeklyResetTime,
        isOwner: false,
        used: userData.transcriptionsUsed,
        limit: TRANSCRIPTION_CONFIG.WEEKLY_LIMIT
    };
}

/**
 * Check if a specific voice message has been transcribed before
 * @param {string} userId - Discord user ID
 * @param {string} messageId - Discord message ID
 * @param {string} attachmentId - Discord attachment ID
 * @returns {Promise<boolean>} True if transcribed before
 */
async function checkVoiceMessageTranscribed(userId, messageId, attachmentId) {
    try {
        const userData = await getUserTranscriptionData(userId);

        if (!userData || !userData.transcriptionHistory) {
            return false;
        }

        // Check if this specific voice message has been transcribed
        return userData.transcriptionHistory.some(t =>
            t.messageId === messageId && t.attachmentId === attachmentId
        );
    } catch (error) {
        console.error(`[transcription] ❌ Error checking transcription status for ${userId}:`, error);
        return false;
    }
}

/**
 * Invalidate voice message cache for a user in a specific channel
 * @param {string} userId - Discord user ID
 * @param {string} channelId - Discord channel ID
 */
function invalidateVoiceMessageCache(userId, channelId) {
    const cacheKey = `${userId}-${channelId}`;
    voiceMessageCache.delete(cacheKey);
    console.log(`[transcription] 🗑️ Invalidated voice message cache for user ${userId} in channel ${channelId}`);
}

/**
 * Search for user's voice messages in a channel
 * @param {Object} interaction - Discord interaction
 * @param {number} limit - Maximum messages to search
 * @param {boolean} forceRefresh - Force refresh cache (skip cache lookup)
 * @returns {Promise<Array>} Array of voice message options
 */
async function searchUserVoiceMessages(interaction, limit = TRANSCRIPTION_CONFIG.MAX_VOICE_MESSAGES, forceRefresh = false) {
    const startTime = Date.now();
    const userId = interaction.user.id;
    const channelId = interaction.channel.id;
    const cacheKey = `${userId}-${channelId}`;

    // Check cache first (but skip cache in development for debugging or if force refresh)
    const isDevelopment = process.env.NODE_ENV === 'development';
    const cached = !isDevelopment && !forceRefresh ? voiceMessageCache.get(cacheKey) : null;
    if (cached) {
        transcriptionMetrics.cacheHits++;
        console.log(`[transcription] 💾 Using cached results: ${cached.length} voice messages`);
        return cached;
    }

    transcriptionMetrics.cacheMisses++;
    
    try {
        // Fetch recent messages from the channel
        const messages = await interaction.channel.messages.fetch({ limit });
        const voiceMessages = [];

        console.log(`[transcription] 🔍 Searching ${messages.size} messages for user ${userId}`);

        let userMessages = 0;
        let userAttachments = 0;

        // Filter for user's voice messages
        for (const [messageId, message] of messages) {
            if (message.author.id === userId && message.attachments.size > 0) {
                userMessages++;
                console.log(`[transcription] 👤 Found user message with ${message.attachments.size} attachments`);

                for (const [attachmentId, attachment] of message.attachments) {
                    userAttachments++;
                    console.log(`[transcription] 📎 Attachment: ${attachment.name}, type: ${attachment.contentType}, flags: ${attachment.flags}`);
                    console.log(`[transcription] 📎 Duration properties: duration_secs=${attachment.duration_secs}, duration=${attachment.duration}, waveform=${attachment.waveform ? 'present' : 'none'}`);

                    // Check if it's a voice message using Discord's voice message flag
                    // Handle both number flags and BitField objects
                    let hasVoiceFlag = false;
                    if (attachment.flags) {
                        if (typeof attachment.flags === 'number') {
                            hasVoiceFlag = (attachment.flags & MessageFlags.IsVoiceMessage) !== 0;
                        } else if (attachment.flags.has) {
                            // BitField object
                            hasVoiceFlag = attachment.flags.has(MessageFlags.IsVoiceMessage);
                        } else if (attachment.flags.bitfield !== undefined) {
                            // BitField with bitfield property
                            hasVoiceFlag = (attachment.flags.bitfield & MessageFlags.IsVoiceMessage) !== 0;
                        }
                    }

                    console.log(`[transcription] 🎤 Voice flag check: ${hasVoiceFlag} (flags type: ${typeof attachment.flags}, value: ${JSON.stringify(attachment.flags)})`);

                    if (hasVoiceFlag) {
                        // Try multiple duration properties
                        const duration = attachment.duration_secs || attachment.duration || 0;
                        const timestamp = Math.floor(message.createdTimestamp / 1000);

                        voiceMessages.push({
                            messageId,
                            attachmentId,
                            attachment,
                            duration,
                            timestamp,
                            createdAt: message.createdAt
                        });

                        console.log(`[transcription] ✅ Voice message detected via flag: ${messageId}:${attachmentId}, duration: ${duration}s`);
                    } else {
                        // Try alternative detection methods
                        const isOgg = attachment.name && attachment.name.endsWith('.ogg');
                        const hasAudio = attachment.contentType && attachment.contentType.includes('audio');
                        const hasDuration = attachment.duration_secs !== undefined;
                        const isVoiceMessageFile = attachment.name && attachment.name.startsWith('voice-message');

                        console.log(`[transcription] 🤔 Alternative checks - OGG: ${isOgg}, Audio: ${hasAudio}, Duration: ${hasDuration}, VoiceFile: ${isVoiceMessageFile}`);

                        // Use alternative detection: voice-message.ogg files with audio content type
                        if (isVoiceMessageFile && isOgg && hasAudio) {
                            // Try multiple duration properties
                            const duration = attachment.duration_secs || attachment.duration || 0;
                            const timestamp = Math.floor(message.createdTimestamp / 1000);

                            voiceMessages.push({
                                messageId,
                                attachmentId,
                                attachment,
                                duration,
                                timestamp,
                                createdAt: message.createdAt
                            });

                            console.log(`[transcription] ✅ Voice message detected via alternative method: ${messageId}:${attachmentId}, duration: ${duration}s`);
                        }
                    }
                }
            }
        }

        console.log(`[transcription] 📊 Summary: ${messages.size} total, ${userMessages} user messages, ${userAttachments} attachments, ${voiceMessages.length} voice messages`);

        // Log final results
        if (voiceMessages.length === 0 && userAttachments > 0) {
            console.log(`[transcription] ⚠️ No voice messages detected despite having ${userAttachments} attachments`);
        }
        
        // Sort by creation time (newest first)
        voiceMessages.sort((a, b) => b.timestamp - a.timestamp);

        // Limit to reasonable number for select menu
        const limitedMessages = voiceMessages.slice(0, 25); // Discord select menu limit

        // Cache the results (but not in development for debugging)
        if (!isDevelopment) {
            voiceMessageCache.set(cacheKey, limitedMessages);
        }
        
        const duration = Date.now() - startTime;
        transcriptionMetrics.voiceMessagesScanned += messages.size;
        
        if (transcriptionMetrics.verboseLogging || duration > 200) {
            console.log(`[transcription] ✅ Found ${limitedMessages.length} voice messages for ${userId} in ${duration}ms`);
        }
        
        return limitedMessages;
        
    } catch (error) {
        console.error(`[transcription] ❌ Error searching voice messages for ${userId}:`, error);
        return [];
    }
}

/**
 * Process voice message transcription
 * @param {Object} interaction - Discord interaction
 * @param {string} messageId - Voice message ID
 * @param {string} attachmentId - Attachment ID
 * @returns {Promise<Object>} Transcription result
 */
async function processTranscription(interaction, messageId, attachmentId) {
    const startTime = Date.now();
    const userId = interaction.user.id;
    const isOwner = userId === process.env.OWNER;

    try {
        // Check quota first
        const quota = await checkTranscriptionQuota(userId);
        if (!quota.canTranscribe) {
            return {
                success: false,
                error: 'QUOTA_EXCEEDED',
                message: `You've used all ${TRANSCRIPTION_CONFIG.WEEKLY_LIMIT} transcriptions this week. Resets <t:${quota.resetTime}:R>.`
            };
        }

        // Find the voice message
        const voiceMessages = await searchUserVoiceMessages(interaction, 100);
        const voiceMessage = voiceMessages.find(vm => vm.messageId === messageId && vm.attachmentId === attachmentId);

        if (!voiceMessage) {
            return {
                success: false,
                error: 'MESSAGE_NOT_FOUND',
                message: 'Voice message not found or no longer accessible.'
            };
        }

        // Select appropriate model based on user type
        const model = isOwner ? TRANSCRIPTION_CONFIG.MODELS.OWNER : TRANSCRIPTION_CONFIG.MODELS.USER;

        // Transcribe the voice message
        const transcriptionResult = await transcribeVoiceMessage(voiceMessage.attachment, messageId, {
            model,
            format: 'json',
            task: 'transcribe'
        });

        if (!transcriptionResult.success) {
            return {
                success: false,
                error: 'TRANSCRIPTION_FAILED',
                message: 'Failed to transcribe voice message. Please try again.'
            };
        }

        // Calculate statistics
        const text = transcriptionResult.text.trim();
        const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
        const duration = transcriptionResult.duration || voiceMessage.duration || 0;
        const processingTime = Date.now() - startTime;



        // Update user data (for both owners and non-owners to track stats)
        const userData = await getUserTranscriptionData(userId);

        // Update stats for everyone
        userData.totalWordsTranscribed += wordCount;
        userData.totalMinutesTranscribed += duration / 60;

        // Only update quota for non-owners
        if (!isOwner) {
            userData.transcriptionsUsed++;
        }

        // Add to history (keep last 10)
        userData.transcriptionHistory.unshift({
            messageId,
            attachmentId,
            timestamp: Math.floor(Date.now() / 1000),
            wordCount,
            duration,
            processingTime: Math.round(processingTime / 1000)
        });
        userData.transcriptionHistory = userData.transcriptionHistory.slice(0, 10);

        await optimizedUpdateOne('user_transcriptions',
            { userId },
            { $set: userData }
        );

        // Invalidate cache to force fresh data on next load
        transcriptionDataCache.delete(userId);

        // Update metrics
        transcriptionMetrics.transcriptionsProcessed++;
        transcriptionMetrics.averageProcessingTime =
            (transcriptionMetrics.averageProcessingTime + processingTime) / 2;

        return {
            success: true,
            text,
            wordCount,
            duration,
            processingTime: Math.round(processingTime / 1000),
            language: transcriptionResult.language || 'unknown',
            model: model
        };

    } catch (error) {
        console.error(`[transcription] ❌ Error processing transcription for ${userId}:`, error);
        return {
            success: false,
            error: 'PROCESSING_ERROR',
            message: 'An error occurred while processing the transcription. Please try again.'
        };
    }
}

/**
 * Build transcription container for /you command
 * @param {string} userId - User ID
 * @param {Object} interaction - Discord interaction (for voice message search)
 * @param {Object} lastTranscription - Last transcription result (optional)
 * @param {Object} selectedState - Selected voice message state (optional)
 * @returns {Promise<Object>} Container builder
 */
async function buildTranscriptionContainer(userId, interaction, lastTranscription = null, selectedState = null, statusMessage = null, disableButton = false) {
    try {
        const userData = await getUserTranscriptionData(userId);
        const quota = await checkTranscriptionQuota(userId);
        const isOwner = userId === process.env.OWNER;

        // COMPONENT 1: Main transcription interface (OPERATION_COLORS.NEUTRAL)
        const component1 = new ContainerBuilder()
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        // Title and description
        component1.addTextDisplayComponents(
            new TextDisplayBuilder().setContent('# transcribe'),
            new TextDisplayBuilder().setContent('> transcribe ur own voice')
        );

        // Stats content
        let statsContent = '';
        if (isOwner) {
            statsContent += '**transcriptions:** unlimited\n';
            statsContent += '**reset:** owner privileges\n';
        } else {
            statsContent += `**transcriptions:** ${quota.remaining} left\n`;
            statsContent += `**reset:** <t:${quota.resetTime}:R>\n`;
        }

        statsContent += `**total words transcribed:** ${userData.totalWordsTranscribed.toLocaleString()}\n`;
        statsContent += `**total minutes transcribed:** ${userData.totalMinutesTranscribed.toFixed(2)}`;

        component1.addTextDisplayComponents(
            new TextDisplayBuilder().setContent(statsContent)
        );

        // Voice message select menu - always show (following imageUploader pattern)
        // Force refresh on first load to ensure we see the most recent voice messages
        const forceRefresh = !selectedState || !selectedState.selectedMessageId;
        const voiceMessages = await searchUserVoiceMessages(interaction, 50, forceRefresh);

        let selectMenu;
        if (voiceMessages.length > 0) {
            // Build options for actual voice messages
            const selectedValue = selectedState && selectedState.selectedMessageId && selectedState.selectedAttachmentId
                ? `${selectedState.selectedMessageId}:${selectedState.selectedAttachmentId}`
                : null;

            const selectOptions = await Promise.all(voiceMessages.slice(0, 25).map(async vm => {
                let durationSeconds = 0;
                let durationLabel = 'unknown';

                if (vm.duration && vm.duration > 0) {
                    durationSeconds = normalizeDuration(vm.duration);
                } else {
                    // Try to get duration from attachment if not in vm object
                    const attachmentDuration = vm.attachment?.duration_secs || vm.attachment?.duration;
                    if (attachmentDuration && attachmentDuration > 0) {
                        durationSeconds = normalizeDuration(attachmentDuration);
                    }
                }

                if (durationSeconds > 0) {
                    durationLabel = `${durationSeconds} second${durationSeconds !== 1 ? 's' : ''}`;
                }

                // Check if this voice message has been transcribed before
                const transcriptionStatus = await checkVoiceMessageTranscribed(userId, vm.messageId, vm.attachmentId);
                const statusDescription = transcriptionStatus ? 'previously transcribed' : 'not transcribed';

                const optionValue = `${vm.messageId}:${vm.attachmentId}`;

                return new StringSelectMenuOptionBuilder()
                    .setLabel(durationLabel)
                    .setDescription(statusDescription)
                    .setValue(optionValue)
                    .setEmoji('🎤')
                    .setDefault(selectedValue === optionValue); // Set as default if selected
            }));

            selectMenu = new StringSelectMenuBuilder()
                .setCustomId('transcription-voice-select')
                .setPlaceholder('select voice msg to transcribe')
                .addOptions(selectOptions);


        } else {
            // Build "no voice messages" option (following imageUploader pattern)
            selectMenu = new StringSelectMenuBuilder()
                .setCustomId('transcription-voice-select')
                .setPlaceholder('select voice msg to transcribe')
                .addOptions({
                    label: 'no voice msg found',
                    value: 'no-voice-messages',
                    description: 'Send a voice message in this channel first',
                    emoji: '🎤'
                });
                // Not disabled so users can interact with it (following imageUploader pattern)
        }

        component1.addActionRowComponents(new ActionRowBuilder().addComponents(selectMenu));

        // Transcribe button logic
        if (quota.canTranscribe) {
            // Enable button only if there's a valid selection, voice messages exist, AND not explicitly disabled
            const hasValidSelection = selectedState && selectedState.selectedMessageId && selectedState.selectedAttachmentId;
            const hasVoiceMessages = voiceMessages.length > 0;
            const buttonEnabled = hasValidSelection && hasVoiceMessages && !disableButton;

            const transcribeButton = new ButtonBuilder()
                .setCustomId('transcription-process')
                .setLabel('transcribe')
                .setStyle(ButtonStyle.Success)
                .setDisabled(!buttonEnabled);

            component1.addActionRowComponents(new ActionRowBuilder().addComponents(transcribeButton));
        }

        // Add status message if provided
        if (statusMessage) {
            component1.addTextDisplayComponents(
                new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`)
            );
        } else if (!quota.canTranscribe) {
            // Show quota exceeded message as status
            component1.addTextDisplayComponents(
                new TextDisplayBuilder().setContent(`**status:** ❌ You've used all ${TRANSCRIPTION_CONFIG.WEEKLY_LIMIT} transcriptions this week. Resets <t:${quota.resetTime}:R>.`)
            );
        }

        // COMPONENT 2: Transcription result (only if we have a result)
        if (lastTranscription && lastTranscription.success && selectedState) {
            const component2 = new ContainerBuilder()
                .setAccentColor(LOG_COLORS.SUCCESS);

            component2.addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# you said:'),
                new TextDisplayBuilder().setContent(`> ${lastTranscription.text}`)
            );

            // Add separator (small, no divider)
            component2.addSeparatorComponents(
                new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false)
            );

            // Get the original voice message to use its raw duration
            const originalVoiceMessage = voiceMessages.find(vm =>
                vm.messageId === selectedState.selectedMessageId &&
                vm.attachmentId === selectedState.selectedAttachmentId
            );

            // Use the original Discord duration for consistency with select menu
            let rawDuration = lastTranscription.duration;
            if (originalVoiceMessage && originalVoiceMessage.duration) {
                rawDuration = originalVoiceMessage.duration;
            }

            // Stats for the transcription (use normalization to match Discord's rounding behavior)
            const normalizedDuration = rawDuration ? normalizeDuration(rawDuration) : 0;
            const minutes = Math.floor(normalizedDuration / 60);
            const seconds = normalizedDuration % 60;
            const duration = normalizedDuration > 0 ?
                (minutes > 0 ? `${minutes}m${seconds}s` : `${seconds}s`) : 'unknown';



            const words = lastTranscription.text ? lastTranscription.text.split(' ').length : 0;
            const processingTime = lastTranscription.processingTime ? `${lastTranscription.processingTime}s` : 'unknown';

            const resultStats = `**length:** ${duration}\n**words:** ${words}\n**ttt:** ${processingTime}`;

            component2.addTextDisplayComponents(
                new TextDisplayBuilder().setContent(resultStats)
            );

            return [component1, component2];
        }

        return [component1];

    } catch (error) {
        console.error(`[transcription] ❌ Error building transcription container for ${userId}:`, error);

        // Return error container
        const errorContainer = new ContainerBuilder()
            .setAccentColor(LOG_COLORS.ERROR)
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('# transcribe\n\n❌ Error loading transcription interface. Please try again.')
            );

        return [errorContainer];
    }
}

module.exports = {
    getUserTranscriptionData,
    checkTranscriptionQuota,
    checkVoiceMessageTranscribed,
    searchUserVoiceMessages,
    invalidateVoiceMessageCache,
    processTranscription,
    buildTranscriptionContainer,
    normalizeDuration,
    getNextMondayReset,
    TRANSCRIPTION_CONFIG,
    transcriptionMetrics
};
