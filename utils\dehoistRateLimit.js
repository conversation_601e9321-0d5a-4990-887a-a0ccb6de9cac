const { mongoClient } = require("../mongo/client.js");

/**
 * Dehoist Rate Limiting System
 * Prevents spam and rate limit abuse
 */

// In-memory cache for quick lookups (resets on bot restart)
const userCooldowns = new Map(); // userId -> timestamp
const guildThrottles = new Map(); // guildId -> { count, resetTime }

// Configuration
const RATE_LIMITS = {
    // Individual user cooldowns (prevents spam nickname changes)
    USER_COOLDOWN_MIN: 5,    // Minimum cooldown in minutes
    USER_COOLDOWN_MAX: 10,   // Maximum cooldown in minutes
    
    // Guild-wide throttling (prevents bulk operations from overwhelming Discord)
    GUILD_THROTTLE_WINDOW: 60,     // 1 minute window
    GUILD_THROTTLE_MAX_ACTIONS: 10, // Max 10 dehoist actions per minute per guild
    
    // Bulk scan protection
    BULK_SCAN_COOLDOWN: 24 * 60 * 60 * 1000, // 24 hours
    BULK_SCAN_BATCH_SIZE: 50,                 // Process 50 members per batch
    BULK_SCAN_BATCH_DELAY: 2000,              // 2 seconds between batches

    // Smart scanning optimization (prevents malicious attack vectors)
    MAX_SCAN_LIMIT: 500,                      // Maximum members to scan (prevents abuse)
    MAX_CONSECUTIVE_NORMAL: 50,               // Stop after N consecutive normal names
};

/**
 * Check if a user is on cooldown for dehoist
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID  
 * @returns {Object} { allowed: boolean, timeLeft: number }
 */
function checkUserCooldown(userId, guildId) {
    const key = `${guildId}-${userId}`;
    const cooldownEnd = userCooldowns.get(key);
    
    if (!cooldownEnd) {
        return { allowed: true, timeLeft: 0 };
    }
    
    const timeLeft = cooldownEnd - Date.now();
    if (timeLeft <= 0) {
        userCooldowns.delete(key);
        return { allowed: true, timeLeft: 0 };
    }
    
    return { 
        allowed: false, 
        timeLeft: Math.ceil(timeLeft / (60 * 1000)) // Convert to minutes
    };
}

/**
 * Set a random cooldown for a user after dehoist
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {number} Cooldown duration in minutes
 */
function setUserCooldown(userId, guildId) {
    const key = `${guildId}-${userId}`;
    
    // Random cooldown between 5-10 minutes
    const cooldownMinutes = Math.floor(
        Math.random() * (RATE_LIMITS.USER_COOLDOWN_MAX - RATE_LIMITS.USER_COOLDOWN_MIN + 1)
    ) + RATE_LIMITS.USER_COOLDOWN_MIN;
    
    const cooldownEnd = Date.now() + (cooldownMinutes * 60 * 1000);
    userCooldowns.set(key, cooldownEnd);
    
    console.log(`[dehoistRateLimit] Set ${cooldownMinutes}min cooldown for user ${userId} in guild ${guildId}`);
    return cooldownMinutes;
}

/**
 * Check if guild is being throttled
 * @param {string} guildId - Guild ID
 * @returns {Object} { allowed: boolean, actionsLeft: number, resetTime: number }
 */
function checkGuildThrottle(guildId) {
    const throttle = guildThrottles.get(guildId);
    const now = Date.now();
    
    if (!throttle || now >= throttle.resetTime) {
        // Reset or create new throttle window
        guildThrottles.set(guildId, {
            count: 0,
            resetTime: now + (RATE_LIMITS.GUILD_THROTTLE_WINDOW * 1000)
        });
        return { 
            allowed: true, 
            actionsLeft: RATE_LIMITS.GUILD_THROTTLE_MAX_ACTIONS,
            resetTime: now + (RATE_LIMITS.GUILD_THROTTLE_WINDOW * 1000)
        };
    }
    
    const actionsLeft = RATE_LIMITS.GUILD_THROTTLE_MAX_ACTIONS - throttle.count;
    return {
        allowed: actionsLeft > 0,
        actionsLeft: Math.max(0, actionsLeft),
        resetTime: throttle.resetTime
    };
}

/**
 * Increment guild throttle counter
 * @param {string} guildId - Guild ID
 */
function incrementGuildThrottle(guildId) {
    const throttle = guildThrottles.get(guildId);
    if (throttle) {
        throttle.count++;
        console.log(`[dehoistRateLimit] Guild ${guildId} throttle: ${throttle.count}/${RATE_LIMITS.GUILD_THROTTLE_MAX_ACTIONS}`);
    }
}

/**
 * Check if bulk scan is on cooldown
 * @param {number} lastScanTime - Timestamp of last scan
 * @returns {Object} { allowed: boolean, timeLeft: number }
 */
function checkBulkScanCooldown(lastScanTime) {
    if (!lastScanTime) {
        return { allowed: true, timeLeft: 0 };
    }
    
    const timeLeft = (lastScanTime + RATE_LIMITS.BULK_SCAN_COOLDOWN) - Date.now();
    if (timeLeft <= 0) {
        return { allowed: true, timeLeft: 0 };
    }
    
    return {
        allowed: false,
        timeLeft: Math.ceil(timeLeft / (60 * 60 * 1000)) // Convert to hours
    };
}

/**
 * Enhanced dehoist handler with rate limiting and caching
 * @param {GuildMember} member - Discord guild member
 * @param {Object} dehoistData - Dehoist configuration
 * @param {boolean} isBulkScan - Whether this is part of a bulk scan
 * @returns {Object} { success: boolean, reason: string, cooldown?: number }
 */
async function safeHandleDehoist(member, dehoistData, isBulkScan = false) {
    const userId = member.user.id;
    const guildId = member.guild.id;

    // Use cached check for whether member needs dehoisting
    const { checkMemberNeedsDehoisting } = require('./dehoistCache.js');
    const needsDehoist = checkMemberNeedsDehoisting(userId, guildId, member.displayName, dehoistData.blocked);

    if (!needsDehoist) {
        return { success: false, reason: 'not_needed' };
    }

    // Skip rate limiting for bulk scans (they have their own throttling)
    if (!isBulkScan) {
        // Check user cooldown
        const userCheck = checkUserCooldown(userId, guildId);
        if (!userCheck.allowed) {
            console.log(`[dehoistRateLimit] User ${userId} on cooldown for ${userCheck.timeLeft} minutes`);
            return {
                success: false,
                reason: 'user_cooldown',
                timeLeft: userCheck.timeLeft
            };
        }

        // Check guild throttle
        const guildCheck = checkGuildThrottle(guildId);
        if (!guildCheck.allowed) {
            const resetIn = Math.ceil((guildCheck.resetTime - Date.now()) / 1000);
            console.log(`[dehoistRateLimit] Guild ${guildId} throttled, resets in ${resetIn}s`);
            return {
                success: false,
                reason: 'guild_throttle',
                resetIn: resetIn
            };
        }
    }

    // Attempt dehoist
    const handleDehoist = require('./handleDehoist');
    const result = await handleDehoist(member, dehoistData);

    if (result) {
        // Success - apply rate limiting and invalidate cache
        const { invalidateMemberDehoistStatus } = require('./dehoistCache.js');
        invalidateMemberDehoistStatus(userId, guildId);

        if (!isBulkScan) {
            incrementGuildThrottle(guildId);
            const cooldownMinutes = setUserCooldown(userId, guildId);
            return {
                success: true,
                reason: 'success',
                cooldown: cooldownMinutes
            };
        }
        return { success: true, reason: 'success' };
    } else {
        return { success: false, reason: 'dehoist_failed' };
    }
}

/**
 * Enhanced bulk scan with smart optimization and security protections
 *
 * SECURITY OPTIMIZATION: Prevents malicious attack vectors where users set names
 * like 'a' with HTML entities (!) that appear before regular alphabet, forcing
 * the system to scan entire large member lists.
 *
 * Features:
 * - Sorts members by display name to process problematic names first
 * - Limits scanning to reasonable number (500 members) to prevent abuse
 * - Early termination after consecutive normal names (50) to avoid unnecessary scanning
 * - Only processes members that actually need dehoisting
 *
 * @param {Guild} guild - Discord guild
 * @param {Object} dehoistData - Dehoist configuration
 * @param {Function} progressCallback - Optional progress callback
 * @returns {Object} Scan results including scanned count
 */
async function safeBulkScan(guild, dehoistData, progressCallback = null) {
    console.log(`[dehoistRateLimit] Starting optimized bulk scan for guild ${guild.id}`);

    const { setCachedBulkScanStatus } = require('./dehoistCache.js');

    // Set initial scan status
    setCachedBulkScanStatus(guild.id, {
        inProgress: true,
        startTime: Date.now(),
        processed: 0,
        total: 0
    });

    const members = await guild.members.fetch();
    let processed = 0;
    let dehoisted = 0;
    let failed = 0;
    let skipped = 0;

    const totalMembers = members.size;
    let memberArray = Array.from(members.values());

    // PERFORMANCE OPTIMIZATION: Smart top-position scanning to prevent malicious attack vectors
    // Sort members by display name to process those with blocked characters first
    memberArray.sort((a, b) => a.displayName.localeCompare(b.displayName));

    // SECURITY: Limit scanning to prevent malicious users from forcing full member list scans
    const scanLimit = Math.min(memberArray.length, RATE_LIMITS.MAX_SCAN_LIMIT);

    console.log(`[dehoistRateLimit] 🔒 Smart scanning: Processing first ${scanLimit}/${totalMembers} members (sorted by display name)`);

    // Smart filtering: Only scan members whose names start with blocked characters + early termination
    const { checkMemberNeedsDehoisting } = require('./dehoistCache.js');
    const membersNeedingDehoist = [];
    let consecutiveNormalNames = 0;

    for (let i = 0; i < scanLimit; i++) {
        const member = memberArray[i];
        const needsDehoist = checkMemberNeedsDehoisting(member.user.id, guild.id, member.displayName, dehoistData.blocked);

        if (needsDehoist) {
            membersNeedingDehoist.push(member);
            consecutiveNormalNames = 0; // Reset counter
        } else {
            consecutiveNormalNames++;
            // Early termination: If we hit many consecutive normal names, likely past the problem area
            if (consecutiveNormalNames >= RATE_LIMITS.MAX_CONSECUTIVE_NORMAL) {
                console.log(`[dehoistRateLimit] 🎯 Early termination: Found ${consecutiveNormalNames} consecutive normal names, stopping scan at position ${i + 1}`);
                break;
            }
        }
    }

    console.log(`[dehoistRateLimit] 🎯 Smart scan results: ${membersNeedingDehoist.length} members need dehoisting (scanned ${Math.min(scanLimit, memberArray.length)} of ${totalMembers} total)`);

    // Update memberArray to only include members that need dehoisting for batch processing
    memberArray = membersNeedingDehoist;
    const batches = Math.ceil(memberArray.length / RATE_LIMITS.BULK_SCAN_BATCH_SIZE);

    for (let batchIndex = 0; batchIndex < batches; batchIndex++) {
        const batchStart = batchIndex * RATE_LIMITS.BULK_SCAN_BATCH_SIZE;
        const batchEnd = Math.min(batchStart + RATE_LIMITS.BULK_SCAN_BATCH_SIZE, memberArray.length);
        const batch = memberArray.slice(batchStart, batchEnd);

        console.log(`[dehoistRateLimit] Processing batch ${batchIndex + 1}/${batches} (${batch.length} members)`);

        // Process batch members in parallel (with limited concurrency)
        const PARALLEL_LIMIT = 5; // Process up to 5 members simultaneously
        const batchPromises = [];

        for (let i = 0; i < batch.length; i += PARALLEL_LIMIT) {
            const parallelBatch = batch.slice(i, i + PARALLEL_LIMIT);

            const parallelPromise = Promise.all(
                parallelBatch.map(async (member) => {
                    const result = await safeHandleDehoist(member, dehoistData, true);

                    if (result.success) {
                        return { type: 'dehoisted', member };
                    } else if (result.reason === 'dehoist_failed') {
                        return { type: 'failed', member };
                    } else {
                        return { type: 'skipped', member };
                    }
                })
            );

            batchPromises.push(parallelPromise);
        }

        // Wait for all parallel batches to complete
        const batchResults = await Promise.all(batchPromises);

        // Flatten results and count
        const flatResults = batchResults.flat();
        flatResults.forEach(result => {
            switch (result.type) {
                case 'dehoisted':
                    dehoisted++;
                    break;
                case 'failed':
                    failed++;
                    break;
                case 'skipped':
                    skipped++;
                    break;
            }
            processed++;
        });

        // Update scan status
        setCachedBulkScanStatus(guild.id, {
            inProgress: true,
            startTime: Date.now(),
            processed,
            total: totalMembers,
            dehoisted,
            failed,
            skipped
        });

        // Call progress callback if provided
        if (progressCallback) {
            progressCallback({
                processed,
                total: totalMembers,
                dehoisted,
                failed,
                skipped,
                batch: batchIndex + 1,
                totalBatches: batches
            });
        }

        // Wait between batches (except for the last one)
        if (batchIndex < batches - 1) {
            console.log(`[dehoistRateLimit] Waiting ${RATE_LIMITS.BULK_SCAN_BATCH_DELAY}ms before next batch...`);
            await new Promise(resolve => setTimeout(resolve, RATE_LIMITS.BULK_SCAN_BATCH_DELAY));
        }
    }

    // Mark scan as complete
    setCachedBulkScanStatus(guild.id, {
        inProgress: false,
        completed: true,
        endTime: Date.now(),
        processed,
        total: totalMembers,
        scanned: Math.min(scanLimit, totalMembers), // Track how many we actually scanned
        dehoisted,
        failed,
        skipped
    });

    console.log(`[dehoistRateLimit] 🎯 Smart bulk scan complete: ${dehoisted} dehoisted, ${failed} failed, ${skipped} skipped (scanned ${Math.min(scanLimit, totalMembers)}/${totalMembers} members)`);

    return {
        processed,
        dehoisted,
        failed,
        skipped,
        totalMembers,
        scanned: Math.min(scanLimit, totalMembers) // Include scanned count in return
    };
}

/**
 * Clean up expired cooldowns (call periodically)
 */
function cleanupExpiredCooldowns() {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, expireTime] of userCooldowns.entries()) {
        if (now >= expireTime) {
            userCooldowns.delete(key);
            cleaned++;
        }
    }
    
    if (cleaned > 0) {
        console.log(`[dehoistRateLimit] Cleaned up ${cleaned} expired cooldowns`);
    }
}

// Clean up expired cooldowns every 5 minutes
setInterval(cleanupExpiredCooldowns, 5 * 60 * 1000);

module.exports = {
    RATE_LIMITS,
    checkUserCooldown,
    setUserCooldown,
    checkGuildThrottle,
    incrementGuildThrottle,
    checkBulkScanCooldown,
    safeHandleDehoist,
    safeBulkScan,
    cleanupExpiredCooldowns
};
