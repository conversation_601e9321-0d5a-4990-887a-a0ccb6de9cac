const { GuildMember } = require("discord.js");
const { sendDehoistUsernameLog } = require("./sendLog.js");

/**
 * Get a random item from an array
 * @param {Array} array - Array to select from
 * @returns {*} Random item from the array
 */
function getRandomArrayItem(array) {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * Basic dehoist function (used by rate-limited wrapper)
 * @param { GuildMember } member
 * @param { Object } data - Dehoist configuration
 * @returns { boolean } Success status
 */
module.exports = async function handleDehoist(member, data) {
    if(data.blocked.includes(member.displayName[0])){
        // Name
        if (data.enabled) { // Use standard enabled field
            let newUsername = member.displayName;
            if (data.blocked.includes(newUsername[0])) {
                const oldName = member.displayName;
                while (data.blocked.includes(newUsername[0]) && newUsername.length) newUsername = newUsername.slice(1);
                if (!newUsername.length) newUsername = getRandomArrayItem(data.names);
                // Use graceful error handling for nickname setting
                const { safeEditMember } = require('./roleAssignmentHandler.js');
                const editResult = await safeEditMember(member, { nick: newUsername }, {
                    operation: 'dehoist',
                    source: 'handleDehoist'
                });

                if (editResult.success && editResult.nicknameResult?.success) {
                    // Send dehoist username log
                    try {
                        await sendDehoistUsernameLog(
                            member.guild.id,
                            member.user.id,
                            oldName,
                            newUsername,
                            member.client
                        );
                    } catch (logError) {
                        console.error('[dehoist] ⚠️ Failed to send dehoist log:', logError);
                    }

                    console.log(`[dehoist] ✅ Successfully dehoisted ${oldName} → ${newUsername}`);
                    return true; // Success
                } else {
                    console.warn(`[dehoist] ❌ Failed to set nickname for ${member.user.tag}: ${editResult.nicknameResult?.reason || 'unknown error'}`);
                    return false; // Failed
                }
            }
        }
    }
    return false; // Not needed or not blocked
}
