# 🎒 Items System - Comprehensive Documentation

## Overview
The Items system provides comprehensive custom item management with creation, editing, inventory tracking, and sophisticated drop mechanics across multiple locations with rarity-based distribution.

## Core Architecture

### Main Files
- `commands/utility/items.js` - Primary items interface
- `utils/itemDrops.js` - Drop calculation and distribution
- `utils/itemCache.js` - Performance caching system
- `utils/imageUploader.js` - Unified image upload handling

### Database Collections
- `custom_items` - Global item definitions
- `user_inventories` - Per-user item collections
- `guilds` - Guild-specific item configurations

## Key Functions

### Primary Interface Functions

#### `execute(interaction)`
**Purpose**: Main slash command handler for items system
**Parameters**: 
- `interaction` - Discord slash command interaction

**Behavior**:
- Displays main items interface with navigation
- Shows item statistics and management options
- Provides access to inventory and creation tools

**Internal Logic**:
```javascript
// Load cached items for performance
const items = await getCachedItems(guildId);
const itemCount = items ? items.length : 0;

// Build interface with current statistics
const container = buildItemsContainer(itemCount, userPermissions);
```

#### `select(interaction, selectPath)`
**Purpose**: Handles cascading select menu navigation
**Parameters**:
- `interaction` - Select menu interaction
- `selectPath` - Navigation path for nested selections

**Navigation Flow**:
```
items-select → ['create'] → Item type selection → Icon selection → Configuration modal
items-select → ['edit'] → Item selection → Edit interface
items-select → ['view'] → Item display with filtering options
```

**Special Behaviors**:
- **Multi-Step Creation**: Type → Icon → Configuration workflow
- **Data Persistence**: Maintains selections across menu interactions
- **Dynamic Options**: Available options change based on previous selections

#### `buttons(interaction)`
**Purpose**: Handles button interactions for item actions
**Key Buttons**:
- `items-create-[type]` - Create item of specific type
- `items-edit-[id]` - Edit existing item
- `items-delete-[id]` - Delete item with confirmation
- `items-toggle-[id]` - Enable/disable item drops

**Button ID Parsing**:
```javascript
const [action, ...params] = customId.split('-').slice(1);
switch (action) {
    case 'create':
        const itemType = params[0];
        return await handleItemCreation(interaction, itemType);
    case 'edit':
        const itemId = params[0];
        return await handleItemEdit(interaction, itemId);
}
```

#### `modalSubmit(interaction)`
**Purpose**: Processes item configuration forms
**Modal Types**:
- `items-create-modal` - New item creation
- `items-edit-modal-[id]` - Item modification
- `items-config-modal` - System configuration

**Form Processing**:
```javascript
const itemData = {
    name: interaction.fields.getTextInputValue('item-name'),
    description: interaction.fields.getTextInputValue('item-description'),
    type: extractTypeFromCustomId(interaction.customId),
    rarity: validateRarity(interaction.fields.getTextInputValue('item-rarity')),
    parameters: parseItemParameters(interaction.fields)
};
```

## Special Behaviors

### Multi-Step Item Creation
**Step 1: Type Selection**
- Choose item category (weapon, armor, consumable, etc.)
- Determines available parameters and validation rules

**Step 2: Icon Selection**
- Browse uploaded images by category
- Recent uploads prioritized (last 10 messages)
- Fallback to default icons if no uploads found

**Step 3: Configuration Modal**
- Dynamic form based on item type
- Parameter validation specific to item category
- Rarity selection with weight implications

### Multi-Step Interface State Management
**State Persistence Across Steps**:
```javascript
// Step 1: Store type selection in customId
const typeSelectCustomId = `items-icon-select-${JSON.stringify({
    type: selectedType,
    step: 'icon-selection',
    timestamp: Date.now()
})}`;

// Step 2: Preserve type while adding icon selection
const iconSelectCustomId = `items-config-modal-${JSON.stringify({
    type: selectedType,
    iconUrl: selectedIconUrl,
    step: 'configuration'
})}`;

// Step 3: Access all previous selections in modal
function buildConfigModal(stateData) {
    const modal = new ModalBuilder()
        .setCustomId(`items-create-modal-${JSON.stringify(stateData)}`)
        .setTitle(`Create ${stateData.type} Item`);

    // Pre-populate fields based on type
    const fields = getFieldsForType(stateData.type);
    modal.addComponents(fields);

    return modal;
}
```

**Modal State Clearing for New Operations**:
```javascript
// Clear modal when switching from edit to create
function clearModalFields(modal) {
    modal.components.forEach(row => {
        row.components.forEach(component => {
            if (component.data.value) {
                component.data.value = ''; // Clear previous values
            }
            if (component.data.placeholder) {
                component.data.placeholder = getDefaultPlaceholder(component.data.custom_id);
            }
        });
    });
}
```

### Drop Location System
**Supported Locations**:
- `TEXT` - Text message activity
- `VOICE` - Voice channel participation
- `LEVEL_UP` - EXP level advancement
- `STARFALL` - Daily reward system

**Drop Calculation**:
```javascript
const dropChance = calculateDropChance(location, userActivity, itemRarity);
const selectedItem = weightedRandomSelection(availableItems);
```

### Rarity System
**Tier Structure**:
```javascript
const RARITIES = {
    common: { weight: 100, color: 0x808080, emoji: '⚪' },
    uncommon: { weight: 50, color: 0x00ff00, emoji: '🟢' },
    rare: { weight: 25, color: 0x0080ff, emoji: '🔵' },
    epic: { weight: 10, color: 0x8000ff, emoji: '🟣' },
    legendary: { weight: 5, color: 0xff8000, emoji: '🟠' },
    mythic: { weight: 1, color: 0xff0080, emoji: '🔴' }
};
```

**Weight-Based Distribution**:
- Higher weight = more common drops
- Cumulative probability calculation
- Guaranteed minimum drop rates

### Image Upload Integration
**Unified Upload System**:
- Scans recent messages for image attachments
- Categorizes by upload context
- Validates image formats and sizes
- Provides fallback options

**Performance Optimization**:
- Limits scan to 10 most recent messages
- Caches upload results for session
- Async processing to prevent blocking

## Internal Logic Details

### Item Data Structure
```javascript
{
    _id: ObjectId,
    name: String,
    description: String,
    type: String,           // weapon, armor, consumable, etc.
    rarity: String,         // common, uncommon, rare, epic, legendary, mythic
    dropLocations: [String], // TEXT, VOICE, LEVEL_UP, STARFALL
    parameters: {
        // Type-specific parameters
        damage: Number,     // for weapons
        defense: Number,    // for armor
        effect: String      // for consumables
    },
    iconUrl: String,
    disabled: Boolean,
    created: Date,
    modified: Date,
    createdBy: String       // User ID of creator
}
```

### Drop Mechanics
**Probability Calculation**:
```javascript
function calculateDropProbability(userActivity, itemRarity, location) {
    const baseRate = DROP_RATES[location];
    const rarityMultiplier = RARITIES[itemRarity].weight / 100;
    const activityBonus = Math.min(userActivity / 100, 2.0);
    
    return baseRate * rarityMultiplier * activityBonus;
}
```

**Weighted Selection Algorithm**:
```javascript
function selectItemByWeight(items) {
    const totalWeight = items.reduce((sum, item) => 
        sum + RARITIES[item.rarity].weight, 0);
    
    const random = Math.random() * totalWeight;
    let currentWeight = 0;
    
    for (const item of items) {
        currentWeight += RARITIES[item.rarity].weight;
        if (random <= currentWeight) {
            return item;
        }
    }
}
```

### Inventory Management
**User Inventory Structure**:
```javascript
{
    userId: String,
    guildId: String,
    items: [{
        itemId: ObjectId,
        quantity: Number,
        acquired: Date,
        source: String      // TEXT, VOICE, LEVEL_UP, STARFALL
    }],
    lastUpdated: Date
}
```

**Inventory Operations**:
- Add item with source tracking
- Remove items with quantity management
- Transfer items between users (if enabled)
- Bulk operations for administrative tasks

## Testing Requirements

### Unit Tests
1. **Item Creation Workflow**
   ```javascript
   async testItemCreation() {
       // Test type selection
       const typeInteraction = this.createMockInteraction(5, 'items-create-type-select', ['weapon']);
       await items.select(typeInteraction, []);
       
       // Test configuration modal
       const modalInteraction = this.createMockInteraction(6, 'items-create-modal', {
           'item-name': 'Test Sword',
           'item-description': 'A test weapon',
           'item-damage': '100'
       });
       await items.modalSubmit(modalInteraction);
   }
   ```

2. **Drop System Testing**
   ```javascript
   async testDropMechanics() {
       const dropResult = await rollForItemDrop(userId, guildId, 'TEXT', 1.0);
       // Validate drop probability and item selection
   }
   ```

3. **Rarity System Validation**
   ```javascript
   async testRaritySystem() {
       // Validate rarity structure
       for (const [rarity, data] of Object.entries(RARITIES)) {
           assert(data.weight > 0);
           assert(data.color !== undefined);
           assert(data.emoji !== undefined);
       }
   }
   ```

### Integration Tests
1. **Inventory Management**
   - Item addition and removal
   - Quantity tracking accuracy
   - Cross-user operations

2. **Cache System**
   - Item data caching
   - Invalidation triggers
   - Performance improvements

3. **Image Upload System**
   - Upload detection and processing
   - Format validation
   - Fallback behavior

### Performance Tests
1. **Response Time Benchmarks**
   - Item interface loading: <300ms
   - Creation workflow: <1000ms
   - Inventory operations: <500ms

2. **Drop System Performance**
   - Drop calculation speed
   - Weighted selection efficiency
   - Concurrent drop handling

## Error Handling Patterns

### Item Validation
```javascript
function validateItemData(itemData) {
    if (!itemData.name || itemData.name.length < 1) {
        throw new Error('Item name is required');
    }
    
    if (!RARITIES[itemData.rarity]) {
        throw new Error('Invalid rarity specified');
    }
    
    if (itemData.type === 'weapon' && !itemData.parameters.damage) {
        throw new Error('Weapons must have damage value');
    }
}
```

### Drop System Errors
```javascript
try {
    const droppedItem = await rollForItemDrop(userId, guildId, location);
    if (droppedItem) {
        await addToInventory(userId, guildId, droppedItem);
    }
} catch (error) {
    console.error('[items] Drop error:', error);
    // Continue without blocking user activity
}
```

### Database Operation Failures
```javascript
try {
    await itemsCol.insertOne(itemData);
    await invalidateItemCache(guildId);
} catch (error) {
    if (error.code === 11000) { // Duplicate key
        throw new Error('Item with this name already exists');
    }
    throw new Error('Failed to create item');
}
```

## Maintenance Considerations

### Regular Tasks
1. **Drop Rate Analysis**
   - Monitor drop frequency by location
   - Analyze rarity distribution
   - Adjust weights based on usage patterns

2. **Inventory Cleanup**
   - Remove items from deleted users
   - Consolidate duplicate entries
   - Archive old inventory data

3. **Performance Monitoring**
   - Cache hit rates
   - Database query optimization
   - Image upload processing times

### Scaling Considerations
- Inventory sharding for large user bases
- Drop calculation optimization for high-activity servers
- Image storage and CDN integration
- Rate limiting for item operations

## Development Guidelines

### Code Patterns
- Use unified image uploader for all upload operations
- Implement proper rarity validation in all item operations
- Maintain cache coherency across item modifications
- Follow Components v2 compliance for all interfaces

### Testing Standards
- Test all item types and their specific parameters
- Validate drop mechanics across all locations
- Include edge cases (empty inventories, invalid items)
- Performance test with large item collections

### Documentation Updates
- Update rarity definitions when adding new tiers
- Document new item types and their parameters
- Maintain drop rate documentation
- Keep testing examples current with implementation
