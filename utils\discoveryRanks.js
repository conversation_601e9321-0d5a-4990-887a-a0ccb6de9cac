/**
 * Unified Discovery Rank Calculator
 * Single source of truth for all discovery rank calculations
 */

const { optimizedCountDocuments } = require('./database-optimizer.js');

/**
 * Calculate discovery rank for an item (SIMPLE VERSION)
 * @param {Object} item - Inventory item
 * @param {string} guildId - Guild ID for guild context
 * @returns {Object} Discovery rank data
 */
async function calculateSimpleDiscoveryRank(item, guildId) {
    try {
        const startTime = Date.now();
        console.log(`[discoveryRanks] 🔍 SIMPLE calculation for ${item.itemName}`);

        // Guild discovery: Count items with this name found in the SAME guild where this item was found
        const actualFoundInGuild = item.foundInGuild || guildId;

        const guildRank = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName,
            foundInGuild: actualFoundInGuild,
            droppedAt: { $lt: new Date(item.droppedAt) }
        }) + 1;

        const guildTotal = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName,
            foundInGuild: actualFoundInGuild
        });

        // Global discovery: Count items with this name found anywhere before this timestamp
        const globalRank = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName,
            droppedAt: { $lt: new Date(item.droppedAt) }
        }) + 1;

        const globalTotal = await optimizedCountDocuments('user_inventory', {
            itemName: item.itemName
        });

        const duration = Date.now() - startTime;
        const result = {
            guildRank,
            guildTotal,
            globalRank,
            globalTotal
        };

        console.log(`[discoveryRanks] ✅ SIMPLE result (${duration}ms):`, result);

        return result;

    } catch (error) {
        console.error('[discoveryRanks] ❌ Error calculating simple discovery rank:', error);
        return {
            guildRank: 1,
            guildTotal: 1,
            globalRank: 1,
            globalTotal: 1
        };
    }
}

/**
 * Format discovery ranks for display
 * @param {Object} ranks - Discovery rank data
 * @param {Object} options - Display options
 * @returns {string} Formatted discovery text
 */
function formatUnifiedDiscoveryRanks(ranks, options = {}) {
    const { showTotals = true, showOrdinals = true } = options;
    
    let result = '';
    
    if (ranks.guildRank && ranks.guildTotal) {
        const ordinal = showOrdinals ? getOrdinalSuffix(ranks.guildRank) : '';
        if (showTotals) {
            result += `${ranks.guildRank}${ordinal}/${ranks.guildTotal} server`;
        } else {
            result += `${ranks.guildRank}${ordinal} server`;
        }
    }
    
    if (ranks.globalRank && ranks.globalTotal) {
        if (result) result += ', ';
        const ordinal = showOrdinals ? getOrdinalSuffix(ranks.globalRank) : '';
        if (showTotals) {
            result += `${ranks.globalRank}${ordinal}/${ranks.globalTotal} global`;
        } else {
            result += `${ranks.globalRank}${ordinal} global`;
        }
    }
    
    return result;
}

/**
 * Get ordinal suffix for numbers (1st, 2nd, 3rd, etc.)
 */
function getOrdinalSuffix(num) {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
}

module.exports = {
    calculateSimpleDiscoveryRank,
    formatUnifiedDiscoveryRanks,
    getOrdinalSuffix
};
