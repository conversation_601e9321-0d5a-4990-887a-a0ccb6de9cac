---
type: "always_apply"
---

# 🧩 Rule: Creating New Core Features

When building **new features for the bot**, follow these mandatory steps:

1. **Interaction Setup**
   - Always hook new buttons and select menus into the global `interactionCreate` listener.
   - If omitted, the feature will not function at runtime.

2. **Feature Menu Registration**
   - Add the feature to `featuresmenu.cs`.
   - If the user is on the feature’s own page, do **not** display the feature in its own menu (prevent self-listing).

3. **Owner Visibility Logic**
   - Determine what only the owner can see or do by comparing similar files.
   - Use existing features as examples for splitting behavior between owner and public access.

4. **Feature Structure**
   - Every feature must follow the same format:
     - Title
     - Quote
     - Constants and parameters displayed with `TextDisplayBuilder`
   - Review how `create` and `edit` functions work in other features.
   - Analyze and match existing code patterns **exactly** — this ensures seamless integration and UI/UX consistency.

5. **UI/UX Implementation Standards**
   - **MANDATORY**: Follow all patterns in `docs/UI_UX_IMPLEMENTATION_GUIDE.md`
   - **Modal State Management**: Always clear modal fields when switching operations (edit→create, type changes)
   - **Select Menu Defaults**: Show current configuration values in placeholders and mark as default options
   - **Cascading State Persistence**: Embed state in customIds and show previous selections in context
   - **Data Persistence**: Never lose user input across multi-step interfaces

6. **Select Menu Behavior**
   - When using select menus to edit part of a configuration, reveal a **second select menu** specific to the selected task.
   - Example: When creating a new item, the first select menu chooses an icon category, and the second reveals recently uploaded icons for that category.
   - **CRITICAL**: Preserve information from the first select menu using state embedding in customIds
   - Show previous selections in placeholders: `Type: weapon → Select icon`
   - Follow cascading state patterns in `docs/features/ITEMS_SYSTEM.md` and `docs/features/EXP_SYSTEM.md`

6. **Update Bot Invite Permissions**
   - If the new feature requires additional permissions (e.g., Manage Roles, View Audit Log), update the bot’s invite link to include the necessary scopes.
   - This ensures users can use the new feature without encountering permission-related errors.
   - Review existing permissions and match only what's required.

7. **Color Usage**
   - Always import and use predefined accent colors from `colors.js`.
   - Do not hardcode hex values inline; this keeps visual styles consistent and centralizes theme updates.

8. **Feature Documentation Requirements**
   - **Create feature documentation** in `docs/features/[FEATURE_NAME].md` following the template in existing feature docs
   - **Include all sections**: Overview, Key Functions, Special Behaviors, Internal Logic, Testing Requirements
   - **Document UI patterns**: Modal clearing, select menu defaults, state persistence specific to your feature
   - **Add testing examples**: Extend `BotTestBase` with feature-specific test cases

9. **Drop Category Expansion**
   - When adding a new category of item drop location, **always register it with the drop notification system**.
   - Match behavior and structure from existing categories (Level Up, Starfall, Text/Voice EXP).
   - This ensures drops are visible and consistent across all tracking systems.

## 📚 **Required Reading Before Implementation**

**MANDATORY**: Review these documentation files before creating any new feature:

### **Core Implementation Guides**
- **`docs/UI_UX_IMPLEMENTATION_GUIDE.md`** - UI/UX patterns, modal state management, select menu defaults
- **`docs/FEATURE_DOCUMENTATION.md`** - Overview of all existing features and patterns
- **`docs/COMPREHENSIVE_TESTING_GUIDE.md`** - Testing requirements and shared test base usage

### **Feature-Specific Examples**
- **`docs/features/EXP_SYSTEM.md`** - Modal clearing, cascading select menus, permission boundaries
- **`docs/features/ITEMS_SYSTEM.md`** - Multi-step workflows, state persistence, rarity systems
- **`docs/features/OWNER_ADMINISTRATION.md`** - Security patterns, modular architecture
- **`docs/features/YOU_COMMAND_SYSTEM.md`** - Multi-section interfaces, data integration
- **`docs/features/UTILITY_FEATURES.md`** - Configuration interfaces, bulk operations
- **`docs/features/SPECIALIZED_SYSTEMS.md`** - Advanced functionality patterns

### **Development Standards**
- **`docs/CONTRIBUTING.md`** - Code standards and contribution guidelines
- **`docs/COLOR_SYSTEM_GUIDE.md`** - UI color usage and consistency
- **`tests/shared/README.md`** - Shared test base documentation and examples

## 🧪 **Testing Integration Requirements**

**MANDATORY**: Every new feature must include comprehensive tests:

1. **Extend BotTestBase**: Use `tests/shared/BotTestBase.js` for all feature tests
2. **Test All UI Patterns**: Modal clearing, select menu defaults, state persistence
3. **Include Performance Tests**: Response time benchmarks for all major operations
4. **Security Validation**: Permission boundaries and access control testing
5. **Follow Examples**: Use patterns from `tests/test_exp_system_focused.js` and `tests/test_items_system_focused.js`

📌 **Do not guess. Look at existing features and replicate structure, logic, and formatting 1:1.**

📌 **CRITICAL**: Failure to follow UI/UX patterns in `docs/UI_UX_IMPLEMENTATION_GUIDE.md` will result in inconsistent user experience and user data loss.
