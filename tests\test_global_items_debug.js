const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class GlobalItemsDebugTester extends BotTestBase {
    constructor() {
        super('GlobalItemsDebugTest');
    }

    async testGlobalItemsDatabase() {
        try {
            const { mongoClient } = require('../mongo/client.js');
            const db = mongoClient.db('seventeen_bot');

            console.log('🔍 Checking ALL items in database...');

            // Check ALL items first
            const allItems = await db.collection('custom_items').find({}).toArray();
            console.log(`   Total items in database: ${allItems.length}`);

            // Check global items specifically
            const globalItems = await db.collection('custom_items').find({
                guildId: null
            }).toArray();
            console.log(`   Global items (guildId: null): ${globalItems.length}`);
            globalItems.forEach(item => {
                console.log(`   - ${item.name} (ID: ${item.id}, Locations: ${item.dropLocations})`);
                console.log(`     Rarity: ${item.rarity || 'NONE'}, Weight: ${item.rarity?.weight || 'N/A'}`);
                console.log(`     Full rarity object:`, item.rarity);
            });

            // Check global items with LEVEL_UP location
            const globalLevelUpItems = await db.collection('custom_items').find({
                guildId: null,
                dropLocations: 'LEVEL_UP',
                disabled: { $ne: true }
            }).toArray();
            console.log(`   Global LEVEL_UP items: ${globalLevelUpItems.length}`);

            // Check global levels configuration
            const globalLevels = await db.collection('global_levels').find({
                isActive: true
            }).sort({ level: 1 }).toArray();

            console.log(`   Total global levels configured: ${globalLevels.length}`);
            const levelsWithItems = globalLevels.filter(level => level.rewards?.items?.length > 0);
            console.log(`   Global levels with item rewards: ${levelsWithItems.length}`);

            levelsWithItems.forEach(level => {
                console.log(`   Level ${level.level}: ${level.name} - Items: [${level.rewards.items.join(', ')}]`);
            });

            // Check if configured items exist
            if (levelsWithItems.length > 0) {
                const allItemIds = levelsWithItems.flatMap(level => level.rewards.items);
                const uniqueItemIds = [...new Set(allItemIds)];

                console.log('   Checking configured reward items...');
                for (const itemId of uniqueItemIds) {
                    const item = await db.collection('custom_items').findOne({ id: itemId });
                    if (item) {
                        console.log(`   ✅ Item ${itemId} exists: ${item.name} (guildId: ${item.guildId})`);
                    } else {
                        console.log(`   ❌ Item ${itemId} NOT FOUND`);
                    }
                }
            }

            return allItems.length > 0;
        } catch (error) {
            console.error('   Database check error:', error.message);
            return false;
        }
    }

    async testGlobalLevelSystem() {
        try {
            const { awardGlobalExp } = require('../utils/globalLevels.js');
            
            console.log('🔍 Testing global level system...');
            
            // Test awarding global EXP
            const testUserId = process.env.OWNER;
            const result = await awardGlobalExp(testUserId, 1, 'TEST');
            
            console.log(`   Current global level: ${result.newLevel}`);
            console.log(`   Current global EXP: ${result.newExp}`);
            console.log(`   Leveled up: ${result.leveledUp}`);
            
            if (result.levelRewards) {
                console.log(`   Level rewards: ${JSON.stringify(result.levelRewards)}`);
            }
            
            return true;
        } catch (error) {
            console.error('   Global level system error:', error.message);
            return false;
        }
    }

    async testItemDropSystem() {
        try {
            const { getDroppableItems } = require('../utils/itemDrops.js');

            console.log('🔍 Testing item drop system...');

            // Test global item drops in guild context (should include global items now)
            const testGuildId = process.env.GUILDIDTWO;
            const guildTextItems = await getDroppableItems('TEXT', testGuildId);
            console.log(`   Guild TEXT items (including global): ${guildTextItems.length}`);

            guildTextItems.forEach(item => {
                const scope = item.guildId === null ? 'GLOBAL' : 'GUILD';

                // Get proper weight from rarity
                let weight = 'N/A';
                if (typeof item.rarity === 'string') {
                    try {
                        const { RARITIES } = require('../commands/utility/items.js');
                        const rarityData = RARITIES[item.rarity];
                        weight = rarityData?.weight || 1;
                    } catch (error) {
                        weight = 1;
                    }
                } else if (item.rarity?.weight) {
                    weight = item.rarity.weight;
                }

                console.log(`   - ${item.name} (${scope}, Rarity: ${item.rarity}, Weight: ${weight})`);
            });

            // Test global context
            const globalLevelUpItems = await getDroppableItems('LEVEL_UP', null);
            console.log(`   Global LEVEL_UP items available: ${globalLevelUpItems.length}`);

            // Test starfall items
            const starfallItems = await getDroppableItems('STARFALL', null);
            console.log(`   Global STARFALL items available: ${starfallItems.length}`);

            return guildTextItems.length > 0;
        } catch (error) {
            console.error('   Item drop system error:', error.message);
            return false;
        }
    }

    async testRecentGlobalActivity() {
        try {
            const { mongoClient } = require('../mongo/client.js');
            const db = mongoClient.db('seventeen_bot');
            
            console.log('🔍 Checking recent global activity...');
            
            // Check recent global level-ups
            const recentLevelUps = await db.collection('global_level_notifications').find({})
                .sort({ createdAt: -1 })
                .limit(5)
                .toArray();
                
            console.log(`   Recent global level-ups: ${recentLevelUps.length}`);
            recentLevelUps.forEach(levelUp => {
                const hasItems = levelUp.levelUpData?.levelRewards?.items?.length > 0;
                console.log(`   User: ${levelUp.userId}, Level: ${levelUp.levelUpData?.newLevel}, Items: ${hasItems ? 'YES' : 'NO'}`);
            });
            
            // Check recent item notifications
            const recentItemNotifications = await db.collection('item_notifications_queue').find({
                guildId: null // Global items only
            }).sort({ createdAt: -1 }).limit(5).toArray();
            
            console.log(`   Recent global item notifications: ${recentItemNotifications.length}`);
            recentItemNotifications.forEach(notification => {
                console.log(`   User: ${notification.userId}, Items: ${notification.items?.length || 0}, Location: ${notification.location}`);
            });
            
            return true;
        } catch (error) {
            console.error('   Recent activity check error:', error.message);
            return false;
        }
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Global items database', test: () => this.testGlobalItemsDatabase() },
            { name: 'Global level system', test: () => this.testGlobalLevelSystem() },
            { name: 'Item drop system', test: () => this.testItemDropSystem() },
            { name: 'Recent global activity', test: () => this.testRecentGlobalActivity() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
        return this.results.passed === this.results.total;
    }
}

async function runGlobalItemsDebugTest() {
    const tester = new GlobalItemsDebugTester();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runGlobalItemsDebugTest();
}

module.exports = { GlobalItemsDebugTester };
