#!/usr/bin/env node

/**
 * Comprehensive Deep-Dive Test Suite Runner
 * 
 * This script runs the comprehensive deep-dive test suite that validates
 * every Discord bot feature interaction at the granular level.
 * 
 * Usage: node run-comprehensive-deep-dive-tests.js
 * 
 * Success Criteria:
 * - 100% success rate (no failures allowed)
 * - All interactions complete within 3-second timeout
 * - Minimum 20 tests executed
 * - All features tested at granular level
 */

const path = require('path');
const fs = require('fs');

// Ensure we're in the correct directory
const projectRoot = __dirname;
process.chdir(projectRoot);

// Load environment variables
require('dotenv').config();

// Validate required environment variables
const requiredEnvVars = ['TOKEN', 'MONGO', 'OWNER', 'CLIENTID', 'GUILDIDTWO'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(varName => {
        console.error(`   - ${varName}`);
    });
    console.error('\nPlease ensure your .env file contains all required variables.');
    process.exit(1);
}

// Set MONGO for compatibility with test suite
if (process.env.MONGO && !process.env.MONGO) {
    process.env.MONGO = process.env.MONGO;
}

// Import the test suite
const ComprehensiveDeepDiveTestSuite = require('./tests/comprehensive-deep-dive-test-suite.js');

/**
 * Main test runner function
 */
async function runTests() {
    console.log('🚀 Comprehensive Deep-Dive Discord Bot Test Suite');
    console.log('='.repeat(60));
    console.log('Testing every feature interaction at granular level...\n');

    // Display test configuration
    console.log('📋 TEST CONFIGURATION:');
    console.log(`   Discord Token: ${process.env.TOKEN ? '✅ Configured' : '❌ Missing'}`);
    console.log(`   MongoDB URI: ${process.env.MONGO ? '✅ Configured' : '❌ Missing'}`);
    console.log(`   Bot Owner ID: ${process.env.OWNER || 'Not set'}`);
    console.log(`   Client ID: ${process.env.CLIENTID || 'Not set'}`);
    console.log(`   Test Guild ID: ${process.env.GUILDIDTWO || 'Not set'}`);
    console.log(`   Test Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`   Project Root: ${projectRoot}`);
    console.log('');

    // Display success criteria
    console.log('🎯 SUCCESS CRITERIA:');
    console.log('   ✅ 100% success rate (no failures allowed)');
    console.log('   ✅ All interactions complete within 3-second timeout');
    console.log('   ✅ All database operations complete successfully');
    console.log('   ✅ All permission checks function correctly');
    console.log('   ✅ All form validations work as expected');
    console.log('   ✅ All UI components render and respond properly');
    console.log('   ✅ Minimum 20 tests executed across all features');
    console.log('');

    // Display feature coverage
    console.log('📦 FEATURE COVERAGE:');
    console.log('   🎁 Items System: Create, edit, delete, configuration, permissions');
    console.log('   📊 Levels System: Guild levels, global levels, rewards, notifications');
    console.log('   🏷️  Sticky Roles: Role management, nickname preservation');
    console.log('   📝 Logging System: Channel configuration, event selection, toggles');
    console.log('   👤 User Settings: Profile, inventory, daily rewards, notifications');
    console.log('   ⚙️  Administrative: Feature toggles, permission boundaries, owner panel');
    console.log('');

    // Display test depth
    console.log('🔍 TEST DEPTH:');
    console.log('   📝 Modal form submissions (valid and invalid inputs)');
    console.log('   📋 Select menu options (including edge cases and restrictions)');
    console.log('   🔘 Button interactions (including disabled states)');
    console.log('   ✅ Form validation logic (required fields, limits, formats)');
    console.log('   🔒 Permission boundaries (owner vs user vs no permissions)');
    console.log('   💾 Database operations (CRUD for each feature)');
    console.log('   🎨 UI state management (pre-population, dynamic updates)');
    console.log('');

    try {
        // Create and run the test suite
        const testSuite = new ComprehensiveDeepDiveTestSuite();
        await testSuite.runAllTests();
        
    } catch (error) {
        console.error('❌ Test suite execution failed:');
        console.error(error);
        
        // Additional error context
        if (error.code === 'ECONNREFUSED') {
            console.error('\n💡 Possible solutions:');
            console.error('   - Ensure MongoDB is running and accessible');
            console.error('   - Check MONGO in .env file');
            console.error('   - Verify network connectivity');
        } else if (error.message.includes('permission')) {
            console.error('\n💡 Possible solutions:');
            console.error('   - Check OWNER environment variable');
            console.error('   - Verify bot permissions in test guild');
            console.error('   - Ensure test user has required roles');
        }
        
        process.exit(1);
    }
}

/**
 * Handle process signals for graceful shutdown
 */
process.on('SIGINT', () => {
    console.log('\n⏹️  Test suite interrupted by user');
    process.exit(130);
});

process.on('SIGTERM', () => {
    console.log('\n⏹️  Test suite terminated');
    process.exit(143);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run the tests
if (require.main === module) {
    runTests().catch(error => {
        console.error('❌ Fatal error in test runner:', error);
        process.exit(1);
    });
}

module.exports = { runTests };
