const { PermissionFlagsBits } = require("discord.js");

module.exports = {
	// To view more events, visit: https://discord.js.org/#/docs/discord.js/main/typedef/Events
	// To see how events are logged, check events/messageUpdate or events/messageDelete for an example
	// Core Discord events (21 events - under 25-option limit, redundant events removed)
	events: [
		// Message events (not covered by audit log)
		"messageDelete",
		"messageUpdate",
		"messageDeleteBulk",

		// Member events (user join/leave not covered by audit log)
		"guildMemberAdd",
		"guildMemberRemove",

		// Voice events (real-time activity not covered by audit log)
		"voiceStateUpdate",

		// Thread events (member activity not covered by audit log)
		"threadMemberUpdate",

		// Event/scheduling events (user interest not covered by audit log)
		"guildScheduledEventUserAdd",
		"guildScheduledEventUserRemove",

		// Server events (bot join/leave not covered by audit log)
		"guildCreate",
		"guildDelete",

		// Audit log events (comprehensive coverage)
		"guildAuditLogEntryCreate",
		"applicationCommandPermissionsUpdate",
		"autoModerationActionExecution"
	],

	// Specialty feature events (6 unified groups - much cleaner UX)
	specialtyEvents: [
		"featureManagement",    // featureEnabled, featureDisabled
		"expSystem",           // expLevelUp, expVoiceSession, expLevelCreated, expLevelEdited, expLevelDeleted
		"threadOpener",        // openerThreadWatched, openerThreadBumped, openerThreadUnwatched
		"dehoistSystem",       // dehoistUsername, dehoistScanCompleted
		"stickySystem",        // stickyNicknameRecovered, stickyRolesRecovered
		"itemSystem"           // itemCreated, itemUpdated, itemDeleted, itemDisabled, itemEnabled, itemDropped
	],

	// Mapping of unified specialty events to their individual events
	specialtyEventMappings: {
		"featureManagement": ["featureEnabled", "featureDisabled"],
		"expSystem": ["expLevelUp", "expVoiceSession", "expLevelCreated", "expLevelEdited", "expLevelDeleted"],
		"threadOpener": ["openerThreadWatched", "openerThreadBumped", "openerThreadUnwatched"],
		"dehoistSystem": ["dehoistUsername", "dehoistScanCompleted"],
		"stickySystem": ["stickyNicknameRecovered", "stickyRolesRecovered"],
		"itemSystem": ["itemCreated", "itemUpdated", "itemDeleted", "itemDisabled", "itemEnabled", "itemDropped"]
	},

	// Owner-only events (only visible to bot owner)
	ownerEvents: [
		"botConsole",
		"botJoinedServer",
		"botLeftServer",
		"ownerItemEvents"
	],

	// Permissions that will be set for the commands
	permissions: {
		dehoist: PermissionFlagsBits.Administrator,
		logs: PermissionFlagsBits.Administrator,
		sticky: PermissionFlagsBits.Administrator
		// Note: p, welcome, frnd commands removed - no longer exist
	}
}

