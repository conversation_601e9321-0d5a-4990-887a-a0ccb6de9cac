const { ContainerB<PERSON>er, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize } = require('discord.js');
const { OPERATION_COLORS, LOG_COLORS } = require('../../utils/colors.js');

/**
 * Owner Servers Management Module
 * Extracted from owner.js to reduce file size and improve maintainability
 * Handles server listing and management functionality
 */

/**
 * Build servers container showing all guilds the bot is in
 * @param {Object} client - Discord client
 * @returns {ContainerBuilder} Servers container
 */
async function buildServersContainer(client) {
    // Get all guilds and sort by join date (oldest first)
    const guilds = client.guilds.cache
        .map(g => ({
            id: g.id,
            name: g.name,
            memberCount: g.memberCount,
            joinedAt: g.joinedAt || g.joinedTimestamp || 0
        }))
        .sort((a, b) => b.joinedAt - a.joinedAt); // sort by newest first

    const total = guilds.length;
    const totalMembers = guilds.reduce((sum, g) => sum + g.memberCount, 0);

    const lines = guilds.map((g, i) => {
        const num = total - i; // 9 → 1
        const date = g.joinedAt ? `<t:${Math.floor(g.joinedAt / 1000)}:d>` : 'unknown';
        return `${num}\\. ${date} **${g.name}** ${g.memberCount} members`;
    });

    // Server/member count display
    const countDisplay = new TextDisplayBuilder().setContent(`${total} server${total === 1 ? '' : 's'}, ${totalMembers.toLocaleString()} members`);

    // Split into multiple text displays if needed
    const displays = [];
    let current = '';
    for (const line of lines) {
        if ((current + line + '\n').length > 4000) {
            displays.push(new TextDisplayBuilder().setContent(current));
            current = '';
        }
        current += line + '\n';
    }
    if (current) {
        displays.push(new TextDisplayBuilder().setContent(current));
    }

    // Back button section
    const backButton = new ButtonBuilder()
        .setCustomId('owner-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(new TextDisplayBuilder().setContent('# servers'))
        .setButtonAccessory(backButton);

    const description = new TextDisplayBuilder().setContent('> who r u?');

    // Compose the container
    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(description, countDisplay, ...displays)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;

}

/**
 * Get server statistics
 * @param {Object} client - Discord client
 * @returns {Object} Server statistics
 */
function getServerStats(client) {
    try {
        const guilds = client.guilds.cache;
        const totalServers = guilds.size;
        const totalMembers = guilds.reduce((acc, guild) => acc + guild.memberCount, 0);
        
        // Calculate average members per server
        const avgMembers = totalServers > 0 ? Math.round(totalMembers / totalServers) : 0;
        
        // Find largest and smallest servers
        const guildArray = Array.from(guilds.values());
        const largestServer = guildArray.reduce((max, guild) => 
            guild.memberCount > max.memberCount ? guild : max, 
            guildArray[0] || { name: 'None', memberCount: 0 }
        );
        
        const smallestServer = guildArray.reduce((min, guild) => 
            guild.memberCount < min.memberCount ? guild : min, 
            guildArray[0] || { name: 'None', memberCount: 0 }
        );

        return {
            totalServers,
            totalMembers,
            avgMembers,
            largestServer: {
                name: largestServer.name,
                memberCount: largestServer.memberCount
            },
            smallestServer: {
                name: smallestServer.name,
                memberCount: smallestServer.memberCount
            }
        };

    } catch (error) {
        console.error('[owner-servers] Error getting server stats:', error);
        return {
            totalServers: 0,
            totalMembers: 0,
            avgMembers: 0,
            largestServer: { name: 'Error', memberCount: 0 },
            smallestServer: { name: 'Error', memberCount: 0 }
        };
    }
}

/**
 * Build detailed server statistics container
 * @param {Object} client - Discord client
 * @returns {ContainerBuilder} Server statistics container
 */
async function buildServerStatsContainer(client) {
    // Back button section
    const backSection = new SectionBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('owner-servers-back')
                .setLabel('back')
                .setStyle(ButtonStyle.Secondary)
        );

    const heading = new TextDisplayBuilder().setContent('# server statistics');
    const info = new TextDisplayBuilder().setContent('> detailed server analytics');

    try {
        const stats = getServerStats(client);
        
        const statsText = `**overview:**\n` +
            `• Total servers: ${stats.totalServers}\n` +
            `• Total members: ${stats.totalMembers.toLocaleString()}\n` +
            `• Average members per server: ${stats.avgMembers}\n\n` +
            `**extremes:**\n` +
            `• Largest: **${stats.largestServer.name}** (${stats.largestServer.memberCount} members)\n` +
            `• Smallest: **${stats.smallestServer.name}** (${stats.smallestServer.memberCount} members)`;

        const statsDisplay = new TextDisplayBuilder().setContent(statsText);

        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(heading, info)
            .addSeparatorComponents(separator)
            .addTextDisplayComponents(statsDisplay)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        return container;

    } catch (error) {
        console.error('[owner-servers] Error building stats container:', error);
        
        const errorDisplay = new TextDisplayBuilder().setContent('**Error loading statistics**\nPlease try again.');
        
        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(heading, info, errorDisplay)
            .setAccentColor(LOG_COLORS.ERROR);

        return container;
    }
}

module.exports = {
    buildServersContainer,
    buildServerStatsContainer,
    getServerStats
};
