/**
 * Create Test Global Items for Starfall and Level-Up Testing
 * This script creates sample global items to test the server name display fix
 */

require('dotenv').config();
const { optimizedInsertOne } = require('../utils/database-optimizer.js');

async function createTestGlobalItems() {
    console.log('🎁 Creating test global items for starfall and level-up testing...');

    try {
        // Test Starfall Item
        const starfallItem = {
            id: `item_${Date.now()}_starfall_test`,
            name: 'Cosmic Memory Fragment',
            description: 'A shimmering fragment of cosmic memory, found only during starfall events. Contains traces of ancient wisdom.',
            type: 'JUNK',
            rarity: {
                name: 'RARE',
                weight: 10000,
                color: 0x3498db
            },
            emote: '🌌',
            dropLocations: ['STARFALL'],
            guildId: null, // Global item
            disabled: false,
            parameters: {
                mysticalPower: {
                    min: 50,
                    max: 100,
                    decimals: 1
                },
                stardustContent: {
                    min: 10,
                    max: 50,
                    decimals: 0
                }
            },
            createdAt: new Date(),
            globalOrder: 1000,
            guildOrder: null
        };

        // Test Level-Up Item
        const levelUpItem = {
            id: `item_${Date.now()}_levelup_test`,
            name: 'Experience Crystal',
            description: 'A crystallized form of pure experience energy. Glows with the accumulated wisdom of countless adventures.',
            type: 'TOOL',
            rarity: {
                name: 'EPIC',
                weight: 5000,
                color: 0x9b59b6
            },
            emote: '💎',
            dropLocations: ['LEVEL_UP'],
            guildId: null, // Global item
            disabled: false,
            parameters: {
                experienceBonus: {
                    min: 100,
                    max: 500,
                    decimals: 0
                },
                purity: {
                    min: 80,
                    max: 100,
                    decimals: 1
                }
            },
            createdAt: new Date(),
            globalOrder: 1001,
            guildOrder: null
        };

        // Another Starfall Item for variety
        const starfallItem2 = {
            id: `item_${Date.now()}_starfall_test2`,
            name: 'Stellar Compass',
            description: 'An ancient navigation tool that points toward distant stars. Its needle trembles with cosmic energy.',
            type: 'TOOL',
            rarity: {
                name: 'LEGENDARY',
                weight: 2000,
                color: 0xf39c12
            },
            emote: '🧭',
            dropLocations: ['STARFALL'],
            guildId: null, // Global item
            disabled: false,
            parameters: {
                accuracy: {
                    min: 90,
                    max: 100,
                    decimals: 2
                },
                magneticField: {
                    min: 1,
                    max: 10,
                    decimals: 1
                }
            },
            createdAt: new Date(),
            globalOrder: 1002,
            guildOrder: null
        };

        // Insert all items
        await optimizedInsertOne('custom_items', starfallItem);
        console.log(`✅ Created starfall item: ${starfallItem.name} (${starfallItem.emote})`);

        await optimizedInsertOne('custom_items', levelUpItem);
        console.log(`✅ Created level-up item: ${levelUpItem.name} (${levelUpItem.emote})`);

        await optimizedInsertOne('custom_items', starfallItem2);
        console.log(`✅ Created starfall item: ${starfallItem2.name} (${starfallItem2.emote})`);

        console.log('\n🎉 Test global items created successfully!');
        console.log('\nNow you can:');
        console.log('1. Claim starfall to see: "You found a 🌌 **Cosmic Memory Fragment** in **19 testing**, dropped from starfall:"');
        console.log('2. Level up globally to see: "You found a 💎 **Experience Crystal** in **19 testing**, dropped from level up:"');
        console.log('\nBoth should now show the correct server name instead of "Server"!');

    } catch (error) {
        console.error('❌ Error creating test items:', error);
    }
}

// Run if called directly
if (require.main === module) {
    createTestGlobalItems().then(() => {
        console.log('✅ Script completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Script failed:', error);
        process.exit(1);
    });
}

module.exports = { createTestGlobalItems };
