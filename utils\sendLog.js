const { EmbedBuilder, MessageFlags } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedBulkWrite } = require("./database-optimizer.js");
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * Send Log System (Enterprise-Grade Performance Optimized)
 * Handles all bot logging with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const sendLogMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    logsProcessed: 0,
    logsSent: 0,
    parallelOperations: 0,
    channelFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildLogConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild log configurations
const channelCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for Discord channel objects
const logTemplateCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for log templates

// Register caches for global cleanup
registerCache(guildLogConfigCache);
registerCache(channelCache);
registerCache(logTemplateCache);

/**
 * Send a specialty log to configured channels
 * @param {string} guildId - Guild ID
 * @param {string} eventType - Event type (must match config.js events)
 * @param {Object} logData - Log data object
 * @param {string} logData.title - Embed title
 * @param {string} logData.description - Embed description
 * @param {string} [logData.color] - Embed color (hex without #)
 * @param {Object} [logData.fields] - Additional embed fields
 * @param {Object} [client] - Discord client (for fetching channels)
 */
/**
 * Get cached guild log configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Guild log configuration
 */
async function getCachedGuildLogConfig(guildId) {
    const startTime = Date.now();

    // OPTIMIZED: Check LRU cache first (automatic TTL and size management)
    const cached = guildLogConfigCache.get(guildId);
    if (cached !== undefined) {
        sendLogMetrics.cacheHits++;
        if (sendLogMetrics.verboseLogging) {
            console.log(`[sendLog] ⚡ Guild log config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    try {
        sendLogMetrics.cacheMisses++;
        sendLogMetrics.databaseQueries++;

        const guildData = await optimizedFindOne("guilds",
            { id: guildId },
            { projection: { logs: 1 } } // Only fetch logs configuration
        );

        const config = guildData?.logs || null;

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        guildLogConfigCache.set(guildId, config);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        sendLogMetrics.averageQueryTime =
            (sendLogMetrics.averageQueryTime * (sendLogMetrics.databaseQueries - 1) + duration) /
            sendLogMetrics.databaseQueries;

        if (sendLogMetrics.verboseLogging || duration > 50) {
            console.log(`[sendLog] ✅ Guild log config fetched for ${guildId} (${duration}ms) - cached for future access`);
        }

        return config;
    } catch (error) {
        console.error(`[sendLog] ❌ Error getting guild config for ${guildId}:`, error);
        return null;
    }
}

/**
 * Get cached Discord channel objects (Enterprise-Grade Optimized)
 * OPTIMIZED: LRU caching for Discord channel objects to reduce API calls
 * @param {Array} channelConfigs - Array of channel configurations
 * @param {Object} client - Discord client
 * @returns {Promise<Array>} Array of valid Discord channel objects
 */
async function getCachedChannels(channelConfigs, client) {
    const startTime = Date.now();
    const channels = [];
    const uncachedChannels = [];

    // Check cache for each channel
    for (const channelConfig of channelConfigs) {
        const cacheKey = `channel_${channelConfig.id}`;
        const cached = channelCache.get(cacheKey);

        if (cached) {
            sendLogMetrics.cacheHits++;
            if (cached.isValid) {
                channels.push(cached.channel);
            }
        } else {
            uncachedChannels.push(channelConfig);
        }
    }

    // Fetch uncached channels in parallel
    if (uncachedChannels.length > 0) {
        sendLogMetrics.cacheMisses += uncachedChannels.length;

        const channelPromises = uncachedChannels.map(async (channelConfig) => {
            try {
                let channel = null;
                if (client) {
                    channel = client.channels.cache.get(channelConfig.id);
                } else if (global.discordClient) {
                    channel = global.discordClient.channels.cache.get(channelConfig.id);
                }

                const cacheKey = `channel_${channelConfig.id}`;
                if (channel && channel.isTextBased()) {
                    // Cache valid channel
                    channelCache.set(cacheKey, { channel, isValid: true });
                    return channel;
                } else {
                    // Cache invalid channel to prevent repeated lookups
                    channelCache.set(cacheKey, { channel: null, isValid: false });
                    return null;
                }
            } catch (error) {
                if (sendLogMetrics.verboseLogging) {
                    console.error(`[sendLog] Error getting channel ${channelConfig.id}:`, error.message);
                }
                // Cache error result
                const cacheKey = `channel_${channelConfig.id}`;
                channelCache.set(cacheKey, { channel: null, isValid: false });
                return null;
            }
        });

        const fetchedChannels = await Promise.allSettled(channelPromises);
        const validChannels = fetchedChannels
            .filter(result => result.status === 'fulfilled' && result.value)
            .map(result => result.value);

        channels.push(...validChannels);
    }

    const duration = Date.now() - startTime;
    if (sendLogMetrics.verboseLogging || duration > 100) {
        console.log(`[sendLog] ✅ Channels fetched: ${channels.length}/${channelConfigs.length} valid (${duration}ms)`);
    }

    return channels;
}

/**
 * Invalidate guild log configuration cache (Enhanced)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 * @param {string} guildId - Guild ID
 */
function invalidateGuildLogConfig(guildId) {
    guildLogConfigCache.delete(guildId);

    // FIXED: Use correct LRU cache methods instead of Map methods
    // Clear related channel cache entries for this guild
    const channelKeys = channelCache.getKeysByAccessTime().filter(key => key.startsWith(`channel_${guildId}_`));
    channelKeys.forEach(key => channelCache.delete(key));

    if (sendLogMetrics.verboseLogging) {
        console.log(`[sendLog] 🗑️ Invalidated log config cache for guild ${guildId} (${channelKeys.length} channel entries cleared)`);
    }
}

/**
 * Send a specialty log to configured channels (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing, enhanced caching, and comprehensive performance monitoring
 * @param {string} guildId - Guild ID
 * @param {string} eventType - Event type (must match config.js events)
 * @param {Object} logData - Log data object
 * @param {string} logData.title - Embed title
 * @param {string} logData.description - Embed description
 * @param {string} [logData.color] - Embed color (hex without #)
 * @param {Object} [logData.fields] - Additional embed fields
 * @param {Object} [client] - Discord client (for fetching channels)
 */
async function sendLog(guildId, eventType, logData, client = null) {
    const startTime = Date.now();
    sendLogMetrics.logsProcessed++;

    try {
        // OPTIMIZED: Get guild data using enhanced cached function
        const guildLogConfig = await getCachedGuildLogConfig(guildId);

        if (!guildLogConfig || !guildLogConfig.enabled) {
            return; // Logs disabled
        }

        // Find channels configured for this event (including unified specialty events)
        const configuredChannels = guildLogConfig.channels
            ?.filter(l => l.events.includes(eventType)) || [];

        if (configuredChannels.length === 0) {
            return; // No channels configured for this event
        }

        // OPTIMIZED: Get Discord channel objects using cached function
        const channels = await getCachedChannels(configuredChannels, client);

        if (channels.length === 0) {
            return; // No valid channels found
        }

        // OPTIMIZED: Create embed with template caching
        const embedCacheKey = `embed_${eventType}_${JSON.stringify(logData)}`;
        let embed = logTemplateCache.get(embedCacheKey);

        if (!embed) {
            embed = new EmbedBuilder()
                .setTitle(logData.title)
                .setColor(logData.color || "5865F2")
                .setDescription(logData.description)
                .setTimestamp();

            // Add additional fields if provided
            if (logData.fields) {
                for (const [name, value] of Object.entries(logData.fields)) {
                    embed.addFields({ name, value: String(value), inline: true });
                }
            }

            // Cache the embed template (without timestamp for reusability)
            logTemplateCache.set(embedCacheKey, embed);
        }

        // OPTIMIZED: Send to all configured channels in parallel with enhanced error handling
        sendLogMetrics.parallelOperations++;
        const sendPromises = channels.map(async (channel) => {
            try {
                await channel.send({
                    embeds: [embed],
                    allowedMentions: { parse: [] } // Prevent pings but show mentions visually
                });
                sendLogMetrics.logsSent++;
                return { success: true, channelId: channel.id };
            } catch (err) {
                sendLogMetrics.channelFailures++;
                if (sendLogMetrics.verboseLogging) {
                    console.error(`[sendLog] Failed to send log to channel ${channel.id}:`, err.message);
                }
                return { success: false, channelId: channel.id, error: err.message };
            }
        });

        const results = await Promise.allSettled(sendPromises);
        const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

        const duration = Date.now() - startTime;
        if (sendLogMetrics.verboseLogging || duration > 200) {
            console.log(`[sendLog] ✅ Sent ${eventType} log to ${successCount}/${channels.length} channel(s) in guild ${guildId} (${duration}ms)`);
        }

    } catch (error) {
        console.error(`[sendLog] ❌ Error sending log for ${eventType}:`, error);
    }
}

/**
 * Send a Components v2 log container to configured channels (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching, parallel processing, and comprehensive error handling
 * @param {string} guildId - Guild ID
 * @param {string} eventType - Event type (must match config.js events)
 * @param {ContainerBuilder} container - The container to send
 * @param {Object} [client] - Discord client (for fetching channels)
 * @param {ActionRowBuilder[]} [additionalRows] - Additional action rows to include
 */
async function sendLogContainer(guildId, eventType, container, client = null, additionalRows = []) {
    const startTime = Date.now();
    sendLogMetrics.logsProcessed++;

    try {
        // OPTIMIZED: Use cached guild log configuration
        const guildLogConfig = await getCachedGuildLogConfig(guildId);

        if (!guildLogConfig || !guildLogConfig.enabled) {
            return; // Logs disabled
        }

        // Find channels configured for this event
        const configuredChannels = guildLogConfig.channels
            ?.filter(l => l.events.includes(eventType)) || [];

        if (configuredChannels.length === 0) {
            return; // No channels configured for this event
        }

        // OPTIMIZED: Get Discord channel objects using cached function
        const channels = await getCachedChannels(configuredChannels, client);

        if (channels.length === 0) {
            return; // No valid channels found
        }

        // Build components array with container and additional rows as separate components
        const components = [container];
        if (additionalRows.length > 0) {
            // Add additional rows as separate components (outside the container)
            for (const row of additionalRows) {
                components.push(row);
            }
        }

        // OPTIMIZED: Send to all configured channels in parallel using Components v2
        sendLogMetrics.parallelOperations++;
        const { sendToLogChannel } = require('../commands/utility/logs.js');

        const sendPromises = channels.map(async (channel) => {
            try {
                const messageOptions = {
                    flags: MessageFlags.IsComponentsV2,
                    components: components,
                    allowedMentions: { parse: [] } // Prevent pings but show mentions visually
                };

                // Add file attachment if container has one
                if (container._attachmentData) {
                    const { AttachmentBuilder } = require('discord.js');

                    // Create AttachmentBuilder (filename already cleaned in container)
                    const attachment = new AttachmentBuilder(container._attachmentData.data, {
                        name: container._attachmentData.name,
                        description: container._attachmentData.description
                    });
                    messageOptions.files = [attachment];

                    if (sendLogMetrics.verboseLogging) {
                        console.log(`[sendLogContainer] Created attachment: ${container._attachmentData.name}`);
                    }
                }

                await sendToLogChannel(channel, messageOptions, eventType);
                sendLogMetrics.logsSent++;
                return { success: true, channelId: channel.id };
            } catch (err) {
                sendLogMetrics.channelFailures++;
                if (sendLogMetrics.verboseLogging) {
                    console.error(`[sendLogContainer] Failed to send container to channel ${channel.id}:`, err.message);
                }
                return { success: false, channelId: channel.id, error: err.message };
            }
        });

        const results = await Promise.allSettled(sendPromises);
        const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

        const duration = Date.now() - startTime;
        if (sendLogMetrics.verboseLogging || duration > 200) {
            console.log(`[sendLogContainer] ✅ Sent ${eventType} container to ${successCount}/${channels.length} channel(s) in guild ${guildId} (${duration}ms)`);
        }

    } catch (error) {
        console.error(`[sendLogContainer] ❌ Error sending container for ${eventType}:`, error);
    }
}

/**
 * Send feature enable/disable log
 * @param {string} guildId - Guild ID
 * @param {string} featureName - Name of the feature
 * @param {string} subcomponent - Subcomponent name (optional)
 * @param {boolean} enabled - Whether feature was enabled or disabled
 * @param {string} userId - User who triggered the action
 * @param {Object} [client] - Discord client
 */
async function sendFeatureToggleLog(guildId, featureName, subcomponent, enabled, userId, client = null) {
    const { createFeatureToggleContainer } = require('./logContainers.js');

    const eventType = enabled ? 'featureEnabled' : 'featureDisabled';
    const action = enabled ? 'enabled' : 'disabled';

    const container = createFeatureToggleContainer({
        action: action,
        featureName: featureName,
        subcomponent: subcomponent,
        userMention: `<@${userId}>`
    });

    await sendLogContainer(guildId, eventType, container, client);
}

/**
 * Send EXP level up log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who leveled up
 * @param {number} newLevel - New level reached
 * @param {number} totalExp - Total EXP
 * @param {string} roleId - Role ID assigned (if any)
 * @param {Object} [client] - Discord client
 */
async function sendExpLevelUpLog(guildId, userId, newLevel, totalExp, roleId = null, client = null) {
    const { createExpLevelUpContainer } = require('./logContainers.js');

    const container = createExpLevelUpContainer({
        userMention: `<@${userId}>`,
        level: newLevel,
        totalExp: totalExp,
        roleMention: roleId ? `<@&${roleId}>` : null
    });

    await sendLogContainer(guildId, 'expLevelUp', container, client);
}

/**
 * Send EXP voice session log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who gained EXP
 * @param {number} expGained - EXP gained from voice session
 * @param {number} sessionDuration - Session duration in minutes
 * @param {Object} [client] - Discord client
 * @param {number} [sessionDurationMs] - Session duration in milliseconds (for better formatting)
 */
async function sendExpVoiceSessionLog(guildId, userId, expGained, sessionDuration, client = null, sessionDurationMs = null) {
    const { createExpVoiceSessionContainer } = require('./logContainers.js');

    const container = createExpVoiceSessionContainer({
        userMention: `<@${userId}>`,
        expGained: expGained,
        sessionDuration: sessionDuration,
        sessionDurationMs: sessionDurationMs
    });

    await sendLogContainer(guildId, 'expVoiceSession', container, client);
}

/**
 * Send EXP level created log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who created the level
 * @param {number} levelNumber - Level number created
 * @param {string} roleId - Role ID for the level
 * @param {number} expRequired - EXP required for the level
 * @param {Object} [client] - Discord client
 */
async function sendExpLevelCreatedLog(guildId, userId, levelNumber, roleId, expRequired, client = null) {
    const { createExpLevelCreatedContainer } = require('./logContainers.js');

    const container = createExpLevelCreatedContainer({
        adminMention: `<@${userId}>`,
        level: levelNumber,
        roleMention: `<@&${roleId}>`,
        expRequired: expRequired
    });

    await sendLogContainer(guildId, 'expLevelCreated', container, client);
}

/**
 * Send EXP level edited log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who edited the level
 * @param {number} levelNumber - Level number edited
 * @param {Object} changes - Object containing old and new values
 * @param {Object} [client] - Discord client
 */
async function sendExpLevelEditedLog(guildId, userId, levelNumber, changes, client = null) {
    const { createExpLevelEditedContainer } = require('./logContainers.js');

    const container = createExpLevelEditedContainer({
        adminMention: `<@${userId}>`,
        level: levelNumber,
        changes: changes
    });

    await sendLogContainer(guildId, 'expLevelEdited', container, client);
}

/**
 * Send dehoist username log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who was dehoisted
 * @param {string} oldName - Original display name
 * @param {string} newName - New display name
 * @param {Object} [client] - Discord client
 */
async function sendDehoistUsernameLog(guildId, userId, oldName, newName, client = null) {
    const { createDehoistUsernameContainer } = require('./logContainers.js');

    const container = createDehoistUsernameContainer({
        userMention: `<@${userId}>`,
        beforeName: oldName,
        afterName: newName
    });

    await sendLogContainer(guildId, 'dehoistUsername', container, client);
}

/**
 * Send dehoist scan completed log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who initiated the scan
 * @param {number} totalProcessed - Total members processed
 * @param {number} dehoisted - Number of members dehoisted
 * @param {number} failed - Number of failed attempts
 * @param {Object} [client] - Discord client
 */
async function sendDehoistScanCompletedLog(guildId, userId, totalProcessed, dehoisted, failed, client = null) {
    const { createDehoistScanContainer } = require('./logContainers.js');

    const container = createDehoistScanContainer({
        adminMention: `<@${userId}>`,
        processed: totalProcessed,
        dehoisted: dehoisted,
        failed: failed
    });

    await sendLogContainer(guildId, 'dehoistScanCompleted', container, client);
}

/**
 * Send opener thread watched log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who added the thread to tracking
 * @param {string} threadId - Thread ID that was watched
 * @param {string} threadName - Thread name
 * @param {Object} [client] - Discord client
 */
async function sendOpenerThreadWatchedLog(guildId, userId, threadId, threadName, client = null) {
    const { createOpenerThreadContainer } = require('./logContainers.js');

    const container = createOpenerThreadContainer({
        action: 'watched',
        threadMention: `<#${threadId}>`,
        threadName: threadName,
        adminMention: `<@${userId}>`
    });

    await sendLogContainer(guildId, 'openerThreadWatched', container, client);
}

/**
 * Send opener thread bumped log
 * @param {string} guildId - Guild ID
 * @param {string} threadId - Thread ID that was bumped
 * @param {string} threadName - Thread name
 * @param {string} reason - Reason for bump (e.g., "auto-reopened", "refreshed")
 * @param {Object} [client] - Discord client
 */
async function sendOpenerThreadBumpedLog(guildId, threadId, threadName, reason, client = null) {
    const { createOpenerThreadContainer } = require('./logContainers.js');

    const container = createOpenerThreadContainer({
        action: 'bumped',
        threadMention: `<#${threadId}>`,
        threadName: threadName,
        reason: reason
    });

    await sendLogContainer(guildId, 'openerThreadBumped', container, client);
}

/**
 * Send opener thread unwatched log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who removed the thread from tracking
 * @param {string} threadId - Thread ID that was unwatched
 * @param {string} threadName - Thread name
 * @param {Object} [client] - Discord client
 */
async function sendOpenerThreadUnwatchedLog(guildId, userId, threadId, threadName, client = null) {
    const { createOpenerThreadContainer } = require('./logContainers.js');

    const container = createOpenerThreadContainer({
        action: 'unwatched',
        threadMention: `<#${threadId}>`,
        threadName: threadName,
        adminMention: `<@${userId}>`
    });

    await sendLogContainer(guildId, 'openerThreadUnwatched', container, client);
}

/**
 * Send sticky nickname recovered log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User whose nickname was recovered
 * @param {string} nickname - Recovered nickname
 * @param {Object} [client] - Discord client
 */
async function sendStickyNicknameRecoveredLog(guildId, userId, nickname, client = null) {
    const { createStickyRecoveryContainer } = require('./logContainers.js');

    const container = createStickyRecoveryContainer({
        type: 'nickname',
        userMention: `<@${userId}>`,
        content: nickname
    });

    await sendLogContainer(guildId, 'stickyNicknameRecovered', container, client);
}

/**
 * Send sticky roles recovered log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User whose roles were recovered
 * @param {string[]} roleIds - Array of recovered role IDs
 * @param {Object} [client] - Discord client
 */
async function sendStickyRolesRecoveredLog(guildId, userId, roleIds, client = null) {
    const { createStickyRecoveryContainer } = require('./logContainers.js');

    const rolesMention = roleIds.map(id => `<@&${id}>`).join(' ');
    const container = createStickyRecoveryContainer({
        type: 'roles',
        userMention: `<@${userId}>`,
        content: rolesMention
    });

    await sendLogContainer(guildId, 'stickyRolesRecovered', container, client);
}

/**
 * Send owner item management log (for bot-wide items)
 * @param {string} action - Action performed
 * @param {string} ownerId - Bot owner ID
 * @param {Object} itemData - Item data
 * @param {Object} [changes] - Changes made (for updates)
 * @param {Object} [client] - Discord client
 */
async function sendOwnerItemManagementLog(action, ownerId, itemData, changes = null, client = null) {
    try {
        // Get owner logging configuration from a special collection or environment
        // For now, we'll use a simple approach - log to owner's DM or a special owner log channel

        const { createItemManagementContainer } = require('./logContainers.js');

        // Get drop location names
        const { DROP_LOCATIONS } = require('../commands/utility/items.js');
        const dropLocationNames = (itemData.dropLocations || []).map(loc => {
            const location = DROP_LOCATIONS[loc];
            return location ? location.name : loc;
        });

        const container = createItemManagementContainer({
            action: action,
            adminMention: `<@${ownerId}>`,
            itemName: itemData.name,
            itemEmote: itemData.emote,
            itemType: itemData.type,
            itemRarity: itemData.rarity,
            dropLocations: dropLocationNames,
            changes: changes
        });

        // For now, just log to console with a special prefix for owner operations
        // This can be extended later to send to owner-configured channels using 'ownerItemEvents'
        console.log(`[OWNER ITEM LOG] ${action.toUpperCase()}: Item "${itemData.name}" by owner ${ownerId}`);

        // TODO: Implement owner-specific logging channels when owner logging system is built
        // This would use the 'ownerItemEvents' event type for all item operations
        // await sendLogContainer(ownerGuildId, 'ownerItemEvents', container, client);

    } catch (error) {
        console.error(`[sendOwnerItemManagementLog] Error sending owner item log for ${action}:`, error);
    }
}

/**
 * Send item management log (create/update/delete/disable/enable)
 * @param {string} guildId - Guild ID
 * @param {string} action - Action performed
 * @param {string} adminId - Admin who performed the action
 * @param {Object} itemData - Item data
 * @param {Object} [changes] - Changes made (for updates)
 * @param {Object} [client] - Discord client
 */
async function sendItemManagementLog(guildId, action, adminId, itemData, changes = null, client = null) {
    const { createItemManagementContainer } = require('./logContainers.js');

    // Get drop location names
    let dropLocationNames = [];
    try {
        const { DROP_LOCATIONS } = require('../commands/utility/items.js');
        if (DROP_LOCATIONS) {
            dropLocationNames = (itemData.dropLocations || []).map(loc => {
                const location = DROP_LOCATIONS[loc];
                return location ? location.name.toLowerCase() : loc.toLowerCase();
            });
        } else {
            throw new Error('DROP_LOCATIONS not available');
        }
    } catch (error) {
        console.error('Error loading DROP_LOCATIONS:', error);
        // Fallback to simple mapping
        const locationMap = {
            'TEXT': 'text exp',
            'VOICE': 'voice exp',
            'LEVEL_UP': 'level up'
        };
        dropLocationNames = (itemData.dropLocations || []).map(loc =>
            locationMap[loc] || loc.toLowerCase()
        );
    }

    const container = createItemManagementContainer({
        action: action,
        adminMention: `<@${adminId}>`,
        itemName: itemData.name,
        itemEmote: itemData.emote,
        itemType: itemData.type,
        itemRarity: itemData.rarity,
        dropLocations: dropLocationNames,
        changes: changes
    });

    const eventMap = {
        'created': 'itemCreated',
        'updated': 'itemUpdated',
        'deleted': 'itemDeleted',
        'disabled': 'itemDisabled',
        'enabled': 'itemEnabled'
    };

    await sendLogContainer(guildId, eventMap[action], container, client);
}

/**
 * Send item drop log
 * @param {string} guildId - Guild ID
 * @param {string} userId - User who received the item
 * @param {Object} itemData - Item data
 * @param {string} dropLocation - Where the item dropped from
 * @param {number} expGained - EXP gained that triggered the drop
 * @param {boolean} [isFirstInServer] - Whether this is the first time this item dropped in the server
 * @param {Object} [client] - Discord client
 */
async function sendItemDropLog(guildId, userId, itemData, dropLocation, expGained, isFirstInServer = false, client = null) {
    const { createItemDropContainer } = require('./logContainers.js');

    // Get drop location name
    let dropLocationName = dropLocation.toLowerCase();
    try {
        const { DROP_LOCATIONS } = require('../commands/utility/items.js');
        if (DROP_LOCATIONS && DROP_LOCATIONS[dropLocation]) {
            dropLocationName = DROP_LOCATIONS[dropLocation].name.toLowerCase();
        }
    } catch (error) {
        console.error('Error loading DROP_LOCATIONS:', error);
        // Fallback to simple mapping
        const locationMap = {
            'TEXT': 'text exp',
            'VOICE': 'voice exp',
            'LEVEL_UP': 'level up'
        };
        dropLocationName = locationMap[dropLocation] || dropLocation.toLowerCase();
    }

    const container = createItemDropContainer({
        userMention: `<@${userId}>`,
        itemName: itemData.itemName || itemData.name,
        itemEmote: itemData.itemEmote || itemData.emote,
        itemRarity: itemData.itemRarity || itemData.rarity,
        dropLocation: dropLocationName,
        expGained: expGained,
        isFirstInServer: isFirstInServer
    });

    await sendLogContainer(guildId, 'itemDropped', container, client);
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = sendLogMetrics.cacheHits + sendLogMetrics.cacheMisses > 0 ?
        (sendLogMetrics.cacheHits / (sendLogMetrics.cacheHits + sendLogMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: sendLogMetrics.cacheHits,
            cacheMisses: sendLogMetrics.cacheMisses,
            databaseQueries: sendLogMetrics.databaseQueries,
            averageQueryTime: `${sendLogMetrics.averageQueryTime.toFixed(2)}ms`,
            logsProcessed: sendLogMetrics.logsProcessed,
            logsSent: sendLogMetrics.logsSent,
            parallelOperations: sendLogMetrics.parallelOperations,
            channelFailures: sendLogMetrics.channelFailures,
            successRate: sendLogMetrics.logsProcessed > 0 ?
                `${((sendLogMetrics.logsSent / sendLogMetrics.logsProcessed) * 100).toFixed(2)}%` : '0%',
            lastOptimization: new Date(sendLogMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildLogConfig: guildLogConfigCache.getStats(),
            channels: channelCache.getStats(),
            logTemplates: logTemplateCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildLogConfig: guildLogConfigCache.getStats().memoryUsage,
            channels: channelCache.getStats().memoryUsage,
            logTemplates: logTemplateCache.getStats().memoryUsage,
            total: guildLogConfigCache.getStats().memoryUsage +
                   channelCache.getStats().memoryUsage +
                   logTemplateCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, sendLogMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 100) {
        recommendations.push('Database queries are slow - consider adding indexes or optimizing queries');
    }

    if (metrics.channelFailures > metrics.logsSent * 0.1) {
        recommendations.push('High channel failure rate - investigate Discord API issues or permissions');
    }

    if (metrics.logsProcessed > 0 && (metrics.logsSent / metrics.logsProcessed) < 0.8) {
        recommendations.push('Low log success rate - investigate error patterns and channel configurations');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    sendLogMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[sendLog] 📊 Performance Report:`);
    console.log(`[sendLog]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[sendLog]   Logs Processed: ${stats.performance.logsProcessed}`);
    console.log(`[sendLog]   Success Rate: ${stats.performance.successRate}`);
    console.log(`[sendLog]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[sendLog]   Channel Failures: ${stats.performance.channelFailures}`);
    console.log(`[sendLog]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[sendLog]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[sendLog]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[sendLog] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all send log caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllSendLogCaches() {
    guildLogConfigCache.clear();
    channelCache.clear();
    logTemplateCache.clear();

    console.log('[sendLog] 🗑️ Cleared all send log caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, sendLogMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    sendLog,
    sendLogContainer,
    sendFeatureToggleLog,
    sendExpLevelUpLog,
    sendExpVoiceSessionLog,
    sendExpLevelCreatedLog,
    sendExpLevelEditedLog,
    sendDehoistUsernameLog,
    sendDehoistScanCompletedLog,
    sendOpenerThreadWatchedLog,
    sendOpenerThreadBumpedLog,
    sendOpenerThreadUnwatchedLog,
    sendStickyNicknameRecoveredLog,
    sendStickyRolesRecoveredLog,
    sendItemManagementLog,
    sendOwnerItemManagementLog,
    sendItemDropLog,

    // Enhanced optimization functions
    getCachedGuildLogConfig,
    getCachedChannels,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllSendLogCaches,
    invalidateGuildLogConfig,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...sendLogMetrics })
};
