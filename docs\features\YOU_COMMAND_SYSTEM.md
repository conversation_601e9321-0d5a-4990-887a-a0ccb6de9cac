# 👤 /you Command System - Comprehensive Documentation

## Overview
The /you command provides a comprehensive user profile interface displaying EXP, levels, items, starfall rewards, and transcription features. It serves as the primary user-facing dashboard for personal statistics and bot interaction.

## Core Architecture

### Main Files
- `commands/utility/you.js` - Primary user profile interface
- `commands/utility/transcribe-voice.js` - Voice transcription context menu
- `utils/transcriptionHandlers.js` - Transcription processing logic
- `utils/globalLevels.js` - Global EXP system integration
- `utils/starfall.js` - Daily rewards system

### Database Collections
- `user_exp` - User EXP tracking per guild
- `global_levels` - Cross-server EXP data
- `user_inventories` - Personal item collections
- `starfall_data` - Daily reward tracking
- `user_settings` - Personal preferences and configuration

## Key Functions

### Primary Interface Functions

#### `execute(interaction)`
**Purpose**: Main slash command handler for user profiles
**Parameters**:
- `interaction` - Discord slash command interaction
- `user` option - Optional user to view (defaults to command invoker)

**Behavior**:
- Displays comprehensive user profile with multiple sections
- Supports viewing other users' profiles
- Integrates data from multiple systems (EXP, items, starfall)
- Provides navigation to detailed sections

**Performance Optimization**:
```javascript
// Parallel data fetching for improved response times
const [expData, globalData, itemsData, starfallData, settingsData] = await Promise.all([
    getOptimizedGuildExpData(guildId),
    getGlobalLevelData(userId),
    getUserInventory(userId, guildId),
    getStarfallData(userId),
    getUserSettings(userId)
]);
```

#### `select(interaction, selectPath)`
**Purpose**: Handles navigation within user profile sections
**Available Sections**:
- `exp` - EXP and leveling information
- `items` - Personal inventory and item management
- `starfall` - Daily rewards and streak tracking
- `notifications` - Notification preferences
- `settings` - Personal configuration options

**Navigation Flow**:
```
you-hub-select → ['exp'] → EXP details with level progression
you-hub-select → ['items'] → Inventory with item details
you-hub-select → ['starfall'] → Daily rewards interface
you-hub-select → ['settings'] → Personal preferences
```

#### `buttons(interaction)`
**Purpose**: Handles action buttons within profile sections
**Key Actions**:
- `you-claim-starfall` - Claim daily starfall reward
- `you-toggle-notifications` - Enable/disable notifications
- `you-refresh-data` - Refresh profile data
- `you-export-data` - Export personal data (GDPR compliance)

#### `modalSubmit(interaction)`
**Purpose**: Processes settings and preference forms
**Modal Types**:
- `you-settings-modal` - Personal preferences
- `you-notification-modal` - Notification configuration
- `you-privacy-modal` - Privacy settings

## Special Behaviors

### Multi-Section Interface
**Design Pattern**: Single command with multiple specialized views
**Implementation**:
- Main hub with section navigation
- Persistent navigation between sections
- Context-aware back button behavior
- Section-specific action buttons

**Section State Management**:
```javascript
// Maintain section context across interactions
const sectionData = {
    currentSection: 'exp',
    previousSection: 'hub',
    userData: cachedUserData,
    navigationPath: ['hub', 'exp']
};
```

### Transcription Integration
**Purpose**: Voice message transcription using Python and OpenAI Whisper
**Components**:
- Context menu command for voice messages
- Python service integration
- Transcription result delivery
- Error handling for service unavailability

**Transcription Flow**:
```javascript
// Voice message processing
const audioUrl = message.attachments.first().url;
const transcription = await processVoiceMessage(audioUrl);
await deliverTranscription(interaction, transcription);
```

**Error Handling**:
```javascript
try {
    const result = await transcribeAudio(audioFile);
    return result;
} catch (error) {
    if (error.code === 'PYTHON_UNAVAILABLE') {
        return 'Transcription service temporarily unavailable';
    }
    throw error;
}
```

### Global vs Guild Data Integration
**Challenge**: Seamlessly display both server-specific and global statistics
**Solution**: Parallel data fetching with intelligent merging

**Data Merging Logic**:
```javascript
function mergeUserData(guildData, globalData) {
    return {
        guildExp: guildData.exp || 0,
        guildLevel: calculateLevel(guildData.exp),
        globalExp: globalData.totalExp || 0,
        globalRank: globalData.rank || 'Unranked',
        combinedStats: {
            totalMessages: guildData.messages + globalData.messages,
            totalVoiceTime: guildData.voiceTime + globalData.voiceTime
        }
    };
}
```

### Demo Mode Support
**Purpose**: Provide meaningful interface when user has no data
**Implementation**:
- Detect empty user data
- Display example statistics with clear labeling
- Encourage user engagement with bot features

**Demo Data Display**:
```javascript
if (!hasUserData(userId)) {
    const demoData = {
        exp: 150,
        level: 3,
        items: ['Demo Sword', 'Demo Shield'],
        starfall: { streak: 0, lastClaim: null }
    };
    return buildDemoProfile(demoData);
}
```

## Internal Logic Details

### Performance Optimization
**Caching Strategy**:
- User data cached for 2 minutes
- Global data cached for 5 minutes
- Inventory data cached for 1 minute
- Settings cached for 10 minutes

**Database Query Optimization**:
```javascript
// Optimized user data retrieval
const userData = await userCol.findOne(
    { userId, guildId },
    { 
        projection: { 
            exp: 1, 
            level: 1, 
            lastActivity: 1,
            settings: 1 
        } 
    }
);
```

**Parallel Processing**:
```javascript
// Concurrent data fetching
const dataPromises = [
    getExpData(userId, guildId),
    getGlobalData(userId),
    getInventoryData(userId, guildId),
    getStarfallData(userId)
];

const [expData, globalData, inventoryData, starfallData] = 
    await Promise.allSettled(dataPromises);
```

### Error Recovery Patterns
**Graceful Degradation**:
```javascript
// Handle partial data failures
const profileData = {
    exp: expData.status === 'fulfilled' ? expData.value : null,
    global: globalData.status === 'fulfilled' ? globalData.value : null,
    items: inventoryData.status === 'fulfilled' ? inventoryData.value : [],
    starfall: starfallData.status === 'fulfilled' ? starfallData.value : null
};

// Display available data with error indicators
return buildPartialProfile(profileData);
```

### Settings Management
**User Preferences Structure**:
```javascript
{
    userId: String,
    notifications: {
        levelUp: Boolean,
        itemDrops: Boolean,
        starfall: Boolean,
        mentions: Boolean
    },
    privacy: {
        showProfile: Boolean,
        showInventory: Boolean,
        showStats: Boolean
    },
    display: {
        theme: String,
        language: String,
        timezone: String
    }
}
```

## Testing Requirements

### Unit Tests
1. **Profile Data Display**
   ```javascript
   async testProfileDisplay() {
       const interaction = this.createMockInteraction(2, 'you');
       interaction.options = {
           getUser: () => null // Self-profile
       };
       
       await you.execute(interaction);
       
       // Validate profile data accuracy
       assert(interaction._responses.length > 0);
       // Check for required profile sections
   }
   ```

2. **Section Navigation**
   ```javascript
   async testSectionNavigation() {
       const interaction = this.createMockInteraction(5, 'you-hub-select', ['exp']);
       await you.select(interaction, []);
       
       // Validate EXP section display
       // Check navigation consistency
   }
   ```

3. **Settings Management**
   ```javascript
   async testSettingsUpdate() {
       const modalInteraction = this.createMockInteraction(6, 'you-settings-modal', {
           'notification-level-up': 'true',
           'privacy-show-profile': 'false'
       });
       
       await you.modalSubmit(modalInteraction);
       
       // Validate settings persistence
   }
   ```

### Integration Tests
1. **Transcription System**
   - Voice message processing
   - Python service integration
   - Error handling for service failures

2. **Multi-System Data Integration**
   - EXP system integration
   - Items system integration
   - Starfall system integration
   - Global levels integration

3. **Performance Validation**
   - Response time benchmarks
   - Concurrent user handling
   - Cache effectiveness

### Performance Tests
1. **Response Time Benchmarks**
   - Profile loading: <200ms
   - Section navigation: <100ms
   - Settings updates: <300ms

2. **Data Integration Performance**
   - Parallel data fetching efficiency
   - Cache hit rate optimization
   - Database query performance

## Error Handling Patterns

### Data Retrieval Failures
```javascript
async function safeDataRetrieval(dataFetcher, fallbackValue) {
    try {
        return await dataFetcher();
    } catch (error) {
        console.warn('[you] Data retrieval failed:', error.message);
        return fallbackValue;
    }
}
```

### Transcription Service Errors
```javascript
async function handleTranscriptionRequest(audioUrl) {
    try {
        const transcription = await transcribeAudio(audioUrl);
        return buildTranscriptionResponse(transcription);
    } catch (error) {
        if (error.code === 'SERVICE_UNAVAILABLE') {
            return buildServiceUnavailableResponse();
        }
        return buildGenericErrorResponse();
    }
}
```

### Settings Update Failures
```javascript
async function updateUserSettings(userId, newSettings) {
    try {
        await settingsCol.updateOne(
            { userId },
            { $set: newSettings },
            { upsert: true }
        );
        
        await invalidateUserCache(userId);
        return buildSuccessResponse();
    } catch (error) {
        console.error('[you] Settings update failed:', error);
        return buildErrorResponse('Failed to save settings');
    }
}
```

## Maintenance Considerations

### Regular Tasks
1. **Performance Monitoring**
   - Profile loading times
   - Data integration efficiency
   - Cache hit rates
   - Error frequency analysis

2. **Transcription Service Health**
   - Python service availability
   - Whisper model performance
   - Audio processing success rates

3. **User Data Integrity**
   - Cross-system data consistency
   - Settings validation
   - Privacy compliance

### Scaling Considerations
- User data sharding for large user bases
- Transcription service load balancing
- Cache distribution for high-traffic scenarios
- Database query optimization for concurrent access

## Development Guidelines

### Code Patterns
- Use parallel data fetching for performance
- Implement graceful degradation for partial failures
- Maintain consistent navigation patterns
- Follow Components v2 compliance standards

### Testing Standards
- Test both self-profile and other-user profile views
- Validate all section navigation paths
- Include transcription service testing (when available)
- Performance test with realistic data volumes

### Documentation Updates
- Document new profile sections when added
- Maintain transcription service integration notes
- Update performance benchmarks
- Keep testing examples current with implementation
