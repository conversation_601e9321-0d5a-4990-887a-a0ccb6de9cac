const { Container<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, RoleSelectMenuBuilder } = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { defaults } = require("../../utils/default_db_structures.js");
const config = require("../../config.js");
const { buildSelectMenu } = require('./featuresMenu');
const { sendFeatureToggleLog } = require("../../utils/sendLog.js");
const { getStickyDemoData } = require("../../utils/demoData.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');
const { getFeatureStatus } = require('../../utils/permissionHandler.js');

// Helper function to determine if guild has real sticky data (not just defaults)
function hasRealStickyData(sticky) {
    if (!sticky) return false;

    // Check if there are sticky roles configured
    if (sticky.roles && sticky.roles.length > 0) return true;

    // Check if sticky nick is enabled
    if (sticky.nick === true) return true;

    return false; // No real data, just defaults
}

function buildStickyContainer({ sticky, guild, roles, enabled = true, hasPermission = true, member = null, commandChannel = null, statusMessage = null }) {
    // Determine if we should show demo data
    const shouldShowDemo = !hasPermission || (!enabled && !hasRealStickyData(sticky));
    const isShowingDemo = shouldShowDemo && guild && member;

    if (isShowingDemo) {
        sticky = getStickyDemoData(guild, member, commandChannel);
        // Don't change enabled state - keep it as is for proper select menu disabling
    }
    const heading = new TextDisplayBuilder().setContent('# sticky');
    const description = new TextDisplayBuilder().setContent('> give back roles and nicknames when a member rejoins');
    // Handle role display with fallback for demo data
    let rolesText;
    if (sticky.roles.length === 0) {
        rolesText = '`none`';
    } else if (isShowingDemo) {
        // For demo data, use fake role names from demo data system
        const { getAccessibleRoles } = require('../../utils/demoData.js');
        const demoRoles = getAccessibleRoles(guild);
        rolesText = sticky.roles.map(roleId => {
            const demoRole = demoRoles.find(role => role.id === roleId);
            return demoRole ? `@${demoRole.name}` : '@Sticky';
        }).join(' ');
    } else {
        // For real data, use Discord role mention format
        rolesText = sticky.roles.map(id => `<@&${id}>`).join(' ');
    }

    const status = new TextDisplayBuilder().setContent(`**nicknames:** ${sticky.nick ? 'sticky' : 'not sticky'}\n**sticky role(s):** ${rolesText}`);

    const nickSelect = new StringSelectMenuBuilder()
        .setCustomId('sticky-nick-select')
        .setPlaceholder('nickname')
        .setDisabled(!enabled || !hasPermission || isShowingDemo) // Disable when showing demo data
        .addOptions([
            {
                label: sticky.nick ? 'disable' : 'enable',
                value: sticky.nick ? 'disable' : 'enable',
                description: sticky.nick ? 'Currently enabled' : 'Currently disabled',
                default: false
            }
        ]);
    const nickRow = new ActionRowBuilder().addComponents(nickSelect);

    let roleRow = null;
    if (roles && roles.size > 0) {
        const validRoleIds = sticky.roles.filter(id => roles.has(id));
        const roleSelect = new RoleSelectMenuBuilder()
            .setCustomId('sticky-role-select')
            .setPlaceholder('role(s)')
            .setMinValues(0)
            .setMaxValues(roles.size)
            .setDefaultRoles(...validRoleIds.map(String))
            .setDisabled(!enabled || !hasPermission || isShowingDemo); // Disable when showing demo data

        roleRow = new ActionRowBuilder().addComponents(roleSelect);
    }

    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, description, status)
        .addSeparatorComponents(separator)
        .addActionRowComponents(nickRow);
    if (roleRow) container.addActionRowComponents(roleRow);

    // Check for roles higher than bot and create persistent status message
    let persistentStatusMessage = statusMessage; // Keep any temporary status message
    if (guild && sticky.roles.length > 0 && hasPermission) {
        const { getCachedRoleValidation } = require('../../utils/stickyCache.js');
        const validation = getCachedRoleValidation(guild.id, guild);

        const botRole = guild.members.me?.roles?.highest;
        const invalidRoles = [];

        sticky.roles.forEach(roleId => {
            const roleData = validation.validRoles.get(roleId);
            if (roleData && !roleData.valid) {
                // CRITICAL FIX: Suppress warning when bot tries to assign its own role
                // This is a Discord API limitation - bots cannot assign their own role
                if (botRole && roleId === botRole.id) {
                    // Skip adding bot's own role to invalid roles list
                    // This prevents confusing "@Bot Role is higher than @Bot Role" messages
                    return;
                }
                invalidRoles.push(`<@&${roleId}>`);
            }
        });

        if (invalidRoles.length > 0) {
            const verb = invalidRoles.length === 1 ? 'is' : 'are';
            const botRoleMention = botRole ? `<@&${botRole.id}>` : 'bot role';
            persistentStatusMessage = `${invalidRoles.join(' ')} ${verb} higher than ${botRoleMention} and won't be sticky upon rejoin`;
        }
    }

    // Add status message at the bottom if present
    if (persistentStatusMessage) {
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${persistentStatusMessage}`);
        container.addTextDisplayComponents(statusDisplay);
    }

    container.setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    buildStickyContainer,
    hasRealStickyData,
    // data: new SlashCommandBuilder()
    //     .setName("sticky")
    //     // .setDMPermission(false)
    //     .setDescription("sticky stuff")
    //     .setDefaultMemberPermissions(config.permissions.sticky),
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'sticky');



        // Use cached sticky configuration for better performance
        const { getCachedGuildStickyConfig } = require('../../utils/stickyCache.js');
        let sticky = await getCachedGuildStickyConfig(interaction.guild.id);

        // Ensure the configuration exists in database if it's using defaults
        if (!sticky.enabled && sticky.roles.length === 0 && !sticky.nick) {
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });

            if (!guildData) {
                await optimizedInsertOne("guilds", defaults.guild(interaction.guild.id));
                sticky = { enabled: false, roles: [], nick: null }; // Use consistent default
            }
        }



        const roles = interaction.guild.roles.cache;
        const container = buildStickyContainer({
            sticky,
            guild: interaction.guild,
            roles,
            enabled: sticky.enabled,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel
        });

        // Get permission-aware toggle button
        const featureStatus = getFeatureStatus(
            interaction.guild,
            interaction.member,
            'sticky',
            sticky.enabled,
            sticky.enabled ? 'sticky-disable' : 'sticky-enable'
        );

        // Add status message to container if there are permission issues
        if (featureStatus.statusMessage) {
            container.addTextDisplayComponents(featureStatus.statusMessage);
        }

        const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
        return [selectMenu, container, new ActionRowBuilder().addComponents(featureStatus.button)];
        }, {
            autoDefer: true, // Auto-defer for execute function as it may be slow
            ephemeral: true,
            fallbackMessage: '❌ Something went wrong loading the sticky interface. Please try again.'
        });
    },
    async select(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            try {
            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'sticky');

            if (!hasPermission) {
            // Show demo mode if no permission
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky: {}, // Will be replaced by demo data
                guild: interaction.guild,
                roles,
                enabled: true,
                hasPermission: false,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('sticky-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            return [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)];
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        let sticky = guildData?.sticky || { roles: [], nick: null, enabled: false }; // Use consistent default

        // Check if sticky is enabled for this guild
        if (!sticky.enabled) {
            // Feature is disabled, don't process the selection
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky,
                guild: interaction.guild,
                roles,
                enabled: false,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const enableButton = new ButtonBuilder()
                .setCustomId('sticky-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
        }



        if (interaction.customId === 'sticky-nick-select') {
            const value = interaction.values[0];
            sticky.nick = value === 'enable';
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.nick': sticky.nick } });

            // Invalidate cache after update
            const { invalidateGuildStickyConfig } = require('../../utils/stickyCache.js');
            invalidateGuildStickyConfig(interaction.guild.id);
        } else if (interaction.customId === 'sticky-role-select') {
            const roles = interaction.values;
            // Save all selected roles (no filtering)
            sticky.roles = roles;

            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.roles': sticky.roles } });

            // Invalidate cache after update
            const { invalidateGuildStickyConfig } = require('../../utils/stickyCache.js');
            invalidateGuildStickyConfig(interaction.guild.id);

            // Note: New sticky roles will be captured by lazy sync as users become active
        }

        // Rebuild UI
        const rolesCache = interaction.guild.roles.cache;
        const validRoleIds = sticky.roles.filter(id => rolesCache.has(id));

        const container = buildStickyContainer({
            sticky,
            guild: interaction.guild,
            roles: rolesCache,
            enabled: sticky.enabled,
            member: interaction.member,
            commandChannel: interaction.channel
        });
        const toggleButton = new ButtonBuilder()
            .setCustomId(sticky.enabled ? 'sticky-disable' : 'sticky-enable')
            .setLabel(sticky.enabled ? 'disable' : 'enable')
            .setStyle(sticky.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
        try {
            return [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)];
        } catch (err) {
            console.error('[sticky.select] Error updating interaction:', err);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    // Try to rebuild basic sticky container with error status message
                    const { mongoClient } = require('../../mongo/client.js');
                    let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                    if (!guildData) guildData = { sticky: { enabled: false, roles: [] } }; // Use consistent default
                    if (!guildData.sticky) guildData.sticky = { enabled: false, roles: [] }; // Use consistent default

                    const container = buildStickyContainer({
                        sticky: guildData.sticky,
                        guild: interaction.guild,
                        roles: interaction.guild.roles.cache,
                        enabled: guildData.sticky.enabled,
                        member: interaction.member,
                        commandChannel: interaction.channel,
                        statusMessage: 'there was an error updating the sticky UI.'
                    });
                    const toggleButton = new ButtonBuilder()
                        .setCustomId(guildData.sticky.enabled ? 'sticky-disable' : 'sticky-enable')
                        .setLabel(guildData.sticky.enabled ? 'disable' : 'enable')
                        .setStyle(guildData.sticky.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');

                    return [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)];
                } catch (fallbackErr) {
                    console.error('[sticky.select] Fallback error:', fallbackErr);
                    // Return error container instead of direct reply
                    return new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** there was an error updating the sticky UI.'))
                        .setAccentColor(OPERATION_COLORS.DELETE);
                }
            }
        }
        } catch (mainError) {
            console.error('[sticky.select] MAIN ERROR:', mainError);
            // Return error container instead of conditional reply
            return new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** An error occurred processing your selection.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
        }
        }, {
            autoDefer: false, // Don't auto-defer for select menus - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your selection. Please try again.'
        });
    },
    async buttons(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Check permissions for all button actions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'sticky');
        if (!hasPermission) {
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky: {}, // Will be replaced by demo data
                guild: interaction.guild,
                roles,
                enabled: true,
                hasPermission: false,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('sticky-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            return [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)];
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        let sticky = guildData.sticky || { roles: [], nick: null, enabled: false }; // Use consistent default
        
        if (interaction.customId === 'sticky-disable') {
            sticky.enabled = false;
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.enabled': false } });

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Sticky',
                null,
                false,
                interaction.user.id,
                interaction.client
            );
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky,
                guild: interaction.guild,
                roles,
                enabled: sticky.enabled,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const enableButton = new ButtonBuilder()
                .setCustomId('sticky-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
        } else if (interaction.customId === 'sticky-enable') {
            sticky.enabled = true;
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.enabled': true } });

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Sticky',
                null,
                true,
                interaction.user.id,
                interaction.client
            );
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky,
                guild: interaction.guild,
                roles,
                enabled: sticky.enabled,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('sticky-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            return [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)];
        }
        }, {
            autoDefer: false, // Don't auto-defer for button presses - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your button press. Please try again.'
        });
    },


};