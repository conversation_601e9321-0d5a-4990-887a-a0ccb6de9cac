const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class HybridSystemTester extends BotTestBase {
    constructor() {
        super('HybridSystemComprehensiveTest');
    }

    async testHybridSystemCoreProcessing() {
        try {
            const { processItemDrops } = require('../utils/itemDropsHybrid.js');
            
            // Test with mock guild and user
            const testUserId = process.env.OWNER;
            const testGuildId = process.env.GUILDIDTWO;
            
            console.log('🧪 Testing hybrid system core processing...');
            
            // Test TEXT location drops
            const textDrops = await processItemDrops(testUserId, testGuildId, 'TEXT', 10, this.client);
            console.log(`   TEXT drops result: ${textDrops.length} items`);
            
            // Test VOICE location drops  
            const voiceDrops = await processItemDrops(testUserId, testGuildId, 'VOICE', 15, this.client);
            console.log(`   VOICE drops result: ${voiceDrops.length} items`);
            
            return true;
        } catch (error) {
            console.error('❌ Hybrid system core processing test failed:', error);
            return false;
        }
    }

    async testServerNameDisplayForGlobalItems() {
        try {
            console.log('🧪 Testing server name display for global items...');

            // Test starfall item processing
            const { processStarfallItemDrop } = require('../utils/starfall.js');
            const testUserId = process.env.OWNER;
            const testGuildId = process.env.GUILDIDTWO;

            // This should now pass the guild ID correctly for server name display
            const starfallResult = await processStarfallItemDrop(testUserId, testGuildId, this.client);
            console.log(`   Starfall result: ${starfallResult.success ? 'SUCCESS' : 'NO_DROP'}`);

            if (starfallResult.success) {
                console.log(`   Item: ${starfallResult.item.itemName} (${starfallResult.item.itemEmote})`);
                console.log(`   📱 DM sent with server name: "${this.testGuild.name}"`);
                console.log(`   Expected format: "You found a ${starfallResult.item.itemEmote} **${starfallResult.item.itemName}** in **${this.testGuild.name}**, dropped from starfall:"`);
            }

            // Test global level-up notifications with actual item
            const { processGlobalLevelUp } = require('../utils/globalLevelNotifications.js');

            // Create a mock level-up with an actual global item
            const { mongoClient } = require('../mongo/client.js');
            const db = mongoClient.db('seventeen_bot');
            const globalLevelUpItem = await db.collection('custom_items').findOne({
                guildId: null,
                dropLocations: 'LEVEL_UP',
                disabled: { $ne: true }
            });

            if (globalLevelUpItem) {
                const mockLevelUpData = {
                    newLevel: 5,
                    oldLevel: 4,
                    levelRewards: {
                        items: [{
                            itemId: globalLevelUpItem.id,
                            itemName: globalLevelUpItem.name,
                            itemEmote: globalLevelUpItem.emote,
                            itemType: globalLevelUpItem.type,
                            itemRarity: globalLevelUpItem.rarity,
                            itemDescription: globalLevelUpItem.description,
                            guildId: null,
                            droppedAt: new Date(),
                            droppedFrom: 'LEVEL_UP'
                        }]
                    }
                };

                // This should now accept and use the guild context
                await processGlobalLevelUp(testUserId, mockLevelUpData, this.client, testGuildId);
                console.log(`   Global level-up item: ${globalLevelUpItem.name} (${globalLevelUpItem.emote})`);
                console.log(`   📱 DM sent with server name: "${this.testGuild.name}"`);
                console.log(`   Expected format: "You found a ${globalLevelUpItem.emote} **${globalLevelUpItem.name}** in **${this.testGuild.name}**, dropped from level up:"`);
            } else {
                console.log('   No global level-up items found - testing without item rewards');
                const mockLevelUpData = {
                    newLevel: 5,
                    oldLevel: 4,
                    levelRewards: { items: [] }
                };
                await processGlobalLevelUp(testUserId, mockLevelUpData, this.client, testGuildId);
            }

            console.log('   Global level-up processing: SUCCESS');

            return true;
        } catch (error) {
            console.error('❌ Server name display test failed:', error);
            return false;
        }
    }

    async testSeparationLogic() {
        try {
            console.log('🧪 Testing global vs guild item separation logic...');
            
            const { processItemNotifications } = require('../utils/itemDropsHybrid.js');
            const testUserId = process.env.OWNER;
            const testGuildId = process.env.GUILDIDTWO;
            
            // Mock global item (guildId: null)
            const mockGlobalItem = {
                guildId: null,
                itemName: 'Test Global Item',
                itemEmote: '🧪',
                itemType: 'test',
                itemRarity: { name: 'Common', weight: 100000 },
                droppedAt: new Date()
            };
            
            // Mock guild item (guildId: specific)
            const mockGuildItem = {
                guildId: testGuildId,
                itemName: 'Test Guild Item', 
                itemEmote: '🏰',
                itemType: 'test',
                itemRarity: { name: 'Common', weight: 100000 },
                droppedAt: new Date()
            };
            
            // Test global item processing (should only go to DMs + notification center)
            await processItemNotifications(testUserId, testGuildId, mockGlobalItem, 'TEST', this.client);
            console.log('   Global item processing: SUCCESS');
            
            // Test guild item processing (should go to all channels)
            await processItemNotifications(testUserId, testGuildId, mockGuildItem, 'TEST', this.client);
            console.log('   Guild item processing: SUCCESS');
            
            return true;
        } catch (error) {
            console.error('❌ Separation logic test failed:', error);
            return false;
        }
    }

    async testPerformanceAndCaching() {
        try {
            console.log('🧪 Testing performance and caching systems...');
            
            const { getCacheStats, clearAllItemDropCaches } = require('../utils/itemDropsHybrid.js');
            
            // Get initial cache stats
            const initialStats = getCacheStats();
            console.log(`   Initial cache hit rate: ${initialStats.performance.cacheHitRate}`);
            console.log(`   Items processed: ${initialStats.performance.itemsProcessed}`);
            console.log(`   Global items: ${initialStats.performance.globalItemsProcessed}`);
            console.log(`   Guild items: ${initialStats.performance.guildItemsProcessed}`);
            
            // Test cache clearing
            clearAllItemDropCaches();
            console.log('   Cache clearing: SUCCESS');
            
            // Test cache stats after clearing
            const clearedStats = getCacheStats();
            console.log(`   Cache hit rate after clear: ${clearedStats.performance.cacheHitRate}`);
            
            return true;
        } catch (error) {
            console.error('❌ Performance and caching test failed:', error);
            return false;
        }
    }

    async testErrorHandling() {
        try {
            console.log('🧪 Testing error handling and graceful degradation...');
            
            const { processItemDrops } = require('../utils/itemDropsHybrid.js');
            
            // Test with invalid parameters
            const invalidResult1 = await processItemDrops(null, null, 'INVALID', 0, null);
            console.log(`   Invalid parameters result: ${invalidResult1.length} items (expected: 0)`);
            
            // Test with non-existent location
            const invalidResult2 = await processItemDrops(process.env.OWNER, process.env.GUILDIDTWO, 'NONEXISTENT', 10, this.client);
            console.log(`   Non-existent location result: ${invalidResult2.length} items (expected: 0)`);
            
            return true;
        } catch (error) {
            console.error('❌ Error handling test failed:', error);
            return false;
        }
    }

    async testDatabaseQueryConsistency() {
        try {
            console.log('🧪 Testing database query consistency...');
            
            const { mongoClient } = require('../mongo/client.js');
            const db = mongoClient.db('seventeen_bot');
            
            // Test that items have correct field structure
            const sampleItems = await db.collection('custom_items').find({
                disabled: { $ne: true }
            }).limit(5).toArray();
            
            console.log(`   Found ${sampleItems.length} sample items`);
            
            for (const item of sampleItems) {
                // Verify required fields exist
                if (!item.dropLocations) {
                    console.warn(`   ⚠️ Item ${item.name} missing dropLocations field`);
                }
                if (item.guildId !== null && typeof item.guildId !== 'string') {
                    console.warn(`   ⚠️ Item ${item.name} has invalid guildId: ${item.guildId}`);
                }
            }
            
            console.log('   Database query consistency: SUCCESS');
            return true;
        } catch (error) {
            console.error('❌ Database query consistency test failed:', error);
            return false;
        }
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Hybrid system core processing', test: () => this.testHybridSystemCoreProcessing() },
            { name: 'Server name display for global items', test: () => this.testServerNameDisplayForGlobalItems() },
            { name: 'Global vs guild separation logic', test: () => this.testSeparationLogic() },
            { name: 'Performance and caching systems', test: () => this.testPerformanceAndCaching() },
            { name: 'Error handling and graceful degradation', test: () => this.testErrorHandling() },
            { name: 'Database query consistency', test: () => this.testDatabaseQueryConsistency() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        return passed === total;
    }
}

// Main execution function
async function runHybridSystemTest() {
    const tester = new HybridSystemTester();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        
        const passed = tester.testResults.filter(r => r.passed).length;
        const total = tester.testResults.length;

        console.log('\n🎯 HYBRID SYSTEM TEST SUMMARY:');
        console.log(`   Tests Passed: ${passed}/${total}`);
        console.log(`   Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (allPassed) {
            console.log('✅ All hybrid system tests passed! System is ready for production.');
        } else {
            console.log('❌ Some tests failed. Review issues before production deployment.');
        }
        
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ Hybrid system test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runHybridSystemTest();
}

module.exports = { HybridSystemTester };
