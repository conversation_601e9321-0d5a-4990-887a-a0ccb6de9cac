/**
 * Permission-Based Feature Disabling Test
 * 
 * This test verifies that the permission-based feature disabling system
 * properly handles Discord permission requirements and UI state management.
 */

require('dotenv').config();
const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class PermissionBasedDisablingTest extends BotTestBase {
    constructor() {
        super('PermissionBasedDisablingTest');
    }

    /**
     * Test permission checking for features
     */
    async testFeaturePermissionChecking() {
        console.log('\n🔒 Testing feature permission checking...');
        
        const { checkFeaturePermissions, FEATURE_PERMISSIONS } = require('../utils/permissionHandler.js');
        
        // Create mock guild with limited bot permissions
        const mockGuild = {
            members: {
                me: {
                    permissions: {
                        has: (permission) => {
                            // Simulate bot missing ManageRoles permission
                            const { PermissionFlagsBits } = require('discord.js');
                            return permission !== PermissionFlagsBits.ManageRoles;
                        }
                    }
                }
            }
        };

        // Test sticky feature (requires ManageRoles)
        const stickyResult = checkFeaturePermissions(mockGuild, 'sticky');
        
        console.log('   Sticky permission check:', {
            hasPermissions: stickyResult.hasPermissions,
            missingPermissions: stickyResult.missingPermissionNames
        });

        if (!stickyResult.hasPermissions && stickyResult.missingPermissionNames.includes('Manage Roles')) {
            console.log('✅ Sticky permission check correctly identified missing ManageRoles');
            return true;
        } else {
            console.log('❌ Sticky permission check failed');
            return false;
        }
    }

    /**
     * Test permission status message creation
     */
    async testPermissionStatusMessages() {
        console.log('\n📝 Testing permission status message creation...');
        
        const { createPermissionStatusMessage } = require('../utils/permissionHandler.js');
        
        // Test plural permissions
        const multiplePermissions = ['Manage Roles', 'View Audit Log'];
        const pluralStatusMessage = createPermissionStatusMessage(multiplePermissions);

        const expectedPluralContent = '**status:** 🔒 Missing required permissions: Manage Roles, View Audit Log';
        const actualPluralContent = pluralStatusMessage.data.content;

        // Test singular permission
        const singlePermission = ['Manage Roles'];
        const singularStatusMessage = createPermissionStatusMessage(singlePermission);

        const expectedSingularContent = '**status:** 🔒 Missing required permission: Manage Roles';
        const actualSingularContent = singularStatusMessage.data.content;

        console.log('   Expected plural:', expectedPluralContent);
        console.log('   Actual plural:', actualPluralContent);
        console.log('   Expected singular:', expectedSingularContent);
        console.log('   Actual singular:', actualSingularContent);

        if (actualPluralContent === expectedPluralContent && actualSingularContent === expectedSingularContent) {
            console.log('✅ Permission status messages created correctly (singular/plural grammar)');
            return true;
        } else {
            console.log('❌ Permission status message creation failed');
            return false;
        }
    }

    /**
     * Test disabled toggle button creation
     */
    async testDisabledToggleButton() {
        console.log('\n🔘 Testing disabled toggle button creation...');
        
        const { createDisabledToggleButton } = require('../utils/permissionHandler.js');
        const { ButtonStyle } = require('discord.js');
        
        const missingPermissions = ['Manage Roles'];
        const button = createDisabledToggleButton('sticky', missingPermissions, 'test-button');
        
        console.log('   Button properties:', {
            customId: button.data.custom_id,
            label: button.data.label,
            disabled: button.data.disabled,
            style: button.data.style,
            emoji: button.data.emoji
        });

        const expectedLabel = 'enable'; // Should maintain consistent styling
        const isCorrect = button.data.custom_id === 'test-button' &&
                         button.data.label === expectedLabel &&
                         button.data.disabled === true &&
                         button.data.style === ButtonStyle.Success; // Success style for enable button

        if (isCorrect) {
            console.log('✅ Disabled toggle button created correctly');
            return true;
        } else {
            console.log('❌ Disabled toggle button creation failed');
            return false;
        }
    }

    /**
     * Test enabled toggle button creation
     */
    async testEnabledToggleButton() {
        console.log('\n✅ Testing enabled toggle button creation...');
        
        const { createEnabledToggleButton } = require('../utils/permissionHandler.js');
        const { ButtonStyle } = require('discord.js');
        
        // Test for currently disabled feature (should show "Enable")
        const enableButton = createEnabledToggleButton(false, 'test-enable');
        
        console.log('   Enable button properties:', {
            customId: enableButton.data.custom_id,
            label: enableButton.data.label,
            disabled: enableButton.data.disabled,
            style: enableButton.data.style,
            emoji: enableButton.data.emoji
        });

        // Test for currently enabled feature (should show "Disable")
        const disableButton = createEnabledToggleButton(true, 'test-disable');
        
        console.log('   Disable button properties:', {
            customId: disableButton.data.custom_id,
            label: disableButton.data.label,
            disabled: disableButton.data.disabled,
            style: disableButton.data.style,
            emoji: disableButton.data.emoji
        });

        const enableCorrect = enableButton.data.label === 'enable' && // lowercase
                             enableButton.data.style === ButtonStyle.Success &&
                             enableButton.data.disabled === false;

        const disableCorrect = disableButton.data.label === 'disable' && // lowercase
                              disableButton.data.style === ButtonStyle.Danger &&
                              disableButton.data.disabled === false;

        if (enableCorrect && disableCorrect) {
            console.log('✅ Enabled toggle buttons created correctly');
            return true;
        } else {
            console.log('❌ Enabled toggle button creation failed');
            return false;
        }
    }

    /**
     * Test comprehensive feature status
     */
    async testFeatureStatus() {
        console.log('\n🎯 Testing comprehensive feature status...');
        
        const { getFeatureStatus } = require('../utils/permissionHandler.js');
        const { PermissionFlagsBits } = require('discord.js');
        
        // Create mock guild and member with missing bot permissions
        const mockGuild = {
            members: {
                me: {
                    permissions: {
                        has: (permission) => permission !== PermissionFlagsBits.ManageRoles
                    }
                }
            }
        };

        const mockMember = {
            user: { id: process.env.OWNER }, // Bot owner should have user permissions
            guild: mockGuild,
            permissions: {
                has: () => true // User has all permissions
            }
        };

        const featureStatus = getFeatureStatus(mockGuild, mockMember, 'sticky', false, 'test-status');
        
        console.log('   Feature status result:', {
            hasPermissions: featureStatus.hasPermissions,
            userHasPermission: featureStatus.userHasPermission,
            botHasPermissions: featureStatus.botHasPermissions,
            missingPermissions: featureStatus.missingPermissions,
            buttonLabel: featureStatus.button.data.label,
            buttonDisabled: featureStatus.button.data.disabled,
            statusMessage: featureStatus.statusMessage?.data?.content
        });

        // Should show bot permission issue with consistent button styling
        const isCorrect = !featureStatus.hasPermissions &&
                         featureStatus.userHasPermission &&
                         !featureStatus.botHasPermissions &&
                         featureStatus.missingPermissions.includes('Manage Roles') &&
                         featureStatus.statusMessage?.data?.content?.includes('Missing required permission') && // Singular grammar for 1 permission
                         featureStatus.button.data.disabled === true &&
                         featureStatus.button.data.label === 'enable'; // Should maintain consistent styling

        if (isCorrect) {
            console.log('✅ Feature status correctly identified bot permission issue');
            return true;
        } else {
            console.log('❌ Feature status test failed');
            return false;
        }
    }

    /**
     * Test non-toggle feature permission checking
     */
    async testNonToggleFeaturePermissions() {
        console.log('\n🎯 Testing non-toggle feature permission checking...');

        const { checkNonToggleFeaturePermissions, CRITICAL_PERMISSIONS } = require('../utils/permissionHandler.js');
        const { PermissionFlagsBits } = require('discord.js');

        // Create mock guild missing critical permissions
        const mockGuild = {
            members: {
                me: {
                    permissions: {
                        has: (permission) => {
                            // Simulate bot missing SendMessages (critical permission)
                            return permission !== PermissionFlagsBits.SendMessages;
                        }
                    }
                }
            }
        };

        const result = checkNonToggleFeaturePermissions(mockGuild, 'you');

        console.log('   Non-toggle permission check result:', {
            canFunction: result?.canFunction,
            missingPermissions: result?.missingPermissions,
            statusMessage: result?.statusMessage?.data?.content
        });

        // Check that it uses correct grammar (singular "permission" for 1 missing)
        const expectedGrammar = result?.statusMessage?.data?.content?.includes('Missing permission ') ||
                               result?.statusMessage?.data?.content?.includes('Missing permissions ');

        if (result && !result.canFunction && result.statusMessage?.data?.content?.includes('Bot cannot function') && expectedGrammar) {
            console.log('✅ Non-toggle feature permission checking works correctly with proper grammar');
            return true;
        } else {
            console.log('❌ Non-toggle feature permission checking failed');
            console.log('   Grammar check:', expectedGrammar);
            return false;
        }
    }

    /**
     * Test feature permission constants
     */
    async testFeaturePermissionConstants() {
        console.log('\n📋 Testing feature permission constants...');

        const { FEATURE_PERMISSIONS, PERMISSION_NAMES, CRITICAL_PERMISSIONS } = require('../utils/permissionHandler.js');
        const { PermissionFlagsBits } = require('discord.js');

        console.log('   Feature permissions:', Object.keys(FEATURE_PERMISSIONS));
        console.log('   Permission names:', Object.keys(PERMISSION_NAMES).length);
        console.log('   Critical permissions:', CRITICAL_PERMISSIONS.length);

        // Check that key features have permission requirements
        const requiredFeatures = ['sticky', 'exp', 'dehoist', 'logs', 'opener', 'items', 'transcribe-voice', 'you', '17', 'lookup'];
        const hasAllFeatures = requiredFeatures.every(feature =>
            FEATURE_PERMISSIONS[feature] && FEATURE_PERMISSIONS[feature].length > 0
        );

        // Check that permission names exist for critical permissions
        const hasCriticalNames = CRITICAL_PERMISSIONS.every(perm => PERMISSION_NAMES[perm]);

        // Check that core permissions are included
        const corePermissions = [
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages,
            PermissionFlagsBits.UseApplicationCommands
        ];
        const hasCorePermissions = corePermissions.every(perm => CRITICAL_PERMISSIONS.includes(perm));

        if (hasAllFeatures && hasCriticalNames && hasCorePermissions) {
            console.log('✅ Feature permission constants are properly configured');
            return true;
        } else {
            console.log('❌ Feature permission constants test failed');
            console.log('   Missing features:', requiredFeatures.filter(f => !FEATURE_PERMISSIONS[f]));
            console.log('   Missing critical names:', CRITICAL_PERMISSIONS.filter(p => !PERMISSION_NAMES[p]));
            console.log('   Missing core permissions:', corePermissions.filter(p => !CRITICAL_PERMISSIONS.includes(p)));
            return false;
        }
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Feature permission checking', test: () => this.testFeaturePermissionChecking() },
            { name: 'Permission status messages', test: () => this.testPermissionStatusMessages() },
            { name: 'Disabled toggle button', test: () => this.testDisabledToggleButton() },
            { name: 'Enabled toggle button', test: () => this.testEnabledToggleButton() },
            { name: 'Feature status', test: () => this.testFeatureStatus() },
            { name: 'Non-toggle permissions', test: () => this.testNonToggleFeaturePermissions() },
            { name: 'Permission constants', test: () => this.testFeaturePermissionConstants() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
    }
}

// Main execution function
async function runPermissionBasedDisablingTest() {
    const tester = new PermissionBasedDisablingTest();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runPermissionBasedDisablingTest();
}

module.exports = { PermissionBasedDisablingTest };
