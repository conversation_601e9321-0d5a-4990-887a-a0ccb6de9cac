# 🎨 UI/UX Implementation Guide

## Overview
This guide provides comprehensive patterns and best practices for implementing consistent user interface behaviors across all bot features. These patterns ensure data persistence, prevent user frustration, and maintain professional UX standards.

## Core UI/UX Principles

### 1. **Data Persistence** - Never lose user input
### 2. **State Clarity** - Always show current selections and context
### 3. **Predictable Behavior** - Consistent patterns across all features
### 4. **Error Prevention** - Clear previous state to avoid confusion

---

## Modal State Management

### Problem: Modal Field Persistence
Discord modals can retain previous input values between different operations, causing confusion when users see old data in new forms.

### Solution: Explicit Field Clearing
**Always clear modal fields before showing them for new operations**

```javascript
/**
 * Clear all input fields in a modal to prevent leftover state
 * @param {ModalBuilder} modal - The modal to clear
 */
function clearModalFields(modal) {
    modal.components.forEach(row => {
        row.components.forEach(component => {
            if (component.data.value) {
                component.data.value = ''; // Clear previous values
            }
            if (component.data.placeholder) {
                // Reset to default placeholder
                component.data.placeholder = getDefaultPlaceholder(component.data.custom_id);
            }
        });
    });
}

// Usage example - Items creation
function showCreateItemModal(itemType) {
    const modal = buildItemModal(itemType);
    clearModalFields(modal); // Always clear before showing
    return modal;
}

// Usage example - Switching from edit to create
function switchToCreateMode(interaction) {
    const modal = buildItemModal('weapon');
    clearModalFields(modal); // Clear any edit data
    modal.setTitle('Create New Item'); // Clear title context
    return modal;
}
```

### When to Clear Modal Fields
- ✅ **Switching from edit to create operations**
- ✅ **Changing item/level types in creation flow**
- ✅ **Starting new workflows after completing previous ones**
- ❌ **Don't clear when user is correcting validation errors**

---

## Select Menu Default Values

### Problem: Missing Current State Display
Select menus should show current configuration values, not blank placeholders, so users understand what's currently set.

### Solution: Pre-populate with Current Values
**Always show current settings as default selections**

```javascript
/**
 * Build select menu with current value highlighted
 * @param {string} currentValue - Currently configured value
 * @param {Array} options - Available options
 * @param {string} placeholder - Fallback placeholder text
 */
function buildConfigSelectMenu(currentValue, options, placeholder) {
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('config-select')
        .setPlaceholder(
            currentValue 
                ? `Current: ${getCurrentValueLabel(currentValue)}` 
                : placeholder
        );
    
    // Mark current value as default
    const menuOptions = options.map(option => ({
        label: option.label,
        value: option.value,
        description: option.description,
        default: option.value === currentValue, // Highlight current
        emoji: option.value === currentValue ? '✅' : option.emoji
    }));
    
    selectMenu.addOptions(menuOptions);
    return selectMenu;
}

// Usage example - EXP system configuration
async function buildExpConfigInterface(guildId) {
    const currentConfig = await getExpConfig(guildId);
    
    const textExpSelect = buildConfigSelectMenu(
        currentConfig.textExpPerMin,
        TEXT_EXP_OPTIONS,
        'Select text EXP rate'
    );
    
    const voiceExpSelect = buildConfigSelectMenu(
        currentConfig.voiceExpPerMin,
        VOICE_EXP_OPTIONS,
        'Select voice EXP rate'
    );
    
    return { textExpSelect, voiceExpSelect };
}
```

### Current Value Display Patterns
```javascript
// Pattern 1: Show current value in placeholder
.setPlaceholder(`Current: ${currentValue} per minute`)

// Pattern 2: Show current value with checkmark emoji
.setPlaceholder(`✅ Currently: ${currentValue}`)

// Pattern 3: Show "Not configured" for empty values
.setPlaceholder(currentValue ? `Current: ${currentValue}` : 'Not configured')

// Pattern 4: Include change indicator
.setPlaceholder(`Current: ${currentValue} (click to change)`)
```

---

## Cascading Select Menu State Persistence

### Problem: Lost Context in Multi-Step Flows
In cascading select menus, users lose track of their previous selections when moving to subsequent steps.

### Solution: Embed State in CustomIds and Show Context
**Preserve all previous selections and show them in the interface**

```javascript
/**
 * Create cascading select menu with embedded state
 * @param {string} baseId - Base custom ID
 * @param {Object} stateData - Previous selections and context
 * @param {string} currentStep - Current step identifier
 */
function createCascadingSelectMenu(baseId, stateData, currentStep) {
    const customId = `${baseId}-${JSON.stringify({
        ...stateData,
        step: currentStep,
        timestamp: Date.now()
    })}`;
    
    return new StringSelectMenuBuilder()
        .setCustomId(customId)
        .setPlaceholder(buildContextualPlaceholder(stateData, currentStep));
}

/**
 * Build placeholder that shows previous selections
 */
function buildContextualPlaceholder(stateData, currentStep) {
    const context = [];
    
    if (stateData.type) context.push(`Type: ${stateData.type}`);
    if (stateData.category) context.push(`Category: ${stateData.category}`);
    
    const contextStr = context.length > 0 ? `${context.join(' | ')} → ` : '';
    return `${contextStr}${getStepLabel(currentStep)}`;
}

// Usage example - Items creation flow
async function handleItemTypeSelection(interaction) {
    const selectedType = interaction.values[0];
    
    // Step 2: Icon selection with type context
    const iconSelect = createCascadingSelectMenu('items-icon-select', {
        type: selectedType,
        action: 'create'
    }, 'icon-selection');
    
    iconSelect.setPlaceholder(`Type: ${selectedType} → Select icon`);
    
    const availableIcons = await getIconsForType(selectedType);
    iconSelect.addOptions(availableIcons);
    
    return iconSelect;
}

async function handleIconSelection(interaction) {
    // Parse previous state
    const stateData = parseCustomIdState(interaction.customId);
    const selectedIcon = interaction.values[0];
    
    // Step 3: Configuration modal with full context
    const modal = new ModalBuilder()
        .setCustomId(`items-config-modal-${JSON.stringify({
            ...stateData,
            iconUrl: selectedIcon,
            step: 'configuration'
        })}`)
        .setTitle(`Create ${stateData.type} Item`); // Show type in title
    
    // Add context display in modal
    const contextField = new TextInputBuilder()
        .setCustomId('context-display')
        .setLabel(`Creating: ${stateData.type} with selected icon`)
        .setStyle(TextInputStyle.Short)
        .setValue(`Type: ${stateData.type} | Icon: ${getIconName(selectedIcon)}`)
        .setRequired(false);
    
    modal.addComponents(new ActionRowBuilder().addComponents(contextField));
    
    return modal;
}
```

### State Parsing Utility
```javascript
/**
 * Parse state data from customId
 * @param {string} customId - Custom ID with embedded JSON state
 * @returns {Object} Parsed state data
 */
function parseCustomIdState(customId) {
    try {
        const parts = customId.split('-');
        const jsonIndex = parts.findIndex(part => part.startsWith('{'));
        
        if (jsonIndex !== -1) {
            const jsonStr = parts.slice(jsonIndex).join('-');
            return JSON.parse(jsonStr);
        }
    } catch (error) {
        console.warn('Failed to parse customId state:', error);
    }
    
    return {}; // Return empty object as fallback
}
```

---

## Data Persistence Patterns

### Universal State Management Pattern
**Use this pattern for all multi-step interfaces**

```javascript
/**
 * Universal state management for multi-step interfaces
 */
class InterfaceStateManager {
    constructor(baseId, initialState = {}) {
        this.baseId = baseId;
        this.state = {
            ...initialState,
            created: Date.now(),
            steps: []
        };
    }
    
    /**
     * Add a step to the state history
     */
    addStep(stepName, stepData) {
        this.state.steps.push({
            name: stepName,
            data: stepData,
            timestamp: Date.now()
        });
        
        // Merge step data into main state
        Object.assign(this.state, stepData);
    }
    
    /**
     * Generate customId with embedded state
     */
    generateCustomId(action) {
        return `${this.baseId}-${action}-${JSON.stringify(this.state)}`;
    }
    
    /**
     * Create contextual placeholder showing progress
     */
    getContextualPlaceholder(currentAction) {
        const completedSteps = this.state.steps.map(step => 
            `${step.name}: ${this.formatStepData(step.data)}`
        );
        
        if (completedSteps.length > 0) {
            return `${completedSteps.join(' | ')} → ${currentAction}`;
        }
        
        return `Select ${currentAction}`;
    }
    
    formatStepData(data) {
        // Format step data for display
        if (data.type) return data.type;
        if (data.name) return data.name;
        return 'Selected';
    }
}

// Usage example
const stateManager = new InterfaceStateManager('exp-level-edit', {
    guildId: interaction.guild.id,
    userId: interaction.user.id
});

// Step 1: Level selection
stateManager.addStep('level-selection', { levelId: selectedLevelId });
const editSelect = new StringSelectMenuBuilder()
    .setCustomId(stateManager.generateCustomId('edit-property'))
    .setPlaceholder(stateManager.getContextualPlaceholder('property to edit'));

// Step 2: Property selection
stateManager.addStep('property-selection', { property: selectedProperty });
const valueModal = new ModalBuilder()
    .setCustomId(stateManager.generateCustomId('set-value'))
    .setTitle(`Edit ${selectedProperty} for Level ${selectedLevelId}`);
```

---

## Implementation Checklist

### ✅ **Modal Operations**
- [ ] Clear modal fields when switching from edit to create
- [ ] Clear modal fields when changing item/level types
- [ ] Preserve modal data when correcting validation errors
- [ ] Reset placeholders to defaults after clearing

### ✅ **Select Menu Configuration**
- [ ] Show current values in placeholders
- [ ] Mark current options as default with checkmarks
- [ ] Use "Not configured" for empty values
- [ ] Include change indicators in placeholders

### ✅ **Cascading Select Menus**
- [ ] Embed state data in customIds
- [ ] Show previous selections in placeholders
- [ ] Parse state data reliably with error handling
- [ ] Maintain context across all steps

### ✅ **Data Persistence**
- [ ] Use consistent state management patterns
- [ ] Preserve user input across interface changes
- [ ] Show progress indicators in multi-step flows
- [ ] Handle state parsing errors gracefully

---

## Testing UI/UX Patterns

### Test Cases for Modal State Management
```javascript
async testModalStateClearing() {
    // Test 1: Edit to create transition
    const editModal = this.createMockModal('edit-item', { name: 'Old Item' });
    const createModal = switchToCreateMode(editModal);
    
    assert(createModal.components[0].components[0].data.value === '');
    
    // Test 2: Type change in creation
    const weaponModal = this.createMockModal('create-weapon', { damage: '100' });
    const armorModal = changeItemType(weaponModal, 'armor');
    
    assert(armorModal.components[0].components[0].data.value === '');
}
```

### Test Cases for Select Menu Defaults
```javascript
async testSelectMenuDefaults() {
    const currentConfig = { textExpPerMin: 5 };
    const selectMenu = buildConfigSelectMenu(currentConfig.textExpPerMin, EXP_OPTIONS);
    
    // Should show current value in placeholder
    assert(selectMenu.data.placeholder.includes('Current: 5'));
    
    // Should mark current option as default
    const defaultOption = selectMenu.data.options.find(opt => opt.default);
    assert(defaultOption.value === '5');
}
```

### Test Cases for Cascading State
```javascript
async testCascadingState() {
    const stateManager = new InterfaceStateManager('test', {});
    
    stateManager.addStep('type', { type: 'weapon' });
    stateManager.addStep('icon', { iconUrl: 'sword.png' });
    
    const customId = stateManager.generateCustomId('configure');
    const parsedState = parseCustomIdState(customId);
    
    assert(parsedState.type === 'weapon');
    assert(parsedState.iconUrl === 'sword.png');
}
```

---

## Common Anti-Patterns to Avoid

### ❌ **Don't Do This**
```javascript
// Bad: Not clearing modal fields
function showCreateModal() {
    return existingModal; // May contain old data
}

// Bad: Empty placeholders
.setPlaceholder('Select option') // Doesn't show current state

// Bad: Losing context in cascading menus
.setCustomId('step2-select') // No state preservation

// Bad: No error handling for state parsing
const state = JSON.parse(customId.split('-')[1]); // Will crash
```

### ✅ **Do This Instead**
```javascript
// Good: Always clear modal fields
function showCreateModal() {
    const modal = buildModal();
    clearModalFields(modal);
    return modal;
}

// Good: Show current state
.setPlaceholder(currentValue ? `Current: ${currentValue}` : 'Not configured')

// Good: Preserve state with error handling
.setCustomId(`step2-select-${JSON.stringify(safeState)}`)

// Good: Safe state parsing
const state = parseCustomIdState(customId) || {};
```

---

## Status Message Error Handling (MANDATORY)

### **Status:** Pattern Requirements
All error messages must use the **status:** pattern for consistent UX:

```javascript
// CORRECT: Status message in container
const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Error message here');
container.addTextDisplayComponents(statusDisplay);

// INCORRECT: Ephemeral error replies
await interaction.reply({
    content: '❌ Error message',
    flags: MessageFlags.Ephemeral
});
```

### Key Requirements
- Always add status messages to the **first container** (never secondary containers)
- Use `**status:**` prefix followed by appropriate emoji and message
- Status messages should edit the current interface, not create new ones
- Preserve user input data when showing status messages

### Status Message Types
```javascript
// Error status
'**status:** ❌ Error message'

// Success status
'**status:** ✅ Success message'

// Warning status
'**status:** ⚠️ Warning message'

// Info status
'**status:** ℹ️ Information message'

// Permission status
'**status:** 🔒 Missing required permissions: Manage Roles, View Audit Log'
```

### Permission-Based Feature Disabling
When features lack required Discord permissions:

```javascript
// Check bot permissions
const botPermissions = interaction.guild.members.me.permissions;
const requiredPermissions = ['ManageRoles', 'ViewAuditLog'];
const missingPermissions = requiredPermissions.filter(perm => !botPermissions.has(perm));

if (missingPermissions.length > 0) {
    // Show status message
    const statusDisplay = new TextDisplayBuilder()
        .setContent(`**status:** 🔒 Missing required permissions: ${missingPermissions.join(', ')}`);
    container.addTextDisplayComponents(statusDisplay);

    // Disable toggle button
    const toggleButton = new ButtonBuilder()
        .setCustomId('feature-toggle')
        .setLabel(`Requires ${missingPermissions[0]} to Enable`)
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(true);
}
```

This guide ensures consistent, professional UI/UX behavior across all bot features and prevents common user experience issues.
