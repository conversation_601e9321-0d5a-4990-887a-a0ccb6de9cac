/**
 * Database Sharding Strategy for Discovery Ranks
 * 
 * Phase 3.2: Implements horizontal scaling for millions of items
 * across multiple MongoDB servers with intelligent shard key distribution.
 * 
 * Features:
 * - Automatic shard key generation based on itemName + foundInGuild
 * - Load balancing across multiple database shards
 * - Transparent query routing to appropriate shards
 * - Shard health monitoring and failover
 * - Performance metrics and optimization
 */

const { MongoClient } = require('mongodb');
const crypto = require('crypto');

// Sharding configuration
const SHARD_CONFIG = {
    // Shard connection strings (add more as needed)
    shards: [
        {
            id: 'shard1',
            uri: process.env.MONGO_SHARD1 || process.env.MONGO,
            weight: 1.0, // Load balancing weight
            healthy: true
        },
        {
            id: 'shard2', 
            uri: process.env.MONGO_SHARD2 || process.env.MONGO,
            weight: 1.0,
            healthy: true
        },
        {
            id: 'shard3',
            uri: process.env.MONGO_SHARD3 || process.env.MONGO,
            weight: 1.0,
            healthy: true
        }
    ],
    
    // Sharding strategy
    shardKey: ['itemName', 'foundInGuild'], // Compound shard key
    collections: ['user_inventory'], // Collections to shard
    
    // Performance settings
    connectionPoolSize: 20,
    queryTimeout: 5000,
    healthCheckInterval: 30000 // 30 seconds
};

class DatabaseShardManager {
    constructor() {
        this.shardClients = new Map();
        this.shardHealth = new Map();
        this.queryMetrics = new Map();
        this.initialized = false;
        
        // Performance tracking
        this.metrics = {
            totalQueries: 0,
            shardDistribution: new Map(),
            avgQueryTime: 0,
            errors: 0,
            failovers: 0,
            startTime: Date.now()
        };
        
        this.initializeShards();
        this.startHealthMonitoring();
    }

    /**
     * Initialize connections to all database shards
     */
    async initializeShards() {
        console.log('[ShardManager] 🔄 Initializing database shards...');
        
        for (const shard of SHARD_CONFIG.shards) {
            try {
                console.log(`[ShardManager] 🔌 Connecting to ${shard.id}...`);
                
                const client = new MongoClient(shard.uri, {
                    maxPoolSize: SHARD_CONFIG.connectionPoolSize,
                    serverSelectionTimeoutMS: SHARD_CONFIG.queryTimeout,
                    socketTimeoutMS: SHARD_CONFIG.queryTimeout
                });
                
                await client.connect();
                
                this.shardClients.set(shard.id, client);
                this.shardHealth.set(shard.id, { healthy: true, lastCheck: Date.now() });
                this.queryMetrics.set(shard.id, { queries: 0, avgTime: 0, errors: 0 });
                
                console.log(`[ShardManager] ✅ ${shard.id} connected`);
                
            } catch (error) {
                console.error(`[ShardManager] ❌ Failed to connect to ${shard.id}:`, error.message);
                this.shardHealth.set(shard.id, { healthy: false, lastCheck: Date.now(), error: error.message });
            }
        }
        
        const healthyShards = Array.from(this.shardHealth.values()).filter(h => h.healthy).length;
        console.log(`[ShardManager] 📊 Initialized ${healthyShards}/${SHARD_CONFIG.shards.length} shards`);
        
        this.initialized = true;
    }

    /**
     * Generate shard key hash for consistent routing
     */
    generateShardKey(itemName, foundInGuild) {
        const keyString = `${itemName}:${foundInGuild || 'global'}`;
        const hash = crypto.createHash('md5').update(keyString).digest('hex');
        
        // Convert hash to number and mod by shard count
        const hashNumber = parseInt(hash.substring(0, 8), 16);
        const shardIndex = hashNumber % SHARD_CONFIG.shards.length;
        
        return SHARD_CONFIG.shards[shardIndex].id;
    }

    /**
     * Get healthy shard client for a given shard key
     */
    getShardClient(shardId) {
        const health = this.shardHealth.get(shardId);
        
        if (health && health.healthy) {
            return this.shardClients.get(shardId);
        }
        
        // Failover to healthy shard
        console.log(`[ShardManager] ⚠️ Shard ${shardId} unhealthy, finding failover...`);
        this.metrics.failovers++;
        
        for (const [id, healthStatus] of this.shardHealth.entries()) {
            if (healthStatus.healthy) {
                console.log(`[ShardManager] 🔄 Failing over to ${id}`);
                return this.shardClients.get(id);
            }
        }
        
        throw new Error('No healthy shards available');
    }

    /**
     * Execute sharded query with automatic routing
     */
    async executeShardedQuery(collection, query, operation = 'find') {
        if (!this.initialized) {
            throw new Error('Shard manager not initialized');
        }
        
        const startTime = Date.now();
        this.metrics.totalQueries++;
        
        try {
            // Determine target shard based on query
            let targetShardId;
            
            if (query.itemName && (query.foundInGuild !== undefined)) {
                // Single shard query - route to specific shard
                targetShardId = this.generateShardKey(query.itemName, query.foundInGuild);
            } else {
                // Multi-shard query - execute on all healthy shards and merge results
                return await this.executeMultiShardQuery(collection, query, operation);
            }
            
            // Execute on target shard
            const client = this.getShardClient(targetShardId);
            const db = client.db();
            const coll = db.collection(collection);
            
            let result;
            switch (operation) {
                case 'find':
                    result = await coll.find(query).toArray();
                    break;
                case 'count':
                    result = await coll.countDocuments(query);
                    break;
                case 'aggregate':
                    result = await coll.aggregate(query).toArray();
                    break;
                default:
                    throw new Error(`Unsupported operation: ${operation}`);
            }
            
            // Update metrics
            const queryTime = Date.now() - startTime;
            this.updateShardMetrics(targetShardId, queryTime);
            this.updateDistribution(targetShardId);
            
            console.log(`[ShardManager] ✅ Query executed on ${targetShardId} (${queryTime}ms)`);
            return result;
            
        } catch (error) {
            const queryTime = Date.now() - startTime;
            this.metrics.errors++;
            
            console.error(`[ShardManager] ❌ Sharded query failed (${queryTime}ms):`, error.message);
            throw error;
        }
    }

    /**
     * Execute query across multiple shards and merge results
     */
    async executeMultiShardQuery(collection, query, operation) {
        console.log(`[ShardManager] 🔄 Executing multi-shard ${operation} query...`);
        
        const promises = [];
        const healthyShards = [];
        
        // Execute on all healthy shards
        for (const [shardId, health] of this.shardHealth.entries()) {
            if (health.healthy) {
                healthyShards.push(shardId);
                
                const promise = (async () => {
                    try {
                        const client = this.shardClients.get(shardId);
                        const db = client.db();
                        const coll = db.collection(collection);
                        
                        switch (operation) {
                            case 'find':
                                return await coll.find(query).toArray();
                            case 'count':
                                return await coll.countDocuments(query);
                            case 'aggregate':
                                return await coll.aggregate(query).toArray();
                            default:
                                throw new Error(`Unsupported operation: ${operation}`);
                        }
                    } catch (error) {
                        console.error(`[ShardManager] ❌ Multi-shard query failed on ${shardId}:`, error.message);
                        return operation === 'count' ? 0 : [];
                    }
                })();
                
                promises.push(promise);
            }
        }
        
        // Wait for all shard results
        const results = await Promise.all(promises);
        
        // Merge results based on operation type
        if (operation === 'count') {
            const totalCount = results.reduce((sum, count) => sum + count, 0);
            console.log(`[ShardManager] 📊 Multi-shard count: ${totalCount} across ${healthyShards.length} shards`);
            return totalCount;
        } else {
            const mergedResults = results.flat();
            console.log(`[ShardManager] 📊 Multi-shard results: ${mergedResults.length} items across ${healthyShards.length} shards`);
            return mergedResults;
        }
    }

    /**
     * Update shard performance metrics
     */
    updateShardMetrics(shardId, queryTime) {
        const metrics = this.queryMetrics.get(shardId);
        if (metrics) {
            metrics.queries++;
            metrics.avgTime = ((metrics.avgTime * (metrics.queries - 1)) + queryTime) / metrics.queries;
        }
    }

    /**
     * Update shard distribution metrics
     */
    updateDistribution(shardId) {
        const current = this.metrics.shardDistribution.get(shardId) || 0;
        this.metrics.shardDistribution.set(shardId, current + 1);
    }

    /**
     * Start health monitoring for all shards
     */
    startHealthMonitoring() {
        setInterval(async () => {
            await this.performHealthChecks();
        }, SHARD_CONFIG.healthCheckInterval);
        
        console.log(`[ShardManager] 💓 Health monitoring started (${SHARD_CONFIG.healthCheckInterval}ms interval)`);
    }

    /**
     * Perform health checks on all shards
     */
    async performHealthChecks() {
        for (const [shardId, client] of this.shardClients.entries()) {
            try {
                await client.db().admin().ping();
                this.shardHealth.set(shardId, { healthy: true, lastCheck: Date.now() });
            } catch (error) {
                console.error(`[ShardManager] ❌ Health check failed for ${shardId}:`, error.message);
                this.shardHealth.set(shardId, { 
                    healthy: false, 
                    lastCheck: Date.now(), 
                    error: error.message 
                });
            }
        }
    }

    /**
     * Get comprehensive sharding statistics
     */
    getShardingStats() {
        const uptime = Date.now() - this.metrics.startTime;
        const healthyShards = Array.from(this.shardHealth.values()).filter(h => h.healthy).length;
        
        // Calculate distribution percentages
        const distribution = {};
        for (const [shardId, count] of this.metrics.shardDistribution.entries()) {
            const percentage = this.metrics.totalQueries > 0 
                ? ((count / this.metrics.totalQueries) * 100).toFixed(1)
                : '0.0';
            distribution[shardId] = `${count} (${percentage}%)`;
        }
        
        return {
            // Shard status
            totalShards: SHARD_CONFIG.shards.length,
            healthyShards,
            shardHealth: Object.fromEntries(this.shardHealth),
            
            // Performance metrics
            totalQueries: this.metrics.totalQueries,
            avgQueryTime: this.metrics.avgQueryTime.toFixed(2) + 'ms',
            errors: this.metrics.errors,
            failovers: this.metrics.failovers,
            
            // Load distribution
            queryDistribution: distribution,
            
            // Individual shard metrics
            shardMetrics: Object.fromEntries(this.queryMetrics),
            
            // System info
            uptime: Math.round(uptime / 1000) + 's',
            architecture: 'Horizontal Sharding',
            shardKey: SHARD_CONFIG.shardKey.join(' + ')
        };
    }

    /**
     * Graceful shutdown of all shard connections
     */
    async shutdown() {
        console.log('[ShardManager] 🔄 Shutting down shard connections...');
        
        for (const [shardId, client] of this.shardClients.entries()) {
            try {
                await client.close();
                console.log(`[ShardManager] ✅ ${shardId} connection closed`);
            } catch (error) {
                console.error(`[ShardManager] ❌ Error closing ${shardId}:`, error.message);
            }
        }
        
        console.log('[ShardManager] ✅ All shard connections closed');
    }
}

// Create singleton instance
const databaseShardManager = new DatabaseShardManager();

module.exports = {
    DatabaseShardManager,
    databaseShardManager
};
