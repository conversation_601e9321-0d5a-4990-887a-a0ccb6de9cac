/**
 * Owner Backup System - Modular Component
 * Comprehensive database backup utility for local MongoDB
 */

const { ContainerBuilder, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const { optimizedFindOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

// Performance metrics for backup system
const backupMetrics = {
    totalBackups: 0,
    successfulBackups: 0,
    failedBackups: 0,
    lastBackupTime: null,
    lastBackupSize: 0,
    averageBackupTime: 0,
    verboseLogging: process.env.NODE_ENV === 'development'
};

// State management for cascading menus (like items system)
const backupStateCache = new Map();

/**
 * Get the correct mongodump executable name for the current platform
 * @returns {string} The mongodump executable name
 */
function getMongodumpExecutable() {
    const platform = os.platform();

    // On Windows, executables typically have .exe extension
    if (platform === 'win32') {
        return 'mongodump.exe';
    }

    // On Unix-like systems (Linux, macOS), no extension needed
    return 'mongodump';
}

/**
 * Check if mongodump is available on the system
 * @returns {Promise<boolean>} Whether mongodump is available
 */
async function checkMongodumpAvailable() {
    return new Promise((resolve) => {
        const executable = getMongodumpExecutable();
        const testProcess = spawn(executable, ['--version'], { stdio: 'ignore' });

        testProcess.on('close', (code) => {
            resolve(code === 0);
        });

        testProcess.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * Store backup UI state (for cascading menus)
 */
function storeBackupState(userId, state) {
    backupStateCache.set(userId, { ...state, updatedAt: new Date() });
}

/**
 * Get backup UI state
 */
function getBackupState(userId) {
    return backupStateCache.get(userId) || { currentConfig: null };
}

/**
 * Clear backup UI state
 */
function clearBackupState(userId) {
    backupStateCache.delete(userId);
}

/**
 * Build backup system container
 */
async function buildBackupContainer(client, userId = null) {
    const startTime = Date.now();
    
    try {
        if (backupMetrics.verboseLogging) {
            console.log('[owner-backup] Building backup container...');
        }

        // Get backup configuration and UI state
        const backupConfig = await getBackupConfig();
        const uiState = userId ? getBackupState(userId) : { currentConfig: null };
        
        // Back button section
        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('# backup'))
            .setButtonAccessory(backButton);

        // Quote (title is now in the section)
        const quote = new TextDisplayBuilder().setContent('> automated mongoDB backup utility');

        // Status display
        const statusText = new TextDisplayBuilder().setContent(
            `**enabled:** ${backupConfig.enabled ? 'yes' : 'no'}\n` +
            `**frequency:** ${getFrequencyDisplay(backupConfig.frequency)}\n` +
            `**channel:** ${backupConfig.channelId ? `<#${backupConfig.channelId}>` : 'not set'}\n` +
            `**last backup:** ${getLastBackupDisplay(backupConfig)}`
        );

        // Separator
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

        // Configuration select menu
        const configOptions = [
            {
                label: backupConfig.enabled ? 'disable' : 'enable',
                value: 'toggle',
                description: `Currently ${backupConfig.enabled ? 'enabled' : 'disabled'}`,
                emoji: backupConfig.enabled ? '🔴' : '🟢',
                default: uiState.currentConfig === 'toggle'
            },
            {
                label: 'frequency',
                value: 'frequency',
                description: `Currently: ${getFrequencyDisplay(backupConfig.frequency)}`,
                emoji: '⏰',
                default: uiState.currentConfig === 'frequency'
            },
            {
                label: 'channel',
                value: 'channel',
                description: backupConfig.channelId ? 'Change backup channel' : 'Set backup channel',
                emoji: '📁',
                default: uiState.currentConfig === 'channel'
            }
        ];

        const configSelect = new StringSelectMenuBuilder()
            .setCustomId('backup-config')
            .setPlaceholder('backup configuration')
            .addOptions(configOptions);

        const configRow = new ActionRowBuilder().addComponents(configSelect);
        const components = [configRow];

        // Add cascading select menus based on UI state
        if (uiState.currentConfig === 'frequency') {
            const frequencySelect = buildFrequencySelect(backupConfig.frequency);
            const frequencyRow = new ActionRowBuilder().addComponents(frequencySelect);
            components.push(frequencyRow);
        } else if (uiState.currentConfig === 'channel') {
            const channelSelect = buildChannelSelect(backupConfig.channelId);
            const channelRow = new ActionRowBuilder().addComponents(channelSelect);
            components.push(channelRow);
        }

        // Action buttons (always at the bottom)
        const actionButtons = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('backup-now')
                .setLabel('backup')
                .setStyle(ButtonStyle.Success)
                //.setEmoji('💾')
                .setDisabled(!backupConfig.enabled || !backupConfig.channelId)
        );

        // Build container
        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(quote, statusText)
            .addSeparatorComponents(separator)
            .addActionRowComponents(...components, actionButtons)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        // Performance tracking
        const duration = Date.now() - startTime;
        if (backupMetrics.verboseLogging || duration > 200) {
            console.log(`[owner-backup] ✅ Backup container built in ${duration}ms`);
        }

        return container;

    } catch (error) {
        console.error('[owner-backup] ❌ Error building backup container:', error);
        throw error;
    }
}

/**
 * Get backup configuration from database
 */
async function getBackupConfig() {
    try {
        const config = await optimizedFindOne('backup_config', { key: 'global' });
        
        return {
            enabled: config?.enabled || false,
            frequency: config?.frequency || 7, // Default: 7 days
            channelId: config?.channelId || null,
            lastBackup: config?.lastBackup || null,
            lastBackupMessageId: config?.lastBackupMessageId || null,
            lastBackupUrl: config?.lastBackupUrl || null
        };
    } catch (error) {
        console.error('[owner-backup] Error getting backup config:', error);
        return {
            enabled: false,
            frequency: 7,
            channelId: null,
            lastBackup: null,
            lastBackupMessageId: null,
            lastBackupUrl: null
        };
    }
}

/**
 * Update backup configuration
 */
async function updateBackupConfig(updates) {
    try {
        await optimizedUpdateOne(
            'backup_config',
            { key: 'global' },
            { $set: { key: 'global', ...updates } },
            { upsert: true }
        );
        
        if (backupMetrics.verboseLogging) {
            console.log('[owner-backup] Config updated:', updates);
        }
    } catch (error) {
        console.error('[owner-backup] Error updating backup config:', error);
        throw error;
    }
}

/**
 * Get frequency display text
 */
function getFrequencyDisplay(frequency) {
    switch (frequency) {
        case 1: return 'daily';
        case 3: return 'every 3 days';
        case 7: return 'weekly';
        default: return `every ${frequency} days`;
    }
}



/**
 * Get last backup display text
 */
function getLastBackupDisplay(config) {
    if (!config.lastBackup) {
        return 'never';
    }

    const lastBackup = new Date(config.lastBackup);
    const timestamp = Math.floor(lastBackup.getTime() / 1000);

    // Use Discord timestamp format like in owner container
    let timeText = `<t:${timestamp}:R>`;

    // Add Discord message link if available
    if (config.lastBackupMessageId && config.channelId) {
        return `${timeText} https://discord.com/channels/@me/${config.channelId}/${config.lastBackupMessageId}`;
    }

    return timeText;
}

/**
 * Build frequency selection menu (FIXED: Show current selection as selected)
 */
function buildFrequencySelect(currentFrequency = 1) {
    const options = [
        { label: 'daily', value: '1', description: 'Backup every day', emoji: '📅', default: currentFrequency == 1 },
        { label: 'every 3 days', value: '3', description: 'Backup every 3 days', emoji: '📆', default: currentFrequency == 3 },
        { label: 'weekly', value: '7', description: 'Backup every 7 days', emoji: '🗓️', default: currentFrequency == 7 }
    ];

    return new StringSelectMenuBuilder()
        .setCustomId('backup-frequency')
        .setPlaceholder('select backup frequency')
        .addOptions(options);
}

/**
 * Build channel selection menu (FIXED: Show current selection as selected)
 */
function buildChannelSelect(currentChannelId = null) {
    const { ChannelSelectMenuBuilder } = require('discord.js');

    const channelSelect = new ChannelSelectMenuBuilder()
        .setCustomId('backup-channel')
        .setPlaceholder('select backup channel')
        .setChannelTypes([0]) // Text channels only
        .setMinValues(1)
        .setMaxValues(1);

    // Set default channel if one is currently selected
    if (currentChannelId) {
        channelSelect.setDefaultChannels(currentChannelId);
    }

    return channelSelect;
}



/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Perform MongoDB backup
 */
async function performBackup(client, channelId, manual = false) {
    const startTime = Date.now();

    try {
        if (backupMetrics.verboseLogging) {
            console.log('[owner-backup] Starting backup process...');
        }

        // Create backup directory if it doesn't exist
        const backupDir = path.join(process.cwd(), 'backups');
        try {
            await fs.access(backupDir);
        } catch {
            await fs.mkdir(backupDir, { recursive: true });
        }

        // Generate backup filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(backupDir, `seventeen_bot_backup_${timestamp}.gz`);

        // Get MongoDB connection URI
        const mongoUri = process.env.MONGO;
        if (!mongoUri) {
            throw new Error('MONGO environment variable not set');
        }

        // Check if mongodump is available before attempting backup
        const mongodumpExecutable = getMongodumpExecutable();
        const isMongodumpAvailable = await checkMongodumpAvailable();

        if (!isMongodumpAvailable) {
            const platform = os.platform();
            let installInstructions = '';

            if (platform === 'win32') {
                installInstructions = 'Install MongoDB Database Tools from https://www.mongodb.com/try/download/database-tools';
            } else if (platform === 'darwin') {
                installInstructions = 'Install with: brew install mongodb/brew/mongodb-database-tools';
            } else {
                installInstructions = 'Install MongoDB Database Tools for your Linux distribution';
            }

            throw new Error(`mongodump not found. ${installInstructions}`);
        }

        // Perform MongoDB dump (cross-platform compatible)
        await new Promise((resolve, reject) => {
            const mongodump = spawn(mongodumpExecutable, [
                '--uri=' + mongoUri,
                '--archive=' + backupFile,
                '--gzip'
            ]);

            let stderr = '';
            let stdout = '';

            // Capture stderr and stdout for better error reporting
            mongodump.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            mongodump.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            mongodump.on('close', (code) => {
                if (code === 0) {
                    if (backupMetrics.verboseLogging && stdout) {
                        console.log('[owner-backup] mongodump output:', stdout);
                    }
                    resolve();
                } else {
                    const errorMessage = stderr || stdout || `mongodump exited with code ${code}`;
                    console.error('[owner-backup] mongodump stderr:', stderr);
                    console.error('[owner-backup] mongodump stdout:', stdout);
                    reject(new Error(`mongodump failed: ${errorMessage}`));
                }
            });

            mongodump.on('error', (error) => {
                reject(new Error(`mongodump spawn error: ${error.message}`));
            });
        });

        // Get file size
        const stats = await fs.stat(backupFile);
        const fileSize = stats.size;

        // Upload to Discord
        const channel = await client.channels.fetch(channelId);
        if (!channel) {
            throw new Error('Backup channel not found');
        }

        const backupType = manual ? 'Manual' : 'Scheduled';
        const backupTimestamp = Math.floor(Date.now() / 1000);
        const durationSeconds = Math.round((Date.now() - startTime) / 1000);

        // Build message content - hide duration if less than 1 second
        let messageContent = `🔄 **${backupType} Database Backup**\n` +
                           `📅 **Date:** <t:${backupTimestamp}:F>\n` +
                           `📊 **Size:** ${formatFileSize(fileSize)}`;

        if (durationSeconds >= 1) {
            messageContent += `\n⏱️ **Duration:** ${durationSeconds}s`;
        }

        const message = await channel.send({
            content: messageContent,
            files: [{
                attachment: backupFile,
                name: `seventeen_bot_backup_${timestamp}.gz`
            }]
        });

        // Update backup config
        await updateBackupConfig({
            lastBackup: new Date(),
            lastBackupMessageId: message.id,
            lastBackupUrl: message.url
        });

        // Update metrics
        backupMetrics.totalBackups++;
        backupMetrics.successfulBackups++;
        backupMetrics.lastBackupTime = Date.now();
        backupMetrics.lastBackupSize = fileSize;

        // Update average backup time
        const duration = Date.now() - startTime;
        if (backupMetrics.averageBackupTime === 0) {
            backupMetrics.averageBackupTime = duration;
        } else {
            backupMetrics.averageBackupTime = (backupMetrics.averageBackupTime + duration) / 2;
        }

        // Clean up local file
        try {
            await fs.unlink(backupFile);
        } catch (error) {
            console.warn('[owner-backup] Warning: Could not delete local backup file:', error.message);
        }

        if (backupMetrics.verboseLogging) {
            console.log(`[owner-backup] ✅ Backup completed successfully in ${Math.round(duration / 1000)}s`);
        }

        return {
            success: true,
            messageId: message.id,
            messageUrl: message.url,
            fileSize: fileSize,
            duration: duration
        };

    } catch (error) {
        console.error('[owner-backup] ❌ Backup failed:', error);

        // Update metrics
        backupMetrics.totalBackups++;
        backupMetrics.failedBackups++;

        throw error;
    }
}

/**
 * Get next backup time based on midnight UTC (like starfall)
 * @param {number} frequency - Backup frequency in days
 * @param {Date} lastBackup - Last backup date
 * @returns {number} Unix timestamp for next backup
 */
function getNextBackupTimestamp(frequency, lastBackup = null) {
    const now = new Date();

    if (!lastBackup) {
        // No previous backup, schedule for next midnight UTC
        const nextMidnight = new Date(now);
        nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
        nextMidnight.setUTCHours(0, 0, 0, 0);
        return Math.floor(nextMidnight.getTime() / 1000);
    }

    // Calculate next backup based on frequency from last backup
    const lastBackupDate = new Date(lastBackup);
    const nextBackup = new Date(lastBackupDate);
    nextBackup.setUTCDate(lastBackupDate.getUTCDate() + frequency);
    nextBackup.setUTCHours(0, 0, 0, 0);

    return Math.floor(nextBackup.getTime() / 1000);
}

/**
 * Check if backup is due (using midnight UTC timing like starfall)
 */
async function isBackupDue() {
    try {
        const config = await getBackupConfig();

        if (!config.enabled || !config.channelId) {
            return false;
        }

        if (!config.lastBackup) {
            return true; // No backup yet
        }

        const now = Math.floor(Date.now() / 1000);
        const nextBackupTime = getNextBackupTimestamp(config.frequency, config.lastBackup);

        return now >= nextBackupTime;
    } catch (error) {
        console.error('[owner-backup] Error checking backup due:', error);
        return false;
    }
}

/**
 * Initialize backup scheduler
 */
function initializeBackupScheduler(client) {
    // Check for due backups every hour
    setInterval(async () => {
        try {
            if (await isBackupDue()) {
                const config = await getBackupConfig();
                if (config.enabled && config.channelId) {
                    if (backupMetrics.verboseLogging) {
                        console.log('[owner-backup] Scheduled backup starting...');
                    }
                    await performBackup(client, config.channelId, false);
                }
            }
        } catch (error) {
            console.error('[owner-backup] Scheduled backup failed:', error);
        }
    }, 60 * 60 * 1000); // Check every hour

    if (backupMetrics.verboseLogging) {
        console.log('[owner-backup] Backup scheduler initialized');
    }
}

module.exports = {
    buildBackupContainer,
    getBackupConfig,
    updateBackupConfig,
    buildFrequencySelect,
    buildChannelSelect,
    performBackup,
    isBackupDue,
    getNextBackupTimestamp,
    initializeBackupScheduler,
    storeBackupState,
    getBackupState,
    clearBackupState,
    getMongodumpExecutable,
    checkMongodumpAvailable,
    backupMetrics
};
