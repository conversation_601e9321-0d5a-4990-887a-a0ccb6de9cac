/**
 * EXP SYSTEM FOCUSED TESTING
 * Uses shared BotTestBase for comprehensive EXP system validation
 * 
 * This test focuses specifically on the EXP system functionality,
 * including levels, text/voice EXP, and database operations.
 */

const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class ExpSystemTester extends BotTestBase {
    constructor() {
        super('ExpSystemTest');
    }

    /**
     * Test EXP system initialization and basic functionality
     */
    async testExpSystemInitialization() {
        const exp = require('../commands/utility/exp.js');
        const interaction = this.createMockInteraction(2, 'exp'); // Slash command
        interaction.options = {
            getString: (name) => name === 'subcomponent' ? 'levels' : null
        };
        
        await exp.execute(interaction);
        
        console.log(`   EXP initialization responses: ${interaction._responses.length}`);
        
        // Validate that response contains expected components
        const lastResponse = interaction._responses[interaction._responses.length - 1];
        if (lastResponse && lastResponse.options.components) {
            console.log(`   Components in response: ${lastResponse.options.components.length}`);
        }
        
        return interaction._responses.length > 0;
    }

    /**
     * Test EXP levels creation workflow
     */
    async testExpLevelsCreation() {
        const exp = require('../commands/utility/exp.js');
        
        // Test levels select menu
        const selectInteraction = this.createMockInteraction(5, 'exp-levels-select', ['create']);
        await exp.select(selectInteraction, []);
        
        console.log(`   Levels select responses: ${selectInteraction._responses.length}`);
        
        // Test level creation button
        const buttonInteraction = this.createMockInteraction(3, 'exp-create-level-exp');
        await exp.buttons(buttonInteraction);
        
        console.log(`   Level creation button responses: ${buttonInteraction._responses.length}`);
        
        return selectInteraction._responses.length > 0 && buttonInteraction._responses.length > 0;
    }

    /**
     * Test EXP modal submissions
     */
    async testExpModalSubmissions() {
        const exp = require('../commands/utility/exp.js');
        
        // Test text EXP per minute modal
        const modalInteraction = this.createMockInteraction(6, 'text-exp-per-min-modal', {
            'text-exp-per-min-input': '5'
        });
        
        await exp.modalSubmit(modalInteraction);
        
        console.log(`   Modal submission responses: ${modalInteraction._responses.length}`);
        
        // Test invalid input handling
        const invalidModalInteraction = this.createMockInteraction(6, 'text-exp-per-min-modal', {
            'text-exp-per-min-input': 'invalid'
        });
        
        try {
            await exp.modalSubmit(invalidModalInteraction);
            console.log(`   Invalid input handled gracefully`);
        } catch (error) {
            console.log(`   Invalid input error: ${error.message}`);
        }
        
        return modalInteraction._responses.length > 0;
    }

    /**
     * Test EXP database operations
     */
    async testExpDatabaseOperations() {
        const guildsCol = this.mongoClient.db('test').collection('guilds');
        const testGuildId = this.testGuild.id;
        
        // Check if guild has EXP configuration
        const guildData = await guildsCol.findOne({ id: testGuildId });
        console.log(`   Guild EXP enabled: ${guildData?.exp?.enabled || false}`);
        console.log(`   Guild EXP levels: ${guildData?.exp?.levels?.length || 0}`);
        
        // Test EXP data structure
        if (guildData?.exp) {
            const expConfig = guildData.exp;
            const hasValidStructure = typeof expConfig.enabled === 'boolean';
            console.log(`   EXP config structure valid: ${hasValidStructure}`);
            
            if (expConfig.levels && expConfig.levels.length > 0) {
                const firstLevel = expConfig.levels[0];
                console.log(`   First level: ${firstLevel.level} (${firstLevel.exp} EXP)`);
            }
        }
        
        return true;
    }

    /**
     * Test EXP caching system
     */
    async testExpCaching() {
        try {
            // Test cache retrieval using the correct function name
            const { getCachedGuildExpData } = require('../utils/expCache.js');
            const cachedData = await getCachedGuildExpData(this.testGuild.id);
            console.log(`   EXP cache retrieval successful: ${!!cachedData}`);

            if (cachedData) {
                console.log(`   Cached levels: ${cachedData.levels?.length || 0}`);
                console.log(`   Cache enabled: ${cachedData.enabled}`);
            }

            return true;
        } catch (error) {
            console.log(`   EXP cache error: ${error.message}`);
            // Cache errors are not critical for basic functionality
            return true;
        }
    }

    /**
     * Test EXP permission system
     */
    async testExpPermissions() {
        const exp = require('../commands/utility/exp.js');
        
        // Create interaction with member that has permissions
        const interaction = this.createMockInteraction(5, 'exp-levels-select', ['view']);
        interaction.member = {
            permissions: {
                has: () => true // Mock permission check
            }
        };
        
        await exp.select(interaction, []);
        
        console.log(`   Permission-based interaction responses: ${interaction._responses.length}`);
        
        // Test without permissions
        const noPermInteraction = this.createMockInteraction(5, 'exp-levels-select', ['view']);
        noPermInteraction.member = {
            permissions: {
                has: () => false // Mock no permissions
            }
        };
        
        await exp.select(noPermInteraction, []);
        
        console.log(`   No-permission interaction responses: ${noPermInteraction._responses.length}`);
        
        return interaction._responses.length > 0;
    }

    /**
     * Test EXP demo data functionality
     */
    async testExpDemoData() {
        try {
            const { getExpDemoData } = require('../utils/demoData.js');

            const demoData = getExpDemoData();
            console.log(`   Demo data levels: ${demoData.levels?.length || 0}`);
            console.log(`   Demo data enabled: ${demoData.enabled}`);

            // Validate demo data structure
            if (demoData.levels && demoData.levels.length > 0) {
                const firstLevel = demoData.levels[0];
                const hasRequiredFields = firstLevel.level !== undefined && firstLevel.exp !== undefined;
                console.log(`   Demo data structure valid: ${hasRequiredFields}`);

                return hasRequiredFields;
            }

            return demoData !== null;
        } catch (error) {
            console.log(`   Demo data error: ${error.message}`);
            return false;
        }
    }

    /**
     * Run all EXP system tests
     */
    async runAllTests() {
        console.log('🚀 Starting EXP System focused testing...\n');
        console.log('=' .repeat(60));
        console.log('TESTING EXP SYSTEM WITH REAL DATABASE');
        console.log('=' .repeat(60));

        // Define EXP-specific tests
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'EXP system initialization', test: () => this.testExpSystemInitialization() },
            { name: 'EXP levels creation', test: () => this.testExpLevelsCreation() },
            { name: 'EXP modal submissions', test: () => this.testExpModalSubmissions() },
            { name: 'EXP database operations', test: () => this.testExpDatabaseOperations() },
            { name: 'EXP caching system', test: () => this.testExpCaching() },
            { name: 'EXP permission system', test: () => this.testExpPermissions() },
            { name: 'EXP demo data', test: () => this.testExpDemoData() }
        ];

        // Run all tests using the base class helper
        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        // Print comprehensive results
        this.printResults();
        
        // Return overall success status
        const passedTests = this.testResults.filter(r => r.passed).length;
        return passedTests === this.testResults.length;
    }
}

// Main execution function
async function runExpSystemTest() {
    const tester = new ExpSystemTester();
    
    try {
        // Validate environment
        tester.validateEnvironment();
        
        // Initialize test environment
        await tester.initialize();
        
        // Run all EXP tests
        const allPassed = await tester.runAllTests();
        
        // Exit with appropriate code
        process.exit(allPassed ? 0 : 1);
        
    } catch (error) {
        console.error('❌ EXP system test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Run if this file is executed directly
if (require.main === module) {
    runExpSystemTest();
}

module.exports = { ExpSystemTester, runExpSystemTest };
