/**
 * Debug Inventory Leaderboard Results
 * Check what's actually in the inventory items
 */

require('dotenv').config();
const { BotTestBase } = require('../tests/shared/BotTestBase.js');

class InventoryLeaderboardDebugger extends BotTestBase {
    constructor() {
        super('InventoryLeaderboardDebugger');
    }

    async debugInventoryItems() {
        try {
            console.log('🔍 Debugging inventory items for leaderboard results...');
            
            // Get user's inventory
            const { getUserInventory } = require('../utils/itemDropsHybrid.js');
            const inventory = await getUserInventory(process.env.OWNER, process.env.GUILDIDTWO);
            
            console.log(`   Found ${inventory.length} items in inventory`);
            
            // Check the first few items in detail
            for (let i = 0; i < Math.min(5, inventory.length); i++) {
                const item = inventory[i];
                console.log(`\n   Item ${i + 1}: ${item.itemName}`);
                console.log(`   - ID: ${item._id}`);
                console.log(`   - Dropped: ${item.droppedAt}`);
                console.log(`   - Has leaderboardResults: ${!!item.leaderboardResults}`);
                
                if (item.leaderboardResults) {
                    console.log(`   - Guild ranks: ${JSON.stringify(item.leaderboardResults.guildRanks)}`);
                    console.log(`   - Global ranks: ${JSON.stringify(item.leaderboardResults.globalRanks)}`);
                    
                    if (item.leaderboardResults.guildRanks?._item_discovery) {
                        const discovery = item.leaderboardResults.guildRanks._item_discovery;
                        console.log(`   - Guild discovery: ${discovery.discoveryRank || discovery.rank}/${discovery.total}`);
                    }
                    
                    if (item.leaderboardResults.globalRanks?._item_discovery) {
                        const discovery = item.leaderboardResults.globalRanks._item_discovery;
                        console.log(`   - Global discovery: ${discovery.discoveryRank || discovery.rank}/${discovery.total}`);
                    }
                } else {
                    console.log(`   - No leaderboard results stored`);
                }
            }
            
            // Check if any items have leaderboard results
            const itemsWithLeaderboard = inventory.filter(item => item.leaderboardResults);
            console.log(`\n   Items with leaderboard results: ${itemsWithLeaderboard.length}/${inventory.length}`);
            
            // Check recent items (last 10 to find today's items)
            console.log('\n   🕒 Checking most recent items:');
            const recentItems = inventory.slice(0, 10);
            for (const item of recentItems) {
                const droppedDate = new Date(item.droppedAt);
                const today = new Date();
                const isToday = droppedDate.toDateString() === today.toDateString();
                console.log(`   - ${item.itemName} (${item.droppedAt}) ${isToday ? '🆕 TODAY' : '📅 OLD'}: ${item.leaderboardResults ? 'HAS' : 'NO'} leaderboard results`);
            }
            
            return true;
        } catch (error) {
            console.error('❌ Debug failed:', error);
            return false;
        }
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Debug inventory items', test: () => this.debugInventoryItems() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        return passed === total;
    }
}

// Main execution function
async function runInventoryLeaderboardDebug() {
    const tester = new InventoryLeaderboardDebugger();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        await tester.runAllTests();
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runInventoryLeaderboardDebug();
}

module.exports = { InventoryLeaderboardDebugger };
