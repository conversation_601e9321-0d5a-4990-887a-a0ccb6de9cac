#!/usr/bin/env python3
"""
Whisper Audio Transcriber

A simple script to transcribe audio files using OpenAI's Whisper model.
Supports various audio formats and provides multiple output options.
"""

import argparse
import os
import sys
import time
from pathlib import Path
import json
import glob

# Set FFmpeg path if provided by Node.js
if 'FFMPEG_BINARY' in os.environ:
    os.environ['PATH'] = os.path.dirname(os.environ['FFMPEG_BINARY']) + os.pathsep + os.environ['PATH']

def load_model(model_name="base"):
    """Load the Whisper model."""
    print(f"Loading Whisper model: {model_name}")

    # Check if model is already cached
    import whisper
    cache_dir = os.path.expanduser("~/.cache/whisper")
    model_path = os.path.join(cache_dir, f"{model_name}.pt")

    if os.path.exists(model_path):
        print(f"Using cached model: {model_path}")
    else:
        print(f"Model not cached, will download to: {cache_dir}")
        # Ensure cache directory exists
        os.makedirs(cache_dir, exist_ok=True)

    try:
        model = whisper.load_model(model_name)
        print("Model loaded successfully!")
        return model
    except ImportError:
        print("Error: OpenAI Whisper is not installed.")
        print("Please install it with: pip install openai-whisper")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)

def format_timestamp(seconds):
    """Format seconds to SRT timestamp format."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    milliseconds = int((seconds % 1) * 1000)
    seconds = int(seconds)
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

def expand_audio_files(file_patterns):
    """Expand file patterns and filter for audio files."""
    audio_extensions = {'.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac', '.webm', '.mp4'}
    audio_files = []
    
    for pattern in file_patterns:
        if os.path.isfile(pattern):
            # Direct file
            if Path(pattern).suffix.lower() in audio_extensions:
                audio_files.append(pattern)
        else:
            # Pattern matching
            matches = glob.glob(pattern)
            for match in matches:
                if os.path.isfile(match) and Path(match).suffix.lower() in audio_extensions:
                    audio_files.append(match)
    
    return sorted(list(set(audio_files)))

def get_output_path(input_file, output_dir, format_type):
    """Generate output file path."""
    input_path = Path(input_file)
    
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        output_file = output_path / f"{input_path.stem}.{format_type}"
    else:
        output_file = input_path.parent / f"{input_path.stem}.{format_type}"
    
    return str(output_file)

def transcribe_audio(model, audio_path, language=None, task="transcribe"):
    """Transcribe an audio file."""
    print(f"Transcribing: {audio_path}")
    
    try:
        # Transcribe the audio
        result = model.transcribe(
            audio_path,
            language=language,
            task=task,
            verbose=True
        )
        return result
    except Exception as e:
        print(f"Error transcribing {audio_path}: {e}")
        return None

def save_transcription(result, output_path, format_type="txt"):
    """Save transcription to file in specified format."""
    try:
        if format_type == "txt":
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(result["text"])
        
        elif format_type == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
        
        elif format_type == "srt":
            # Create SRT subtitle format
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, segment in enumerate(result["segments"], 1):
                    start_time = format_timestamp(segment["start"])
                    end_time = format_timestamp(segment["end"])
                    text = segment["text"].strip()
                    
                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{text}\n\n")
        
        print(f"Saved transcription: {output_path}")
        
    except Exception as e:
        print(f"Error saving transcription: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="Transcribe audio files using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python transcriber.py audio.mp3
  python transcriber.py audio.wav --model large --language en
  python transcriber.py audio.m4a --format json --output ./transcriptions/
  python transcriber.py *.mp3 --format srt
        """
    )
    
    parser.add_argument(
        "audio_files",
        nargs="+",
        help="Audio file(s) to transcribe"
    )
    
    parser.add_argument(
        "--model",
        default="base",
        choices=["tiny", "base", "small", "medium", "large"],
        help="Whisper model to use (default: base)"
    )
    
    parser.add_argument(
        "--language",
        help="Language of the audio (auto-detect if not specified)"
    )
    
    parser.add_argument(
        "--task",
        default="transcribe",
        choices=["transcribe", "translate"],
        help="Task to perform (default: transcribe)"
    )
    
    parser.add_argument(
        "--format",
        default="txt",
        choices=["txt", "json", "srt"],
        help="Output format (default: txt)"
    )
    
    parser.add_argument(
        "--output",
        help="Output directory (default: same as input file)"
    )
    
    args = parser.parse_args()
    
    # Load the model once
    model = load_model(args.model)

    # Expand file patterns and get audio files
    audio_files = expand_audio_files(args.audio_files)

    if not audio_files:
        print("No audio files found to process!")
        sys.exit(1)

    print(f"Found {len(audio_files)} audio file(s) to process")

    # Track processing statistics
    successful = 0
    failed = 0
    total_time = 0

    # Process each audio file
    for i, audio_file in enumerate(audio_files, 1):
        if not os.path.exists(audio_file):
            print(f"Warning: File not found: {audio_file}")
            failed += 1
            continue

        print(f"\n{'='*50}")
        print(f"Processing ({i}/{len(audio_files)}): {audio_file}")
        print(f"{'='*50}")
        
        start_time = time.time()
        
        # Transcribe the audio
        result = transcribe_audio(
            model, 
            audio_file, 
            language=args.language,
            task=args.task
        )
        
        if result:
            # Generate output path
            output_path = get_output_path(
                audio_file, 
                args.output, 
                args.format
            )
            
            # Save transcription
            save_transcription(result, output_path, args.format)
            
            # Show timing info
            elapsed_time = time.time() - start_time
            total_time += elapsed_time
            successful += 1
            print(f"Completed in {elapsed_time:.2f} seconds")

            # Show detected language if auto-detected
            if not args.language and "language" in result:
                print(f"Detected language: {result['language']}")

        else:
            print(f"Failed to transcribe: {audio_file}")
            failed += 1

    # Show final statistics
    print(f"\n{'='*50}")
    print("TRANSCRIPTION SUMMARY")
    print(f"{'='*50}")
    print(f"Total files processed: {len(audio_files)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total processing time: {total_time:.2f} seconds")
    
    if successful > 0:
        print(f"Average time per file: {total_time/successful:.2f} seconds")

if __name__ == "__main__":
    main()
