/**
 * COMPREHENSIVE BOT INTERACTION TESTING
 * This test actually logs in as the bot and clicks buttons/selects to test real functionality
 * 
 * This approach:
 * 1. Logs in as the bot using Discord.js Client
 * 2. Finds the test guild and user
 * 3. Creates mock interactions that match real Discord API structure
 * 4. Calls the actual handler functions with real interaction objects
 * 5. Catches and reports any errors that would occur in production
 */

const { Client, GatewayIntentBits, ButtonInteraction, StringSelectMenuInteraction, MessageFlags } = require('discord.js');

// Test configuration
const TEST_CONFIG = {
    GUILD_ID: '417175807795134475', // Your test guild
    USER_ID: '97757532835033088',   // Your user ID (bot owner)
    CHANNEL_ID: null, // Will be set dynamically
    BOT_TOKEN: process.env.TOKEN
};

class BotInteractionTester {
    constructor() {
        this.client = null;
        this.testGuild = null;
        this.testUser = null;
        this.testChannel = null;
        this.results = [];
    }

    async initialize() {
        console.log('🤖 Initializing bot client for testing...');
        
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ]
        });

        return new Promise((resolve, reject) => {
            this.client.once('ready', async () => {
                console.log(`✅ Bot logged in as ${this.client.user.tag}`);
                
                try {
                    // Get test guild and user
                    this.testGuild = await this.client.guilds.fetch(TEST_CONFIG.GUILD_ID);
                    this.testUser = await this.client.users.fetch(TEST_CONFIG.USER_ID);
                    this.testChannel = this.testGuild.channels.cache.find(c => c.type === 0); // Text channel
                    
                    console.log(`✅ Test guild: ${this.testGuild.name}`);
                    console.log(`✅ Test user: ${this.testUser.username}`);
                    console.log(`✅ Test channel: ${this.testChannel?.name || 'Not found'}`);
                    
                    resolve();
                } catch (error) {
                    reject(error);
                }
            });

            this.client.login(TEST_CONFIG.BOT_TOKEN).catch(reject);
        });
    }

    // Create a mock interaction that matches Discord's structure
    createMockInteraction(type, customId, values = null) {
        const baseInteraction = {
            id: `test-interaction-${Date.now()}`,
            type: type, // 3 = Button, 5 = StringSelect
            customId: customId,
            user: this.testUser,
            member: null, // Will be set if needed
            guild: this.testGuild,
            channel: this.testChannel,
            client: this.client,
            token: 'mock-token',
            version: 1,
            applicationId: this.client.user.id,
            replied: false,
            deferred: false,
            ephemeral: null,
            webhook: null,
            
            // Mock the reply method
            reply: async (options) => {
                console.log(`📤 Mock Reply Called:`);
                console.log(`   Content: ${options.content || 'No content'}`);
                console.log(`   Flags: ${options.flags || 'None'}`);
                console.log(`   Components: ${options.components?.length || 0} components`);
                console.log(`   Ephemeral: ${options.ephemeral || false}`);
                
                // Validate Components v2 usage
                if (options.flags && (options.flags & MessageFlags.IsComponentsV2)) {
                    if (options.content) {
                        throw new Error('Cannot use content field with MessageFlags.IsComponentsV2');
                    }
                    console.log(`   ✅ Components v2 validation passed`);
                }
                
                this.replied = true;
                return { id: 'mock-message-id' };
            },
            
            // Mock the followUp method
            followUp: async (options) => {
                console.log(`📤 Mock FollowUp Called:`);
                console.log(`   Content: ${options.content || 'No content'}`);
                console.log(`   Components: ${options.components?.length || 0} components`);
                
                if (options.flags && (options.flags & MessageFlags.IsComponentsV2)) {
                    if (options.content) {
                        throw new Error('Cannot use content field with MessageFlags.IsComponentsV2');
                    }
                    console.log(`   ✅ Components v2 validation passed`);
                }
                
                return { id: 'mock-followup-id' };
            },
            
            // Mock the update method
            update: async (options) => {
                console.log(`📤 Mock Update Called:`);
                console.log(`   Components: ${options.components?.length || 0} components`);
                return { id: 'mock-update-id' };
            }
        };

        // Add values for select menu interactions
        if (values) {
            baseInteraction.values = values;
        }

        return baseInteraction;
    }

    async testOwnerItemsButton() {
        console.log('\n🧪 Testing owner.js items button...');
        
        try {
            // Create mock interaction for owner features select
            const ownerSelectInteraction = this.createMockInteraction(5, 'owner-features-select', ['items']);
            
            // Import and test the owner.js select handler
            const owner = require('../commands/utility/owner.js');
            await owner.select(ownerSelectInteraction, []);
            
            console.log('✅ Owner items select completed');
            return true;
            
        } catch (error) {
            console.log('❌ Owner items select failed:');
            console.log(`   ${error.message}`);
            return false;
        }
    }

    async testNotificationViewsButton() {
        console.log('\n🧪 Testing notification views button...');
        
        try {
            // Create mock interaction for the test button
            const buttonInteraction = this.createMockInteraction(3, 'show-notification-views');
            
            // Import and test the show_notifications function
            const { showNotificationViews } = require('./show_notifications.js');
            await showNotificationViews(buttonInteraction);
            
            console.log('✅ Notification views button completed');
            return true;
            
        } catch (error) {
            console.log('❌ Notification views button failed:');
            console.log(`   ${error.message}`);
            console.log(`   Stack: ${error.stack}`);
            return false;
        }
    }

    async testItemsCreationFlow() {
        console.log('\n🧪 Testing items creation flow...');
        
        try {
            // Test items page navigation
            const itemsPageInteraction = this.createMockInteraction(3, 'items-page-next');
            const items = require('../commands/utility/items.js');
            await items.buttons(itemsPageInteraction, []);
            
            console.log('✅ Items page navigation completed');
            return true;
            
        } catch (error) {
            console.log('❌ Items creation flow failed:');
            console.log(`   ${error.message}`);
            return false;
        }
    }

    async runAllTests() {
        console.log('🚀 Starting comprehensive bot interaction tests...\n');
        console.log('=' .repeat(70));
        console.log('TESTING REAL BOT INTERACTIONS WITH ACTUAL HANDLERS');
        console.log('=' .repeat(70));

        const tests = [
            { name: 'Owner items button', test: () => this.testOwnerItemsButton() },
            { name: 'Notification views button', test: () => this.testNotificationViewsButton() },
            { name: 'Items creation flow', test: () => this.testItemsCreationFlow() }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.results.push({ name: test.name, passed: result });
            } catch (error) {
                console.log(`❌ Test "${test.name}" threw an error:`);
                console.log(`   ${error.message}`);
                this.results.push({ name: test.name, passed: false });
            }
        }

        this.printResults();
    }

    printResults() {
        console.log('\n📊 Comprehensive Test Results:');
        console.log('=' .repeat(70));

        let totalTests = this.results.length;
        let passedTests = this.results.filter(r => r.passed).length;

        this.results.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.name}`);
        });

        console.log('=' .repeat(70));
        console.log(`Overall: ${passedTests}/${totalTests} tests passed`);

        if (passedTests === totalTests) {
            console.log('🎯 ALL TESTS PASSED! Bot interactions work correctly.');
        } else {
            console.log('⚠️  Some tests failed! Check the errors above.');
        }
    }

    async cleanup() {
        if (this.client) {
            await this.client.destroy();
            console.log('🔌 Bot client disconnected');
        }
    }
}

// Main execution
async function runComprehensiveTest() {
    const tester = new BotInteractionTester();
    
    try {
        await tester.initialize();
        await tester.runAllTests();
    } catch (error) {
        console.error('❌ Test initialization failed:', error);
    } finally {
        await tester.cleanup();
        process.exit(0);
    }
}

// Run if this file is executed directly
if (require.main === module) {
    runComprehensiveTest();
}

module.exports = { BotInteractionTester, runComprehensiveTest };
