/**
 * Debug script to clear item-related caches and fix data inconsistency issues
 * Run this script if you encounter server count display bugs like "3rd/2 server"
 */

require('dotenv').config();
const { clearLiveTotalsCache } = require('../commands/utility/items.js');

async function clearItemCaches() {
    console.log('🔧 Clearing item-related caches to fix data inconsistency...');
    
    try {
        // Clear live totals cache
        clearLiveTotalsCache();
        
        console.log('✅ Successfully cleared item caches');
        console.log('💡 Server count display should now be consistent');
        
    } catch (error) {
        console.error('❌ Error clearing caches:', error);
    }
}

// Run if called directly
if (require.main === module) {
    clearItemCaches().then(() => {
        console.log('🏁 Cache clearing complete');
        process.exit(0);
    }).catch(error => {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { clearItemCaches };
