const { ContextMenuCommandBuilder, ApplicationCommandType, ContainerBuilder, SectionBuilder, TextDisplayBuilder, ThumbnailBuilder, SeparatorBuilder, SeparatorSpacingSize, MessageFlags } = require('discord.js');
const { optimizedFindOne } = require("../../utils/database-optimizer.js");
const { getUserGuildRank, getUserGlobalRank } = require("../../utils/expRanking.js");
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');
const { incrementCommandUsage } = require("../../utils/commandUsage.js");
const { getCachedUserGlobalInventory } = require("../../utils/itemCache.js");
const { formatDuration, getVoiceExpGuildRank, getTextExpGuildRank, getVoiceExpGlobalRank, getTextExpGlobalRank, calculateAvgPerDay } = require("../../utils/statsUtils.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { getStarfallData } = require('../../utils/starfall.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');
const { checkNonToggleFeaturePermissions } = require('../../utils/permissionHandler.js');

/**
 * Lookup Command System (Enterprise-Grade Performance Optimized)
 * Handles user profile lookups with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 * Based on you.js structure but for looking up other users
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const lookupMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    profilesProcessed: 0,
    expCalculationsProcessed: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const memberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member data
const expDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for experience data

// Register caches for global cleanup
registerCache(memberDataCache);
registerCache(expDataCache);



module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('peep')
        .setType(ApplicationCommandType.User),

    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            try {
            // Check bot permissions first
            const permissionCheck = checkNonToggleFeaturePermissions(interaction.guild, 'lookup');
            if (permissionCheck && !permissionCheck.canFunction) {
                console.log(`[lookup] Bot missing critical permissions:`, permissionCheck.missingPermissions);
                return new ContainerBuilder()
                    .addTextDisplayComponents(permissionCheck.statusMessage)
                    .setAccentColor(OPERATION_COLORS.DELETE);
            }

            // Track command usage
            await incrementCommandUsage('lookup');

            console.log('[lookup] Context menu command triggered for user:', interaction.targetUser?.tag);

            const targetUser = interaction.targetUser;
            const targetMember = interaction.targetMember;

            // Show main lookup page (initial command)
            return await this.showMainPage(interaction, targetUser, targetMember, true);
            } catch (error) {
                console.error('[lookup] Error in execute:', error);
                return new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder()
                        .setContent('**status:** ❌ Something went wrong looking up this user. Please try again.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
            }
        }, {
            autoDefer: false,
            ephemeral: true,
            fallbackMessage: '❌ Something went wrong looking up this user. Please try again.'
        });
    },

    // Show simplified main lookup page - OPTIMIZED VERSION (based on you.js)
    async showMainPage(interaction, targetUser, targetMember, isInitialCommand = false) {
        const startTime = Date.now();

        try {
            // OPTIMIZATION 1: Parallel database queries instead of sequential
            const [guildExpData, globalExpData] = await Promise.all([
                this.getOptimizedGuildExpData(targetUser.id, interaction.guild.id),
                this.getOptimizedGlobalExpData(targetUser.id)
            ]);

            // Extract guild data
            const { guildExp, guildLevel, guildNextLevelExp, guildRole, guildRoleIcon } = guildExpData;

            // Extract global data
            const { globalExp, globalLevel, globalNextLevelExp, globalLevelName, globalLevelIcon, prestigeLevel } = globalExpData;

            // OPTIMIZATION: Performance logging
            const loadTime = Date.now() - startTime;
            if (loadTime > 1000) {
                console.log(`[lookup] PERFORMANCE: Main page loaded in ${loadTime}ms (SLOW)`);
            } else {
                console.log(`[lookup] PERFORMANCE: Main page loaded in ${loadTime}ms`);
            }

            // Build UI components (simplified - no select menu)

            // Build thumbnail section with title and quote
            let titleText = '';
            if (guildRole) {
                titleText = `## ${guildRoleIcon} ${guildRole} | ${globalLevelIcon} ${globalLevelName}`;
            } else {
                titleText = `## ${globalLevelIcon} ${globalLevelName}`;
            }

            // Show server context only if user has server EXP data (indicated by having a guild role)
            const quoteText = guildRole ? `${targetUser.username} in ${interaction.guild.name} and globally` : `${targetUser.username} globally`;

            const profileImage = new ThumbnailBuilder({
                media: { url: targetUser.displayAvatarURL({ forceStatic: false }) }
            });

            const titleTextDisplay = new TextDisplayBuilder().setContent(titleText);
            const quoteTextDisplay = new TextDisplayBuilder().setContent(`> ${quoteText}`);

            const titleSection = new SectionBuilder()
                .setThumbnailAccessory(profileImage)
                .addTextDisplayComponents(titleTextDisplay, quoteTextDisplay);

            // Level display with proper X/MAX format (accounting for prestige)
            // Import MAX_LEVELS from exp.js to get the correct guild level limit
            const { MAX_LEVELS } = require('./exp.js');
            const guildMaxLevel = MAX_LEVELS; // Guild levels are capped at MAX_LEVELS (excluding Level 0)

            // Get dynamic global max level from database and prestige info
            const { getCachedGlobalLevels, getUserBoosters } = require('../../utils/globalLevels.js');
            const { getPrestigeDisplayText } = require('../../utils/prestigeUI.js');
            const globalLevels = await getCachedGlobalLevels();
            // CRITICAL FIX: Global levels are unlimited, show ∞ instead of finite number
            const globalMaxLevel = '∞';

            // Get user's current multiplier for display enhancement
            const userBoosters = await getUserBoosters(targetUser.id);

            // Get prestige display text
            const prestigeText = getPrestigeDisplayText(prestigeLevel || 0);

            // Build level content using modern format
            let levelContent = '';

            if (guildRole) {
                levelContent += `**level:** ${guildLevel}/${guildMaxLevel} | ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;

                // Enhanced progress display with multiplier (Task 4: EXP Display Enhancement)
                const guildProgressText = guildNextLevelExp === null ? 'max' : `${guildExp.toLocaleString()}/${guildNextLevelExp.toLocaleString()}`;
                const globalProgressText = globalNextLevelExp === null ? 'max' : `${globalExp.toLocaleString()}/${globalNextLevelExp.toLocaleString()}`;

                // Add multiplier display if user is not at max level and has active multiplier
                const multiplierText = (userBoosters.expMultiplier > 1.0 && globalNextLevelExp !== null)
                    ? ` (x${parseFloat(userBoosters.expMultiplier.toFixed(1))})`
                    : '';

                levelContent += `**progress:** ${guildProgressText} | ${globalProgressText}${multiplierText}\n`;

                levelContent += `**exp:** ${guildExp.toLocaleString()} | ${globalExp.toLocaleString()}\n`;
            } else {
                levelContent += `**level:** ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;

                // Enhanced progress display with multiplier (Task 4: EXP Display Enhancement)
                const globalProgressText = globalNextLevelExp === null ? 'max' : `${globalExp.toLocaleString()}/${globalNextLevelExp.toLocaleString()}`;

                // Add multiplier display if user is not at max level and has active multiplier
                const multiplierText = (userBoosters.expMultiplier > 1.0 && globalNextLevelExp !== null)
                    ? ` (x${parseFloat(userBoosters.expMultiplier.toFixed(1))})`
                    : '';

                levelContent += `**progress:** ${globalProgressText}${multiplierText}\n`;

                levelContent += `**exp:** ${globalExp.toLocaleString()}\n`;
            }

            // Get starfall data for stars display
            const starfallData = await getStarfallData(targetUser.id);
            levelContent += `**stars:** ${starfallData.stars.toLocaleString()}`;

            const levelDisplay = new TextDisplayBuilder().setContent(levelContent);

            // Large separator
            const largeSeparator = new SeparatorBuilder()
                .setSpacing(SeparatorSpacingSize.Large)
                .setDivider(true);

            // Build inventory preview content (NO SELECT MENUS - just preview)
            const inventoryContent = await this.buildInventoryPreview(targetUser);

            // Build container (simplified - no select menu)
            const container = new ContainerBuilder()
                .addSectionComponents(titleSection)
                .addTextDisplayComponents(levelDisplay)
                .addSeparatorComponents(largeSeparator);

            // Add inventory preview
            if (inventoryContent) {
                container.addTextDisplayComponents(inventoryContent);
            }

            container.setAccentColor(OPERATION_COLORS.ENTITY);

            // Return components (no notification center for lookup)
            return [container];

        } catch (error) {
            console.error('[lookup] Error in showMainPage:', error);

            // Simplified error container
            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent(`## ${targetUser.username}\n\n**status:** ❌ Something went wrong loading this profile. Please try again.`))
                .setAccentColor(OPERATION_COLORS.ENTITY);

            return [container];
        }
    },

    // Get optimized guild EXP data (EXACTLY like you.js - 1:1)
    async getOptimizedGuildExpData(userId, guildId) {
        try {
            const { optimizedFindOne } = require('../../utils/database-optimizer.js');

            // OPTIMIZATION: Parallel queries for member and guild data
            const [memberData, guildData] = await Promise.all([
                optimizedFindOne('member', { userId: userId, guildId: guildId }),
                optimizedFindOne('guilds', { id: guildId })
            ]);

            let guildExp = 0, guildLevel = 0, guildNextLevelExp = null;
            let guildRole = null, guildRoleIcon = '';

            if (memberData?.exp?.total !== undefined && guildData?.exp?.levels) {
                guildExp = memberData.exp.total;

                // Calculate level from EXP (cached) (exactly like you.js)
                const { getCachedLevelCalculation } = require('../../utils/expCache.js');
                const levels = guildData.exp.levels;
                const levelCalc = getCachedLevelCalculation(guildExp, levels);

                guildLevel = levelCalc.currentLevel;
                guildNextLevelExp = levelCalc.nextLevelExp;

                if (levelCalc.levelIndex >= 0) {
                    const currentLevelData = levels[levelCalc.levelIndex];
                    guildRole = currentLevelData.roleId ? `<@&${currentLevelData.roleId}>` : null;
                    // CRITICAL FIX: Remove default seedling emoji - show no icon when none is set (exactly like you.js)
                    guildRoleIcon = currentLevelData.levelIcon || '';
                }
            }

            return { guildExp, guildLevel, guildNextLevelExp, guildRole, guildRoleIcon };

        } catch (error) {
            console.error('[lookup] Error in getOptimizedGuildExpData:', error);
            return { guildExp: 0, guildLevel: 0, guildNextLevelExp: null, guildRole: null, guildRoleIcon: '' };
        }
    },

    // Get optimized global EXP data (EXACTLY like you.js - 1:1)
    async getOptimizedGlobalExpData(userId) {
        try {
            // OPTIMIZATION: Use cached functions that are already optimized (exactly like you.js)
            const { getCachedGlobalUser, getCachedGlobalLevels, calculateGlobalLevel } = require('../../utils/globalLevels.js');

            // These are already cached and optimized (exactly like you.js)
            const [globalUserData, globalLevels] = await Promise.all([
                getCachedGlobalUser(userId),
                getCachedGlobalLevels()
            ]);

            const globalExp = globalUserData.globalExp;
            const levelCalc = calculateGlobalLevel(globalExp, globalUserData.prestigeLevel, globalLevels);
            const globalLevel = levelCalc.currentLevel;
            const globalNextLevelExp = levelCalc.nextLevelExp;

            // CRITICAL FIX: Use consistent level naming instead of hardcoded "Beginner" (exactly like you.js)
            let globalLevelName = `Level ${globalLevel}`;
            // CRITICAL FIX: Remove default seedling emoji - show no icon when none is set (exactly like you.js)
            let globalLevelIcon = '';

            if (levelCalc.levelIndex >= 0 && globalLevels[levelCalc.levelIndex]) {
                const levelData = globalLevels[levelCalc.levelIndex];
                globalLevelName = levelData.name;
                // CRITICAL FIX: Remove default seedling emoji - show no icon when none is set (exactly like you.js)
                globalLevelIcon = levelData.levelIcon || '';
            } else {
                // User is at level 0 - look for Level 0 definition (exactly like you.js)
                const level0 = globalLevels.find(level => level.level === 0);
                if (level0) {
                    globalLevelName = level0.name;
                    // CRITICAL FIX: Remove default seedling emoji - show no icon when none is set (exactly like you.js)
                    globalLevelIcon = level0.levelIcon || '';
                }
                // If no Level 0 is configured, use generic "Level 0" format (already set above) (exactly like you.js)
            }

            return { globalExp, globalLevel, globalNextLevelExp, globalLevelName, globalLevelIcon, prestigeLevel: globalUserData.prestigeLevel || 0 };

        } catch (error) {
            console.error('[lookup] Error in getOptimizedGlobalExpData:', error);
            // CRITICAL FIX: Use consistent level naming instead of hardcoded "Beginner" (exactly like you.js)
            return { globalExp: 0, globalLevel: 0, globalNextLevelExp: null, globalLevelName: 'Level 0', globalLevelIcon: '', prestigeLevel: 0 };
        }
    },

    // Build inventory preview (NO SELECT MENUS - just preview text like you.js)
    async buildInventoryPreview(targetUser) {
        try {
            const { RARITIES, DROP_LOCATIONS } = require('./items.js');
            const inventory = await getCachedUserGlobalInventory(targetUser.id);

            if (inventory.length === 0) {
                return new TextDisplayBuilder().setContent(`## inventory\n\n*Their inventory is empty!*`);
            }

            // Group items by drop location (exactly like you.js)
            const itemsByLocation = {};
            inventory.forEach(item => {
                const location = item.droppedFrom || 'UNKNOWN';
                if (!itemsByLocation[location]) {
                    itemsByLocation[location] = [];
                }
                itemsByLocation[location].push(item);
            });

            // Calculate accessible counts (simplified for lookup)
            const accessibleCounts = {};
            Object.keys(itemsByLocation).forEach(locationKey => {
                const locationItems = itemsByLocation[locationKey] || [];
                if (locationItems.length > 0) {
                    const uniqueItemTypes = new Set(locationItems.map(item => `${item.itemName}_${item.itemType}`));
                    accessibleCounts[locationKey] = uniqueItemTypes.size;
                }
            });

            let content = `## inventory\n\n**total items:** ${inventory.length} (across all servers)\n\n`;

            // Display items by location with proper counts (exactly like you.js)
            for (const [locationKey, locationData] of Object.entries(DROP_LOCATIONS)) {
                const locationItems = itemsByLocation[locationKey] || [];
                const accessibleCount = accessibleCounts[locationKey] || 0;

                // Only show locations that have items or accessible items
                if (locationItems.length > 0 || accessibleCount > 0) {
                    const locationName = locationData.displayName || locationData.name;
                    const uniqueItemTypes = new Set(locationItems.map(item => `${item.itemName}_${item.itemType}`));

                    content += `**${locationName}** (${uniqueItemTypes.size}/${accessibleCount}): `;

                    if (locationItems.length > 0) {
                        const itemDisplays = [];

                        // Group items by name and type to show counts (exactly like you.js)
                        const itemGroups = new Map();
                        locationItems.forEach(item => {
                            const key = `${item.itemName}_${item.itemType}`;
                            if (!itemGroups.has(key)) {
                                itemGroups.set(key, []);
                            }
                            itemGroups.get(key).push(item);
                        });

                        for (const [key, items] of itemGroups) {
                            const firstItem = items[0];
                            const emote = firstItem.itemEmote || '📦';
                            const count = items.length;
                            const subscriptCount = this.numberToSubscript(count);

                            itemDisplays.push(`${emote}${subscriptCount}`);
                        }

                        content += itemDisplays.join(' ');
                    } else {
                        content += '*none*';
                    }

                    content += '\n';
                }
            }

            return new TextDisplayBuilder().setContent(content);

        } catch (error) {
            console.error('[lookup] Error building inventory preview:', error);
            return new TextDisplayBuilder().setContent('**inventory:** error loading');
        }
    },

    // Helper function to convert numbers to Unicode subscripts (from you.js)
    numberToSubscript(num) {
        const subscriptMap = {
            '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
            '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉'
        };
        return num.toString().split('').map(digit => subscriptMap[digit] || digit).join('');
    }
};
