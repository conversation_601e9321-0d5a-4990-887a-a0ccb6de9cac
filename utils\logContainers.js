const { ContainerBuilder, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, ActionRowBuilder, MessageFlags, FileBuilder } = require('discord.js');
const { LOG_COLORS } = require('./colors.js');

/**
 * Utility functions for creating Components v2 log containers
 */

/**
 * Split text content into chunks that fit within Discord's limits
 * @param {string} content - The content to split
 * @param {number} maxLength - Maximum length per chunk (default: 4000)
 * @returns {string[]} Array of text chunks
 */
function splitTextContent(content, maxLength = 4000) {
    if (!content || content.length <= maxLength) {
        return [content || ''];
    }

    const chunks = [];
    let currentChunk = '';
    const lines = content.split('\n');

    for (const line of lines) {
        // If adding this line would exceed the limit
        if (currentChunk.length + line.length + 1 > maxLength) {
            // If we have content in current chunk, save it
            if (currentChunk) {
                chunks.push(currentChunk);
                currentChunk = '';
            }
            
            // If the line itself is too long, split it
            if (line.length > maxLength) {
                let remainingLine = line;
                while (remainingLine.length > maxLength) {
                    chunks.push(remainingLine.substring(0, maxLength));
                    remainingLine = remainingLine.substring(maxLength);
                }
                if (remainingLine) {
                    currentChunk = remainingLine;
                }
            } else {
                currentChunk = line;
            }
        } else {
            // Add line to current chunk
            if (currentChunk) {
                currentChunk += '\n' + line;
            } else {
                currentChunk = line;
            }
        }
    }

    // Add any remaining content
    if (currentChunk) {
        chunks.push(currentChunk);
    }

    return chunks.length > 0 ? chunks : [''];
}

/**
 * Create text display components from content, splitting if necessary
 * @param {string} content - The content to display
 * @param {string} prefix - Optional prefix for each chunk (e.g., "## Before")
 * @returns {TextDisplayBuilder[]} Array of text display components
 */
function createTextDisplays(content, prefix = '') {
    const chunks = splitTextContent(content);
    return chunks.map((chunk, index) => {
        let displayContent = chunk;
        if (prefix) {
            // Only add prefix to first chunk
            if (index === 0) {
                displayContent = `${prefix}\n${chunk}`;
            } else {
                // For continuation chunks, add a continuation indicator
                displayContent = `${prefix} (continued)\n${chunk}`;
            }
        }
        return new TextDisplayBuilder().setContent(displayContent);
    });
}

/**
 * Create a basic log container with title and description
 * @param {Object} options - Container options
 * @param {string} options.title - Container title
 * @param {string} options.description - Container description
 * @param {number} options.color - Container accent color (hex)
 * @param {ActionRowBuilder[]} options.actionRows - Optional action rows
 * @returns {ContainerBuilder} The container
 */
function createBasicLogContainer({ title, description, color = 0x5865F2, actionRows = [] }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# ${title}`);
    const descriptionDisplays = createTextDisplays(description);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, ...descriptionDisplays)
        .setAccentColor(color);

    // Add action rows if provided
    for (const row of actionRows) {
        container.addActionRowComponents(row);
    }

    return container;
}

/**
 * Create a message edit log container with Before/After sections
 * @param {Object} options - Message edit options
 * @param {string} options.author - Author mention/tag
 * @param {string} options.channel - Channel mention
 * @param {string} options.messageUrl - Message URL for button
 * @param {string} options.beforeContent - Content before edit
 * @param {string} options.afterContent - Content after edit
 * @returns {Object} Object containing container and buttonRow for external placement
 */
function createMessageEditContainer({ author, channel, messageUrl, beforeContent, afterContent }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# message edited');
    const infoDisplay = new TextDisplayBuilder().setContent(`**author**: ${author}\n**channel**: ${channel}`);

    // Create Before section
    const beforeDisplays = createTextDisplays(beforeContent || '`unknown`', '## Before');

    // Create After section
    const afterDisplays = createTextDisplays(afterContent || '`unknown`', '## After');

    // Create view message button (to be placed outside container)
    const viewButton = new ButtonBuilder()
        .setURL(messageUrl)
        .setLabel('view message')
        .setStyle(ButtonStyle.Link);
    const buttonRow = new ActionRowBuilder().addComponents(viewButton);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, infoDisplay, ...beforeDisplays, ...afterDisplays)
        .setAccentColor(LOG_COLORS.WARNING);

    return { container, buttonRow };
}

/**
 * Create a message delete log container
 * @param {Object} options - Message delete options
 * @param {string} options.author - Author mention/tag
 * @param {string} options.channel - Channel mention
 * @param {string} options.content - Deleted message content
 * @param {string[]} options.attachments - Array of attachment URLs
 * @returns {ContainerBuilder} The container
 */
function createMessageDeleteContainer({ author, channel, content, attachments = [] }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# message deleted');
    const infoDisplay = new TextDisplayBuilder().setContent(`**author**: ${author}\n**channel**: ${channel}`);

    // Create content section
    const contentDisplays = createTextDisplays(content || '`no content`', '## Content');

    // Create attachments section if any
    const attachmentDisplays = [];
    if (attachments.length > 0) {
        const attachmentText = attachments.join('\n');
        attachmentDisplays.push(...createTextDisplays(attachmentText, '## Attachments'));
    }

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, infoDisplay, ...contentDisplays, ...attachmentDisplays)
        .setAccentColor(LOG_COLORS.ERROR);

    return container;
}

/**
 * Create a member join log container
 * @param {Object} options - Member join options
 * @param {string} options.userMention - User mention
 * @param {string} options.userTag - User tag
 * @param {string} options.userId - User ID
 * @param {string} options.accountAge - Account creation timestamp
 * @param {number} options.memberCount - Current member count
 * @returns {ContainerBuilder} The container
 */
function createMemberJoinContainer({ userMention, userTag, userId, accountAge, memberCount }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# member joined');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention} (${userTag})\n**id**: ${userId}`);
    const detailsDisplay = new TextDisplayBuilder().setContent(`**account created**: ${accountAge}\n**member count**: ${memberCount.toLocaleString()}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, userDisplay, detailsDisplay)
        .setAccentColor(LOG_COLORS.SUCCESS);

    return container;
}

/**
 * Create a member leave log container
 * @param {Object} options - Member leave options
 * @param {string} options.userMention - User mention
 * @param {string} options.userTag - User tag
 * @param {string} options.userId - User ID
 * @param {string} options.joinedAt - When they joined timestamp
 * @param {number} options.memberCount - Current member count
 * @returns {ContainerBuilder} The container
 */
function createMemberLeaveContainer({ userMention, userTag, userId, joinedAt, memberCount }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# member left');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention} (${userTag})\n**id**: ${userId}`);
    const detailsDisplay = new TextDisplayBuilder().setContent(`**joined**: ${joinedAt}\n**member count**: ${memberCount.toLocaleString()}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, userDisplay, detailsDisplay)
        .setAccentColor(LOG_COLORS.ERROR);

    return container;
}

/**
 * Create an audit log entry container
 * @param {Object} options - Audit log options
 * @param {string} options.actionName - Human-readable action name
 * @param {string} options.executor - Executor mention/tag
 * @param {string} options.target - Target description
 * @param {string[]} options.changes - Array of change descriptions
 * @param {string} options.reason - Optional reason
 * @returns {ContainerBuilder} The container
 */
function createAuditLogContainer({ actionName, executor, target, changes = [], reason }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# audit log entry');
    const actionDisplay = new TextDisplayBuilder().setContent(`**action**: ${actionName}\n**executor**: ${executor}\n**target**: ${target}`);

    const components = [titleDisplay, actionDisplay];

    // Add changes section if any
    if (changes.length > 0) {
        const changesText = changes.join('\n');
        const changesDisplays = createTextDisplays(changesText, '## Changes');
        components.push(...changesDisplays);
    }

    // Add reason section if provided
    if (reason) {
        const reasonDisplays = createTextDisplays(reason, '## Reason');
        components.push(...reasonDisplays);
    }

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(LOG_COLORS.INFO);

    return container;
}

/**
 * Create an EXP level up container
 * @param {Object} options - Level up options
 * @param {string} options.userMention - User mention
 * @param {number} options.level - New level
 * @param {number} options.totalExp - Total EXP
 * @param {string} options.roleMention - Role mention (if assigned)
 * @returns {ContainerBuilder} The container
 */
function createExpLevelUpContainer({ userMention, level, totalExp, roleMention }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# level up');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention}\n**level**: ${level}`);
    const expDisplay = new TextDisplayBuilder().setContent(`**total exp**: ${totalExp.toLocaleString()}`);

    const components = [titleDisplay, userDisplay, expDisplay];

    // Add role section if assigned
    if (roleMention) {
        const roleDisplay = new TextDisplayBuilder().setContent(`**role assigned**: ${roleMention}`);
        components.push(roleDisplay);
    }

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(LOG_COLORS.INFO);

    return container;
}

/**
 * Create an EXP voice session container
 * @param {Object} options - Voice session options
 * @param {string} options.userMention - User mention
 * @param {number} options.expGained - EXP gained from session
 * @param {number} options.sessionDuration - Session duration in minutes
 * @param {number} [options.sessionDurationMs] - Session duration in milliseconds (for better formatting)
 * @returns {ContainerBuilder} The container
 */
function createExpVoiceSessionContainer({ userMention, expGained, sessionDuration, sessionDurationMs }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# voice exp gained');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention}`);

    // Use formatted duration if available, otherwise fall back to minutes
    let durationText;
    if (sessionDurationMs) {
        const { formatDuration } = require('./statsUtils.js');
        durationText = formatDuration(sessionDurationMs);
    } else {
        durationText = `${sessionDuration} minute${sessionDuration !== 1 ? 's' : ''}`;
    }

    const sessionDisplay = new TextDisplayBuilder().setContent(`**exp gained**: ${expGained.toLocaleString()}\n**session duration**: ${durationText}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, userDisplay, sessionDisplay)
        .setAccentColor(LOG_COLORS.INFO);

    return container;
}

/**
 * Create a feature toggle container
 * @param {Object} options - Feature toggle options
 * @param {string} options.action - 'enabled' or 'disabled'
 * @param {string} options.featureName - Name of the feature
 * @param {string} options.subcomponent - Subcomponent name (optional)
 * @param {string} options.userMention - User who triggered the action
 * @returns {ContainerBuilder} The container
 */
function createFeatureToggleContainer({ action, featureName, subcomponent, userMention }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# feature ${action}`);
    const featureDisplay = subcomponent ? `${featureName} (${subcomponent})` : featureName;
    const infoDisplay = new TextDisplayBuilder().setContent(`**feature**: ${featureDisplay}\n**admin**: ${userMention}\n**action**: ${action}`);

    const color = action === 'enabled' ? 0x00D166 : 0xED4245;
    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, infoDisplay)
        .setAccentColor(color);

    return container;
}

/**
 * Create a dehoist username container
 * @param {Object} options - Dehoist options
 * @param {string} options.userMention - User mention
 * @param {string} options.beforeName - Name before dehoist
 * @param {string} options.afterName - Name after dehoist
 * @returns {ContainerBuilder} The container
 */
function createDehoistUsernameContainer({ userMention, beforeName, afterName }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# username dehoisted');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention}`);
    const beforeDisplay = new TextDisplayBuilder().setContent(`## Before\n${beforeName}`);
    const afterDisplay = new TextDisplayBuilder().setContent(`## After\n${afterName}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, userDisplay, beforeDisplay, afterDisplay)
        .setAccentColor(LOG_COLORS.WARNING);

    return container;
}

/**
 * Create a dehoist scan completed container
 * @param {Object} options - Scan options
 * @param {string} options.adminMention - Admin who ran the scan
 * @param {number} options.processed - Number of members processed
 * @param {number} options.dehoisted - Number of members dehoisted
 * @param {number} options.failed - Number of failed operations
 * @returns {ContainerBuilder} The container
 */
function createDehoistScanContainer({ adminMention, processed, dehoisted, failed }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# dehoist scan completed');
    const adminDisplay = new TextDisplayBuilder().setContent(`**admin**: ${adminMention}`);
    const statsDisplay = new TextDisplayBuilder().setContent(`**processed**: ${processed.toLocaleString()} members\n**dehoisted**: ${dehoisted.toLocaleString()}\n**failed**: ${failed.toLocaleString()}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, adminDisplay, statsDisplay)
        .setAccentColor(LOG_COLORS.DISABLED);

    return container;
}

/**
 * Create an opener thread container
 * @param {Object} options - Thread options
 * @param {string} options.action - 'watched', 'bumped', or 'unwatched'
 * @param {string} options.threadMention - Thread mention (or name if deleted)
 * @param {string} options.threadName - Thread name
 * @param {string} options.adminMention - Admin mention (for watch/unwatch)
 * @param {string} options.reason - Reason for action (for bump)
 * @returns {ContainerBuilder} The container
 */
function createOpenerThreadContainer({ action, threadMention, threadName, adminMention, reason }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# thread ${action}`);
    const threadDisplay = new TextDisplayBuilder().setContent(`**thread**: ${threadMention}\n**name**: ${threadName}`);

    const components = [titleDisplay, threadDisplay];

    // Add admin section for watch/unwatch
    if (adminMention && (action === 'watched' || action === 'unwatched')) {
        const adminDisplay = new TextDisplayBuilder().setContent(`**admin**: ${adminMention}`);
        components.push(adminDisplay);
    }

    // Add reason section for bump
    if (reason && action === 'bumped') {
        const reasonDisplay = new TextDisplayBuilder().setContent(`**reason**: ${reason}`);
        components.push(reasonDisplay);
    }

    const colorMap = {
        'watched': LOG_COLORS.SUCCESS,
        'bumped': LOG_COLORS.WARNING,
        'unwatched': LOG_COLORS.ERROR
    };

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(colorMap[action] || LOG_COLORS.INFO);

    return container;
}

/**
 * Create a sticky recovery container
 * @param {Object} options - Sticky options
 * @param {string} options.type - 'nickname' or 'roles'
 * @param {string} options.userMention - User mention
 * @param {string} options.content - Recovered nickname or role mentions
 * @returns {ContainerBuilder} The container
 */
function createStickyRecoveryContainer({ type, userMention, content }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# sticky ${type} recovered`);
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention}`);
    const contentDisplay = new TextDisplayBuilder().setContent(`**${type}**: ${content}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, userDisplay, contentDisplay)
        .setAccentColor(LOG_COLORS.INFO);

    return container;
}

/**
 * Create a bulk message delete container
 * @param {Object} options - Bulk delete options
 * @param {string} options.channel - Channel mention
 * @param {string} options.channelName - Channel name for display
 * @param {number} options.count - Number of messages deleted
 * @param {string} options.executor - Executor mention/tag
 * @param {string[]} options.messagePreview - Array of message previews
 * @param {Object} options.fileComponent - Optional file attachment with full message log
 * @returns {ContainerBuilder} The container
 */
function createBulkMessageDeleteContainer({ channel, channelName, count, executor, messagePreview = [], fileComponent = null }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# bulk message delete');
    // Format channel display properly for single vs multiple channels
    const channelInfo = channelName.includes('channels')
        ? `${channel} (${channelName})`  // Multiple: "channel1, channel2 (4 channels)"
        : `${channel} (#${channelName})`; // Single: "channel (#name)"

    const infoDisplay = new TextDisplayBuilder().setContent(`**channel**: ${channelInfo}\n**count**: ${count.toLocaleString()} messages\n**executor**: ${executor}`);

    const components = [titleDisplay, infoDisplay];

    // Add message preview section if available
    if (messagePreview.length > 0) {
        const previewText = messagePreview.join('\n');
        const previewDisplays = createTextDisplays(previewText, '## Message Preview');
        components.push(...previewDisplays);
    }

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(LOG_COLORS.ERROR);

    // Add file component directly to container and store attachment data
    if (fileComponent) {
        // Clean filename - remove leading special characters that can cause attachment issues
        let cleanFilename = fileComponent.name;
        // Remove leading underscores and periods
        while (cleanFilename.startsWith('_') || cleanFilename.startsWith('.')) {
            cleanFilename = cleanFilename.substring(1);
        }
        // Ensure we still have a valid filename
        if (cleanFilename.length === 0 || cleanFilename === '.txt') {
            cleanFilename = 'deleted-messages.txt';
        }

        const fileBuilder = new FileBuilder()
            .setURL(`attachment://${cleanFilename}`);
        container.addFileComponents(fileBuilder);

        // Store attachment data with clean filename for sendLogContainer
        container._attachmentData = {
            ...fileComponent,
            name: cleanFilename
        };
    }

    return container;
}

/**
 * Create a member update container
 * @param {Object} options - Member update options
 * @param {string} options.userMention - User mention
 * @param {string} options.userTag - User tag
 * @param {string[]} options.changes - Array of change descriptions
 * @returns {ContainerBuilder} The container
 */
function createMemberUpdateContainer({ userMention, userTag, changes = [] }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# member updated');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention} (${userTag})`);

    const components = [titleDisplay, userDisplay];

    // Add changes section
    if (changes.length > 0) {
        const changesText = changes.join('\n');
        const changesDisplays = createTextDisplays(changesText, '## Changes');
        components.push(...changesDisplays);
    }

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(LOG_COLORS.WARNING);

    return container;
}

/**
 * Create a voice state update container
 * @param {Object} options - Voice state options
 * @param {string} options.userMention - User mention
 * @param {string} options.userTag - User tag
 * @param {string} options.action - Action description
 * @param {string} options.channelInfo - Channel information
 * @returns {ContainerBuilder} The container
 */
function createVoiceStateContainer({ userMention, userTag, action, channelInfo }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# voice state update');
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention} (${userTag})`);
    const actionDisplay = new TextDisplayBuilder().setContent(`**action**: ${action}\n${channelInfo}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, userDisplay, actionDisplay)
        .setAccentColor(LOG_COLORS.INFO);

    return container;
}

/**
 * Create a ban/unban container
 * @param {Object} options - Ban options
 * @param {string} options.action - 'banned' or 'unbanned'
 * @param {string} options.userMention - User mention
 * @param {string} options.userTag - User tag
 * @param {string} options.executor - Executor mention/tag
 * @param {string} options.reason - Reason for action
 * @returns {ContainerBuilder} The container
 */
function createBanContainer({ action, userMention, userTag, executor, reason }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# member ${action}`);
    const userDisplay = new TextDisplayBuilder().setContent(`**user**: ${userMention} (${userTag})`);
    const executorDisplay = new TextDisplayBuilder().setContent(`**executor**: ${executor}`);

    const components = [titleDisplay, userDisplay, executorDisplay];

    // Add reason section if provided
    if (reason) {
        const reasonDisplays = createTextDisplays(reason, '## Reason');
        components.push(...reasonDisplays);
    }

    const color = action === 'banned' ? 0x992D22 : 0x00D166;
    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(color);

    return container;
}

/**
 * Create a channel management container
 * @param {Object} options - Channel options
 * @param {string} options.action - 'created', 'deleted', or 'updated'
 * @param {string} options.channelMention - Channel mention (or name if deleted)
 * @param {string} options.channelType - Channel type
 * @param {string} options.executor - Executor mention/tag
 * @param {string[]} options.changes - Array of change descriptions
 * @returns {ContainerBuilder} The container
 */
function createChannelContainer({ action, channelMention, channelType, executor, changes = [] }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# channel ${action}`);
    const channelDisplay = new TextDisplayBuilder().setContent(`**channel**: ${channelMention}\n**type**: ${channelType}\n**executor**: ${executor}`);

    const components = [titleDisplay, channelDisplay];

    // Add changes section if any
    if (changes.length > 0) {
        const changesText = changes.join('\n');
        const changesDisplays = createTextDisplays(changesText, '## Changes');
        components.push(...changesDisplays);
    }

    const colorMap = {
        'created': LOG_COLORS.SUCCESS,
        'deleted': LOG_COLORS.ERROR,
        'updated': LOG_COLORS.WARNING
    };

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(colorMap[action] || LOG_COLORS.INFO);

    return container;
}

/**
 * Create a role management container
 * @param {Object} options - Role options
 * @param {string} options.action - 'created', 'deleted', or 'updated'
 * @param {string} options.roleMention - Role mention (or name if deleted)
 * @param {string} options.executor - Executor mention/tag
 * @param {string[]} options.changes - Array of change descriptions
 * @returns {ContainerBuilder} The container
 */
function createRoleContainer({ action, roleMention, executor, changes = [] }) {
    const titleDisplay = new TextDisplayBuilder().setContent(`# role ${action}`);
    const roleDisplay = new TextDisplayBuilder().setContent(`**role**: ${roleMention}\n**executor**: ${executor}`);

    const components = [titleDisplay, roleDisplay];

    // Add changes section if any
    if (changes.length > 0) {
        const changesText = changes.join('\n');
        const changesDisplays = createTextDisplays(changesText, '## Changes');
        components.push(...changesDisplays);
    }

    const colorMap = {
        'created': LOG_COLORS.SUCCESS,
        'deleted': LOG_COLORS.ERROR,
        'updated': LOG_COLORS.WARNING
    };

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(colorMap[action] || LOG_COLORS.INFO);

    return container;
}

/**
 * Create a guild join container
 * @param {Object} options - Guild join options
 * @param {string} options.guildName - Guild name
 * @param {string} options.guildId - Guild ID
 * @param {string} options.ownerMention - Owner mention
 * @param {number} options.memberCount - Member count
 * @param {boolean} options.isOwnerLog - Whether this is an owner-only log
 * @returns {ContainerBuilder} The container
 */
function createGuildJoinContainer({ guildName, guildId, ownerMention, memberCount, isOwnerLog = false }) {
    const title = isOwnerLog ? '# bot joined server' : '# bot joined guild';
    const titleDisplay = new TextDisplayBuilder().setContent(title);

    const serverLabel = isOwnerLog ? 'server' : 'name';
    const infoDisplay = new TextDisplayBuilder().setContent(
        `**${serverLabel}**: ${guildName}\n**id**: ${guildId}\n**owner**: ${ownerMention}\n**members**: ${memberCount.toLocaleString()}`
    );

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, infoDisplay)
        .setAccentColor(LOG_COLORS.SUCCESS);

    return container;
}

/**
 * Create a guild leave container
 * @param {Object} options - Guild leave options
 * @param {string} options.guildName - Guild name
 * @param {string} options.guildId - Guild ID
 * @returns {ContainerBuilder} The container
 */
function createGuildLeaveContainer({ guildName, guildId }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# bot left guild');
    const infoDisplay = new TextDisplayBuilder().setContent(`**name**: ${guildName}\n**id**: ${guildId}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, infoDisplay)
        .setAccentColor(LOG_COLORS.ERROR);

    return container;
}

/**
 * Create item management log container (create/update/delete/disable/enable)
 * @param {Object} data - Log data
 * @param {string} data.action - Action performed (created/updated/deleted/disabled/enabled)
 * @param {string} data.adminMention - Admin who performed the action
 * @param {string} data.itemName - Item name
 * @param {string} data.itemEmote - Item emote
 * @param {string} data.itemType - Item type
 * @param {string} data.itemRarity - Item rarity
 * @param {Array} data.dropLocations - Drop locations
 * @param {Object} [data.changes] - Changes made (for updates)
 * @param {string} [data.reason] - Reason for action
 * @returns {ContainerBuilder} Log container
 */
function createItemManagementContainer(data) {
    const { action, adminMention, itemName, itemEmote, itemType, itemRarity, dropLocations, changes, reason } = data;

    // Handle premium actions differently
    if (action.startsWith('premium_')) {
        const titleDisplay = new TextDisplayBuilder().setContent(`# ${action.replace('_', ' ')}`);

        const premiumInfo = [
            `**user**: ${adminMention}`,
            `**feature**: Items Creation`,
            `**status**: ${action === 'premium_activated' ? 'Activated' : 'Deactivated'}`
        ];

        if (reason) {
            premiumInfo.push(`**reason**: ${reason}`);
        }

        const infoDisplay = new TextDisplayBuilder().setContent(premiumInfo.join('\n'));
        const components = [titleDisplay, infoDisplay];

        const container = new ContainerBuilder()
            .addTextDisplayComponents(...components)
            .setAccentColor(action === 'premium_activated' ? 0xF1C40F : 0x95A5A6);

        return container;
    }

    const titleDisplay = new TextDisplayBuilder().setContent(`# item ${action}`);

    // Basic item info
    const itemInfo = [
        `**admin**: ${adminMention}`,
        `**item**: ${itemEmote} ${itemName}`,
        `**type**: ${itemType}`,
        `**rarity**: ${itemRarity}`,
        `**drop locations**: ${dropLocations?.length ? dropLocations.join(', ') : 'none'}`
    ];

    if (reason) {
        itemInfo.push(`**reason**: ${reason}`);
    }

    const infoDisplay = new TextDisplayBuilder().setContent(itemInfo.join('\n'));
    const components = [titleDisplay, infoDisplay];

    // Add changes section for updates
    if (action === 'updated' && changes && Object.keys(changes).length > 0) {
        const changesList = Object.entries(changes).map(([field, change]) => {
            if (typeof change === 'object' && change.from !== undefined && change.to !== undefined) {
                return `**${field}**: ${change.from} → ${change.to}`;
            }
            return `**${field}**: ${change}`;
        });

        const changesDisplay = new TextDisplayBuilder().setContent(`## Changes\n${changesList.join('\n')}`);
        components.push(changesDisplay);
    }

    const colorMap = {
        'created': LOG_COLORS.SUCCESS,
        'updated': LOG_COLORS.WARNING,
        'deleted': LOG_COLORS.ERROR,
        'disabled': LOG_COLORS.DISABLED,
        'enabled': LOG_COLORS.SUCCESS,
        'premium_activated': LOG_COLORS.PREMIUM,
        'premium_deactivated': LOG_COLORS.DISABLED
    };

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...components)
        .setAccentColor(colorMap[action] || LOG_COLORS.INFO);

    return container;
}

/**
 * Create item drop log container
 * @param {Object} data - Log data
 * @param {string} data.userMention - User who received the item
 * @param {string} data.itemName - Item name
 * @param {string} data.itemEmote - Item emote
 * @param {string} data.itemRarity - Item rarity
 * @param {string} data.dropLocation - Where the item dropped from
 * @param {number} data.expGained - EXP gained that triggered the drop
 * @param {boolean} [data.isFirstInServer] - Whether this is the first time this item dropped in the server
 * @returns {ContainerBuilder} Log container
 */
function createItemDropContainer(data) {
    const { userMention, itemName, itemEmote, itemRarity, dropLocation, expGained, isFirstInServer } = data;

    const titleDisplay = new TextDisplayBuilder().setContent(`# item dropped`);

    const dropInfo = [
        `**user**: ${userMention}`,
        `**item**: ${itemEmote} ${itemName}`,
        `**rarity**: ${itemRarity}`,
        `**location**: ${dropLocation}`,
        `**exp gained**: ${expGained}`
    ];

    if (isFirstInServer) {
        dropInfo.push(`🎉 **first in server!**`);
    }

    const infoDisplay = new TextDisplayBuilder().setContent(dropInfo.join('\n'));

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, infoDisplay)
        .setAccentColor(LOG_COLORS.PREMIUM); // Gold color for drops

    return container;
}

/**
 * Create an EXP level created container
 * @param {Object} options - Level created options
 * @param {string} options.adminMention - Admin mention
 * @param {number} options.level - Level number
 * @param {string} options.roleMention - Role mention
 * @param {number} options.expRequired - EXP required
 * @returns {ContainerBuilder} The container
 */
function createExpLevelCreatedContainer({ adminMention, level, roleMention, expRequired }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# level created');
    const adminDisplay = new TextDisplayBuilder().setContent(`**admin**: ${adminMention}`);
    const levelDisplay = new TextDisplayBuilder().setContent(`**level**: ${level}`);
    const roleDisplay = new TextDisplayBuilder().setContent(`**role**: ${roleMention}`);
    const expDisplay = new TextDisplayBuilder().setContent(`**exp required**: ${expRequired}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, adminDisplay, levelDisplay, roleDisplay, expDisplay)
        .setAccentColor(LOG_COLORS.SUCCESS); // Green color for creation

    return container;
}

/**
 * Create an EXP level edited container
 * @param {Object} options - Level edited options
 * @param {string} options.adminMention - Admin mention
 * @param {number} options.level - Level number
 * @param {Object} options.changes - Changes object with old and new values
 * @returns {ContainerBuilder} The container
 */
function createExpLevelEditedContainer({ adminMention, level, changes }) {
    const titleDisplay = new TextDisplayBuilder().setContent('# level edited');
    const adminDisplay = new TextDisplayBuilder().setContent(`**admin**: ${adminMention}`);
    const levelDisplay = new TextDisplayBuilder().setContent(`**level**: ${level}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(titleDisplay, adminDisplay, levelDisplay);

    // Add change displays
    if (changes.role) {
        const roleDisplay = new TextDisplayBuilder().setContent(`**role**: <@&${changes.role.old}> → <@&${changes.role.new}>`);
        container.addTextDisplayComponents(roleDisplay);
    }
    if (changes.exp) {
        const expDisplay = new TextDisplayBuilder().setContent(`**exp**: ${changes.exp.old} → ${changes.exp.new}`);
        container.addTextDisplayComponents(expDisplay);
    }

    container.setAccentColor(LOG_COLORS.WARNING); // Orange color for editing

    return container;
}

module.exports = {
    splitTextContent,
    createTextDisplays,
    createBasicLogContainer,
    createMessageEditContainer,
    createMessageDeleteContainer,
    createBulkMessageDeleteContainer,
    createMemberJoinContainer,
    createMemberLeaveContainer,
    createMemberUpdateContainer,
    createVoiceStateContainer,
    createBanContainer,
    createChannelContainer,
    createRoleContainer,
    createAuditLogContainer,
    createExpLevelUpContainer,
    createExpVoiceSessionContainer,
    createExpLevelCreatedContainer,
    createExpLevelEditedContainer,
    createFeatureToggleContainer,
    createDehoistUsernameContainer,
    createDehoistScanContainer,
    createOpenerThreadContainer,
    createStickyRecoveryContainer,
    createGuildJoinContainer,
    createGuildLeaveContainer,
    createItemManagementContainer,
    createItemDropContainer
};
