const { Events } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedFind } = require('../utils/database-optimizer.js');
const { defaults } = require("../utils/default_db_structures");
const { sendLogContainer } = require("../utils/sendLog.js");
const { createGuildJoinContainer } = require('../utils/logContainers.js');

module.exports = {
    name: Events.GuildCreate,
    async execute(client, guild) {
        console.log(`[guildCreate] Bot joined guild: ${guild.name} (${guild.id})`);

        // PERFORMANCE OPTIMIZATION: Use optimized database operations with race condition protection
        var guildData = await optimizedFindOne('guilds', { id: guild.id });
        if (guildData == null) {
            try {
                await optimizedInsertOne('guilds', defaults.guild(guild.id));
                guildData = await optimizedFindOne('guilds', { id: guild.id });
            } catch (error) {
                // Handle duplicate key error (race condition)
                if (error.code === 11000) {
                    guildData = await optimizedFindOne('guilds', { id: guild.id });
                } else {
                    throw error;
                }
            }
        }

        // Send owner-only log to all guilds where owner has configured botJoinedServer
        try {
            const allGuilds = await optimizedFind('guilds', {});

            for (const otherGuildData of allGuilds) {
                if (otherGuildData.logs && otherGuildData.logs.enabled) {
                    const hasOwnerEvent = otherGuildData.logs.channels.some(ch =>
                        ch.events.includes('botJoinedServer')
                    );

                    if (hasOwnerEvent) {
                        // Create Components v2 container for owner-only log
                        const container = createGuildJoinContainer({
                            guildName: guild.name,
                            guildId: guild.id,
                            ownerMention: `<@${guild.ownerId}>`,
                            memberCount: guild.memberCount,
                            isOwnerLog: true
                        });

                        await sendLogContainer(otherGuildData.id, 'botJoinedServer', container, client);
                    }
                }
            }
        } catch (error) {
            console.error('[guildCreate] Error sending owner logs:', error);
        }

        // Send join notification to inviter or appropriate channel
        try {
            const { sendJoinNotification } = require('../commands/utility/owner-join-notification.js');
            await sendJoinNotification(guild);
        } catch (error) {
            console.error('[guildCreate] Error sending join notification:', error);
        }

        // Regular guildCreate logging with Components v2
        const channels = (guildData.logs.channels.filter(ch => ch.events.includes("guildCreate")) ?? []).map(l => guild.channels.cache.get(l.id)).filter(ch => ch);

        if (channels.length > 0) {
            // Create Components v2 container for regular guild log
            const container = createGuildJoinContainer({
                guildName: guild.name,
                guildId: guild.id,
                ownerMention: `<@${guild.ownerId}>`,
                memberCount: guild.memberCount,
                isOwnerLog: false
            });

            await sendLogContainer(guild.id, 'guildCreate', container, client);
        }
    },
};