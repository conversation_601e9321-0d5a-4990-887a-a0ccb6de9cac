/**
 * EXAMPLE FEATURE TEST
 * Demonstrates how to create new tests using the shared BotTestBase
 * 
 * This is a template showing how to:
 * - Extend BotTestBase for consistent setup
 * - Create feature-specific tests
 * - Use shared utilities and mock interactions
 */

const { BotTestBase, TestUtils } = require('../shared/BotTestBase.js');

class ExampleFeatureTester extends BotTestBase {
    constructor() {
        super('ExampleFeatureTest');
    }

    /**
     * Test basic feature functionality
     */
    async testBasicFeature() {
        // Example: Test a slash command
        const command = require('../../commands/utility/you.js');
        const interaction = this.createMockInteraction(2, 'you'); // Slash command
        interaction.options = {
            getString: () => null,
            getUser: () => null
        };
        
        await command.execute(interaction);
        
        console.log(`   Command responses: ${interaction._responses.length}`);
        
        // Validate response
        return interaction._responses.length > 0;
    }

    /**
     * Test feature with select menu interaction
     */
    async testSelectMenuInteraction() {
        // Example: Test a select menu
        const command = require('../../commands/utility/featuresMenu.js');
        const interaction = this.createMockInteraction(5, '17-select', ['exp']);
        
        await command.select(interaction, []);
        
        console.log(`   Select menu responses: ${interaction._responses.length}`);
        
        return interaction._responses.length > 0;
    }

    /**
     * Test feature with button interaction
     */
    async testButtonInteraction() {
        // Example: Test a button click
        const command = require('../../commands/utility/owner.js');
        const interaction = this.createMockInteraction(3, 'owner-reload');
        
        try {
            await command.buttons(interaction);
            console.log(`   Button responses: ${interaction._responses.length}`);
            return true;
        } catch (error) {
            console.log(`   Button error (may be expected): ${error.message}`);
            return false;
        }
    }

    /**
     * Test feature database operations
     */
    async testDatabaseOperations() {
        // Example: Test database queries
        const guildsCol = this.mongoClient.db('test').collection('guilds');
        
        // Check if test guild exists
        const guildData = await guildsCol.findOne({ id: this.testGuild.id });
        console.log(`   Guild data exists: ${!!guildData}`);
        
        if (guildData) {
            console.log(`   Guild features configured: ${Object.keys(guildData).length - 1}`); // -1 for _id
        }
        
        return true;
    }

    /**
     * Test feature with timeout handling
     */
    async testWithTimeout() {
        // Example: Test with timeout protection
        const timeoutPromise = TestUtils.createTimeout(5000); // 5 second timeout
        
        const testPromise = new Promise(resolve => {
            // Simulate some async operation
            setTimeout(() => resolve(true), 1000);
        });
        
        try {
            const result = await Promise.race([testPromise, timeoutPromise]);
            console.log(`   Timeout test completed: ${result}`);
            return result;
        } catch (error) {
            console.log(`   Timeout test failed: ${error.message}`);
            return false;
        }
    }

    /**
     * Run all example tests
     */
    async runAllTests() {
        console.log('🚀 Starting Example Feature testing...\n');
        console.log('=' .repeat(50));
        console.log('EXAMPLE TEST USING SHARED BASE');
        console.log('=' .repeat(50));

        // Define tests to run
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Basic feature', test: () => this.testBasicFeature() },
            { name: 'Select menu interaction', test: () => this.testSelectMenuInteraction() },
            { name: 'Button interaction', test: () => this.testButtonInteraction() },
            { name: 'Database operations', test: () => this.testDatabaseOperations() },
            { name: 'Timeout handling', test: () => this.testWithTimeout() }
        ];

        // Run all tests using the base class helper
        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        // Print comprehensive results
        this.printResults();
        
        // Return overall success status
        const passedTests = this.testResults.filter(r => r.passed).length;
        return passedTests === this.testResults.length;
    }
}

// Main execution function
async function runExampleTest() {
    const tester = new ExampleFeatureTester();
    
    try {
        // Validate environment
        tester.validateEnvironment();
        
        // Initialize test environment
        await tester.initialize();
        
        // Run all tests
        const allPassed = await tester.runAllTests();
        
        // Exit with appropriate code
        process.exit(allPassed ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Example test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Run if this file is executed directly
if (require.main === module) {
    runExampleTest();
}

module.exports = { ExampleFeatureTester, runExampleTest };
