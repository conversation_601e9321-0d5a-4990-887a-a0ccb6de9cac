const { optimizedFindOne } = require('./database-optimizer.js');

/**
 * Custom Emoji Integration Utility
 * Provides easy access to custom emojis for features like Starfall
 * ENTERPRISE-GRADE: LRU caching, fallback handling, performance monitoring
 */

// LRU Cache for custom emoji lookups (prevents repeated DB queries)
const { CacheFactory } = require('./LRUCache.js');
const customEmojiCache = CacheFactory.createHighFrequencyCache(); // 10000 entries, 5min TTL

// Performance metrics
const emojiIntegrationMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    dbQueries: 0,
    fallbacksUsed: 0,
    lastOptimization: Date.now()
};

/**
 * Get custom emoji by name with fallback
 * @param {string} emojiName - Name of the custom emoji to retrieve
 * @param {string} fallbackEmoji - Fallback emoji if custom emoji not found
 * @returns {Promise<string>} Custom emoji string or fallback
 */
async function getCustomEmoji(emojiName, fallbackEmoji = '❓') {
    try {
        // Check cache first
        const cacheKey = `custom_emoji_${emojiName}`;
        const cached = customEmojiCache.get(cacheKey);
        
        if (cached !== undefined) {
            emojiIntegrationMetrics.cacheHits++;
            return cached || fallbackEmoji; // Return fallback if cached as null
        }

        emojiIntegrationMetrics.cacheMisses++;
        emojiIntegrationMetrics.dbQueries++;

        // Query database for custom emoji
        const customEmoji = await optimizedFindOne('custom_emojis', { 
            name: emojiName,
            disabled: { $ne: true }
        });

        if (customEmoji && customEmoji.emote) {
            // Cache the result
            customEmojiCache.set(cacheKey, customEmoji.emote);
            return customEmoji.emote;
        } else {
            // Cache null result to prevent repeated queries
            customEmojiCache.set(cacheKey, null);
            emojiIntegrationMetrics.fallbacksUsed++;
            return fallbackEmoji;
        }
    } catch (error) {
        console.error(`[customEmojiIntegration] Error fetching emoji ${emojiName}:`, error);
        emojiIntegrationMetrics.fallbacksUsed++;
        return fallbackEmoji;
    }
}

/**
 * Get multiple custom emojis at once (batch operation)
 * @param {Object} emojiMap - Object with { key: { name: 'emoji_name', fallback: '🎁' } }
 * @returns {Promise<Object>} Object with same keys but emoji values
 */
async function getCustomEmojis(emojiMap) {
    const results = {};
    const promises = [];

    for (const [key, config] of Object.entries(emojiMap)) {
        promises.push(
            getCustomEmoji(config.name, config.fallback).then(emoji => {
                results[key] = emoji;
            })
        );
    }

    await Promise.all(promises);
    return results;
}

/**
 * Starfall-specific emoji getter (convenience function)
 * @returns {Promise<Object>} Object with all Starfall emojis
 */
async function getStarfallEmojis() {
    return await getCustomEmojis({
        nothing: { name: 'starfall_nothing', fallback: '💫' },
        blank: { name: 'starfall_blank', fallback: '<:blank:1390825005079859380>' },
        itemFallback: { name: 'starfall_item', fallback: '🎁' },
        star: { name: 'starfall_star', fallback: '⭐' }
    });
}

/**
 * Invalidate custom emoji cache (call when emojis are created/updated/deleted)
 * @param {string} emojiName - Optional specific emoji name to invalidate
 */
function invalidateCustomEmojiCache(emojiName = null) {
    if (emojiName) {
        const cacheKey = `custom_emoji_${emojiName}`;
        customEmojiCache.delete(cacheKey);
        console.log(`[customEmojiIntegration] Invalidated cache for emoji: ${emojiName}`);
    } else {
        customEmojiCache.clear();
        console.log('[customEmojiIntegration] Cleared all custom emoji cache');
    }
}

/**
 * Get performance metrics for monitoring
 * @returns {Object} Performance metrics
 */
function getEmojiIntegrationMetrics() {
    const hitRate = emojiIntegrationMetrics.cacheHits + emojiIntegrationMetrics.cacheMisses > 0 ?
        (emojiIntegrationMetrics.cacheHits / (emojiIntegrationMetrics.cacheHits + emojiIntegrationMetrics.cacheMisses) * 100).toFixed(2) : '0.00';

    return {
        ...emojiIntegrationMetrics,
        cacheHitRate: `${hitRate}%`,
        cacheSize: customEmojiCache.size,
        systemHealth: {
            status: emojiIntegrationMetrics.fallbacksUsed < emojiIntegrationMetrics.dbQueries * 0.1 ? 'excellent' : 'good',
            recommendations: hitRate > 80 ? ['Cache performance is optimal'] : ['Consider warming cache for frequently used emojis']
        }
    };
}

/**
 * Warm cache with commonly used emojis (performance optimization)
 * @param {Array<string>} emojiNames - Array of emoji names to pre-load
 */
async function warmEmojiCache(emojiNames = ['starfall_nothing', 'starfall_blank', 'starfall_item']) {
    console.log(`[customEmojiIntegration] Warming cache for ${emojiNames.length} emojis...`);
    
    const promises = emojiNames.map(name => getCustomEmoji(name, null));
    await Promise.allSettled(promises);
    
    console.log('[customEmojiIntegration] Cache warming complete');
}

/**
 * Performance cleanup and optimization
 */
function performanceCleanupAndOptimization() {
    emojiIntegrationMetrics.lastOptimization = Date.now();
    
    // Log performance metrics periodically
    const metrics = getEmojiIntegrationMetrics();
    console.log(`[customEmojiIntegration] Performance: ${metrics.cacheHitRate} hit rate, ${metrics.cacheSize} cached, ${metrics.systemHealth.status} health`);
}

// Auto-optimization every 10 minutes
setInterval(performanceCleanupAndOptimization, 10 * 60 * 1000);

module.exports = {
    // Core functions
    getCustomEmoji,
    getCustomEmojis,
    getStarfallEmojis,
    invalidateCustomEmojiCache,
    warmEmojiCache,
    
    // Performance monitoring
    getEmojiIntegrationMetrics,
    performanceCleanupAndOptimization
};
