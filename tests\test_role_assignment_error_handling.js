/**
 * Role Assignment Error Handling Test
 * 
 * This test verifies that the graceful role assignment error handling
 * properly catches and handles permission errors without crashing.
 */

require('dotenv').config();
const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class RoleAssignmentErrorTest extends BotTestBase {
    constructor() {
        super('RoleAssignmentErrorTest');
    }

    /**
     * Test graceful handling of role hierarchy errors
     */
    async testRoleHierarchyError() {
        console.log('\n🔒 Testing role hierarchy error handling...');
        
        const { safeAssignRoles } = require('../utils/roleAssignmentHandler.js');
        
        // Create mock member with limited bot permissions
        const mockMember = {
            user: {
                id: 'test-user-123',
                tag: 'TestUser#1234'
            },
            guild: {
                id: this.testGuildId,
                roles: {
                    cache: new Map([
                        ['high-role-id', {
                            id: 'high-role-id',
                            name: 'High Role',
                            position: 10,
                            editable: false
                        }],
                        ['low-role-id', {
                            id: 'low-role-id',
                            name: 'Low Role',
                            position: 2,
                            editable: true
                        }]
                    ])
                },
                members: {
                    me: {
                        roles: {
                            highest: {
                                id: 'bot-role-id',
                                name: '<PERSON><PERSON> Role',
                                position: 5
                            }
                        }
                    }
                }
            },
            roles: {
                add: async (roleIds) => {
                    // Simulate Discord API permission error for high roles
                    const roles = Array.isArray(roleIds) ? roleIds : [roleIds];
                    const hasHighRole = roles.includes('high-role-id');
                    
                    if (hasHighRole) {
                        const error = new Error('Missing Permissions');
                        error.code = 50013;
                        error.name = 'DiscordAPIError';
                        throw error;
                    }
                    
                    return true; // Success for valid roles
                }
            }
        };

        // Test assigning a role that's too high
        const result = await safeAssignRoles(mockMember, 'high-role-id', {
            operation: 'test_hierarchy',
            source: 'test'
        });

        console.log('   Hierarchy test result:', {
            success: result.success,
            reason: result.reason,
            failed: result.failed.length,
            assigned: result.assigned.length
        });

        // Should fail gracefully without crashing
        if (result.failed.length > 0 && result.failed[0].reason === 'hierarchy_error') {
            console.log('✅ Hierarchy error handled gracefully');
            return true;
        } else {
            console.log('❌ Hierarchy error not handled correctly');
            return false;
        }
    }

    /**
     * Test graceful handling of valid role assignments
     */
    async testValidRoleAssignment() {
        console.log('\n✅ Testing valid role assignment...');
        
        const { safeAssignRoles } = require('../utils/roleAssignmentHandler.js');
        
        // Create mock member with proper permissions
        const mockMember = {
            user: {
                id: 'test-user-456',
                tag: 'TestUser2#5678'
            },
            guild: {
                id: this.testGuildId,
                roles: {
                    cache: new Map([
                        ['valid-role-id', {
                            id: 'valid-role-id',
                            name: 'Valid Role',
                            position: 2,
                            editable: true
                        }]
                    ])
                },
                members: {
                    me: {
                        roles: {
                            highest: {
                                id: 'bot-role-id',
                                name: 'Bot Role',
                                position: 5
                            }
                        }
                    }
                }
            },
            roles: {
                add: async (roleIds) => {
                    return true; // Success
                }
            }
        };

        // Test assigning a valid role
        const result = await safeAssignRoles(mockMember, 'valid-role-id', {
            operation: 'test_valid',
            source: 'test'
        });

        console.log('   Valid assignment result:', {
            success: result.success,
            assigned: result.assigned.length,
            failed: result.failed.length
        });

        // Should succeed
        if (result.success && result.assigned.length === 1) {
            console.log('✅ Valid role assignment succeeded');
            return true;
        } else {
            console.log('❌ Valid role assignment failed unexpectedly');
            return false;
        }
    }

    /**
     * Test member edit functionality
     */
    async testMemberEditHandling() {
        console.log('\n👤 Testing member edit error handling...');
        
        const { safeEditMember } = require('../utils/roleAssignmentHandler.js');
        
        // Create mock member
        const mockMember = {
            user: {
                id: 'test-user-789',
                tag: 'TestUser3#9012'
            },
            guild: {
                id: this.testGuildId,
                roles: {
                    cache: new Map([
                        ['test-role-id', {
                            id: 'test-role-id',
                            name: 'Test Role',
                            position: 2,
                            editable: true
                        }]
                    ])
                },
                members: {
                    me: {
                        roles: {
                            highest: {
                                id: 'bot-role-id',
                                name: 'Bot Role',
                                position: 5
                            }
                        }
                    }
                }
            },
            roles: {
                add: async (roleIds) => true
            },
            setNickname: async (nickname) => {
                if (nickname === 'forbidden') {
                    const error = new Error('Missing Permissions');
                    error.code = 50013;
                    error.name = 'DiscordAPIError';
                    throw error;
                }
                return true;
            }
        };

        // Test editing with forbidden nickname
        const result = await safeEditMember(mockMember, {
            roles: ['test-role-id'],
            nick: 'forbidden'
        }, {
            operation: 'test_edit',
            source: 'test'
        });

        console.log('   Edit result:', {
            success: result.success,
            rolesSuccess: result.rolesResult?.success,
            nicknameSuccess: result.nicknameResult?.success,
            nicknameReason: result.nicknameResult?.reason
        });

        // Should have mixed results (roles success, nickname failure)
        if (result.rolesResult?.success && !result.nicknameResult?.success) {
            console.log('✅ Member edit partial failure handled gracefully');
            return true;
        } else {
            console.log('❌ Member edit error handling failed');
            return false;
        }
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Role hierarchy error handling', test: () => this.testRoleHierarchyError() },
            { name: 'Valid role assignment', test: () => this.testValidRoleAssignment() },
            { name: 'Member edit error handling', test: () => this.testMemberEditHandling() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
    }
}

// Main execution function
async function runRoleAssignmentErrorTest() {
    const tester = new RoleAssignmentErrorTest();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runRoleAssignmentErrorTest();
}

module.exports = { RoleAssignmentErrorTest };
