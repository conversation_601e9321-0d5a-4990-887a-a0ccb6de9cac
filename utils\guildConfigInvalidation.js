/**
 * Centralized Guild Configuration Cache Invalidation System
 * Coordinates cache invalidation across multiple modules when guild settings change
 */

/**
 * Invalidate all guild configuration caches across all modules
 * @param {string} guildId - The guild ID to invalidate caches for
 */
async function invalidateAllGuildConfigCaches(guildId) {
    console.log(`[guildConfigInvalidation] 🔄 Starting centralized guild config cache invalidation for guild ${guildId}`);
    
    const invalidationResults = {
        success: [],
        errors: [],
        totalAttempted: 0
    };
    
    try {
        // 1. Invalidate EXP cache module guild configs
        try {
            const { invalidateGuildCache } = require('./expCache.js');
            invalidateGuildCache(guildId);
            invalidationResults.success.push('expCache.js - guildConfigCache');
            console.log(`[guildConfigInvalidation] ✅ Invalidated EXP cache guild config for ${guildId}`);
        } catch (error) {
            invalidationResults.errors.push({ module: 'expCache.js', error: error.message });
            console.error(`[guildConfigInvalidation] ❌ Error invalidating EXP cache for ${guildId}:`, error);
        }
        invalidationResults.totalAttempted++;
        
        // 2. Invalidate message create event guild configs
        try {
            const messageCreateModule = require('../events/messageCreate.js');
            // Check if guildConfigCache is accessible and clear it
            if (messageCreateModule.guildConfigCache) {
                messageCreateModule.guildConfigCache.delete(guildId);
                invalidationResults.success.push('messageCreate.js - guildConfigCache');
                console.log(`[guildConfigInvalidation] ✅ Invalidated messageCreate guild config for ${guildId}`);
            } else {
                // Try alternative approach - clear all guild configs in messageCreate
                console.log(`[guildConfigInvalidation] ⚠️ guildConfigCache not directly accessible in messageCreate.js for ${guildId}`);
            }
        } catch (error) {
            invalidationResults.errors.push({ module: 'messageCreate.js', error: error.message });
            console.error(`[guildConfigInvalidation] ❌ Error invalidating messageCreate cache for ${guildId}:`, error);
        }
        invalidationResults.totalAttempted++;
        
        // 3. Invalidate message cache module guild configs
        try {
            const { invalidateAllGuildCaches } = require('./messageCache.js');
            invalidateAllGuildCaches(guildId);
            invalidationResults.success.push('messageCache.js - all guild caches');
            console.log(`[guildConfigInvalidation] ✅ Invalidated message cache guild configs for ${guildId}`);
        } catch (error) {
            invalidationResults.errors.push({ module: 'messageCache.js', error: error.message });
            console.error(`[guildConfigInvalidation] ❌ Error invalidating message cache for ${guildId}:`, error);
        }
        invalidationResults.totalAttempted++;
        
        // 4. Invalidate items module guild configs
        try {
            const { clearAllItemsCaches } = require('../commands/utility/items.js');
            clearAllItemsCaches(); // This clears all guild-related item caches
            invalidationResults.success.push('items.js - all item caches');
            console.log(`[guildConfigInvalidation] ✅ Invalidated items module caches for ${guildId}`);
        } catch (error) {
            invalidationResults.errors.push({ module: 'items.js', error: error.message });
            console.error(`[guildConfigInvalidation] ❌ Error invalidating items cache for ${guildId}:`, error);
        }
        invalidationResults.totalAttempted++;
        
        // 5. Invalidate opener cache module guild configs
        try {
            const openerCacheModule = require('./openerCache.js');
            // Clear guild-specific opener configs if the module has such functionality
            if (openerCacheModule.clearGuildOpenerCache) {
                openerCacheModule.clearGuildOpenerCache(guildId);
                invalidationResults.success.push('openerCache.js - guild opener cache');
                console.log(`[guildConfigInvalidation] ✅ Invalidated opener cache for ${guildId}`);
            }
        } catch (error) {
            invalidationResults.errors.push({ module: 'openerCache.js', error: error.message });
            console.error(`[guildConfigInvalidation] ❌ Error invalidating opener cache for ${guildId}:`, error);
        }
        invalidationResults.totalAttempted++;
        
        // 6. Invalidate any other guild-specific caches
        try {
            // Clear command invalidation caches that might be guild-specific
            const { clearInvalidationCache } = require('./commandInvalidation.js');
            if (clearInvalidationCache) {
                clearInvalidationCache(guildId);
                invalidationResults.success.push('commandInvalidation.js - invalidation cache');
                console.log(`[guildConfigInvalidation] ✅ Invalidated command invalidation cache for ${guildId}`);
            }
        } catch (error) {
            invalidationResults.errors.push({ module: 'commandInvalidation.js', error: error.message });
            console.error(`[guildConfigInvalidation] ❌ Error invalidating command invalidation cache for ${guildId}:`, error);
        }
        invalidationResults.totalAttempted++;
        
        // Log summary
        const successCount = invalidationResults.success.length;
        const errorCount = invalidationResults.errors.length;
        
        console.log(`[guildConfigInvalidation] 📊 Guild config cache invalidation summary for ${guildId}:`);
        console.log(`[guildConfigInvalidation] ✅ Successful: ${successCount}/${invalidationResults.totalAttempted}`);
        console.log(`[guildConfigInvalidation] ❌ Errors: ${errorCount}/${invalidationResults.totalAttempted}`);
        
        if (invalidationResults.success.length > 0) {
            console.log(`[guildConfigInvalidation] ✅ Successfully invalidated:`);
            for (const success of invalidationResults.success) {
                console.log(`[guildConfigInvalidation]   - ${success}`);
            }
        }
        
        if (invalidationResults.errors.length > 0) {
            console.log(`[guildConfigInvalidation] ❌ Errors encountered:`);
            for (const error of invalidationResults.errors) {
                console.log(`[guildConfigInvalidation]   - ${error.module}: ${error.error}`);
            }
        }
        
        return invalidationResults;
        
    } catch (error) {
        console.error(`[guildConfigInvalidation] 💥 Fatal error during guild config cache invalidation for ${guildId}:`, error);
        invalidationResults.errors.push({ module: 'system', error: error.message });
        return invalidationResults;
    }
}

/**
 * Invalidate specific guild configuration cache types
 * @param {string} guildId - The guild ID to invalidate caches for
 * @param {string[]} cacheTypes - Array of cache types to invalidate ('exp', 'message', 'items', 'opener', 'command')
 */
async function invalidateSpecificGuildConfigCaches(guildId, cacheTypes = []) {
    console.log(`[guildConfigInvalidation] 🎯 Targeted guild config cache invalidation for guild ${guildId}: ${cacheTypes.join(', ')}`);
    
    const invalidationResults = {
        success: [],
        errors: [],
        totalAttempted: 0
    };
    
    try {
        for (const cacheType of cacheTypes) {
            invalidationResults.totalAttempted++;
            
            switch (cacheType) {
                case 'exp':
                    try {
                        const { invalidateGuildCache } = require('./expCache.js');
                        invalidateGuildCache(guildId);
                        invalidationResults.success.push(`${cacheType} - expCache.js`);
                        console.log(`[guildConfigInvalidation] ✅ Invalidated EXP cache for ${guildId}`);
                    } catch (error) {
                        invalidationResults.errors.push({ module: `${cacheType} - expCache.js`, error: error.message });
                        console.error(`[guildConfigInvalidation] ❌ Error invalidating EXP cache for ${guildId}:`, error);
                    }
                    break;
                    
                case 'message':
                    try {
                        const { invalidateAllGuildCaches } = require('./messageCache.js');
                        invalidateAllGuildCaches(guildId);
                        invalidationResults.success.push(`${cacheType} - messageCache.js`);
                        console.log(`[guildConfigInvalidation] ✅ Invalidated message cache for ${guildId}`);
                    } catch (error) {
                        invalidationResults.errors.push({ module: `${cacheType} - messageCache.js`, error: error.message });
                        console.error(`[guildConfigInvalidation] ❌ Error invalidating message cache for ${guildId}:`, error);
                    }
                    break;
                    
                case 'items':
                    try {
                        const { clearAllItemsCaches } = require('../commands/utility/items.js');
                        clearAllItemsCaches();
                        invalidationResults.success.push(`${cacheType} - items.js`);
                        console.log(`[guildConfigInvalidation] ✅ Invalidated items cache for ${guildId}`);
                    } catch (error) {
                        invalidationResults.errors.push({ module: `${cacheType} - items.js`, error: error.message });
                        console.error(`[guildConfigInvalidation] ❌ Error invalidating items cache for ${guildId}:`, error);
                    }
                    break;
                    
                case 'opener':
                    try {
                        const openerCacheModule = require('./openerCache.js');
                        if (openerCacheModule.clearGuildOpenerCache) {
                            openerCacheModule.clearGuildOpenerCache(guildId);
                            invalidationResults.success.push(`${cacheType} - openerCache.js`);
                            console.log(`[guildConfigInvalidation] ✅ Invalidated opener cache for ${guildId}`);
                        }
                    } catch (error) {
                        invalidationResults.errors.push({ module: `${cacheType} - openerCache.js`, error: error.message });
                        console.error(`[guildConfigInvalidation] ❌ Error invalidating opener cache for ${guildId}:`, error);
                    }
                    break;
                    
                case 'command':
                    try {
                        const { clearInvalidationCache } = require('./commandInvalidation.js');
                        if (clearInvalidationCache) {
                            clearInvalidationCache(guildId);
                            invalidationResults.success.push(`${cacheType} - commandInvalidation.js`);
                            console.log(`[guildConfigInvalidation] ✅ Invalidated command cache for ${guildId}`);
                        }
                    } catch (error) {
                        invalidationResults.errors.push({ module: `${cacheType} - commandInvalidation.js`, error: error.message });
                        console.error(`[guildConfigInvalidation] ❌ Error invalidating command cache for ${guildId}:`, error);
                    }
                    break;
                    
                default:
                    console.warn(`[guildConfigInvalidation] ⚠️ Unknown cache type: ${cacheType} for guild ${guildId}`);
                    invalidationResults.errors.push({ module: `unknown-${cacheType}`, error: 'Unknown cache type' });
                    break;
            }
        }
        
        // Log summary
        const successCount = invalidationResults.success.length;
        const errorCount = invalidationResults.errors.length;
        
        console.log(`[guildConfigInvalidation] 📊 Targeted cache invalidation summary for ${guildId}:`);
        console.log(`[guildConfigInvalidation] ✅ Successful: ${successCount}/${invalidationResults.totalAttempted}`);
        console.log(`[guildConfigInvalidation] ❌ Errors: ${errorCount}/${invalidationResults.totalAttempted}`);
        
        return invalidationResults;
        
    } catch (error) {
        console.error(`[guildConfigInvalidation] 💥 Fatal error during targeted guild config cache invalidation for ${guildId}:`, error);
        invalidationResults.errors.push({ module: 'system', error: error.message });
        return invalidationResults;
    }
}

/**
 * Test guild configuration cache invalidation system
 * @param {string} guildId - The guild ID to test with
 */
async function testGuildConfigInvalidation(guildId) {
    console.log(`[guildConfigInvalidation] 🧪 Testing guild config cache invalidation system for guild ${guildId}`);
    
    try {
        // Test full invalidation
        const fullResults = await invalidateAllGuildConfigCaches(guildId);
        
        // Test targeted invalidation
        const targetedResults = await invalidateSpecificGuildConfigCaches(guildId, ['exp', 'message']);
        
        console.log(`[guildConfigInvalidation] 🧪 Test results:`);
        console.log(`[guildConfigInvalidation] Full invalidation: ${fullResults.success.length} success, ${fullResults.errors.length} errors`);
        console.log(`[guildConfigInvalidation] Targeted invalidation: ${targetedResults.success.length} success, ${targetedResults.errors.length} errors`);
        
        return {
            fullResults,
            targetedResults
        };
        
    } catch (error) {
        console.error(`[guildConfigInvalidation] 💥 Error testing guild config invalidation for ${guildId}:`, error);
        return null;
    }
}

module.exports = {
    invalidateAllGuildConfigCaches,
    invalidateSpecificGuildConfigCaches,
    testGuildConfigInvalidation
};
