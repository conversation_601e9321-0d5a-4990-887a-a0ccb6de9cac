# 🧪 Shared Test Infrastructure

This directory contains reusable testing infrastructure that eliminates code duplication and provides consistent test environments across all bot tests.

## 📁 Files

### `BotTestBase.js`
The main shared base class that provides:
- Discord client initialization and login
- MongoDB connection setup
- Mock interaction creation with Components v2 validation
- Test environment configuration
- Common test utilities and result tracking

## 🚀 Quick Start

### Creating a New Test

1. **Extend BotTestBase**:
```javascript
const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class MyFeatureTester extends BotTestBase {
    constructor() {
        super('MyFeatureTest'); // Test name for logging
    }
}
```

2. **Add Test Methods**:
```javascript
async testMyFeature() {
    // Create mock interactions
    const interaction = this.createMockInteraction(2, 'my-command'); // Slash command
    
    // Test your feature
    const command = require('../commands/utility/my-command.js');
    await command.execute(interaction);
    
    // Validate results
    return interaction._responses.length > 0;
}
```

3. **Run Tests with Base Class Helper**:
```javascript
async runAllTests() {
    const tests = [
        { name: 'Database connection', test: () => this.testDatabaseConnection() },
        { name: 'My feature', test: () => this.testMyFeature() }
    ];

    for (const test of tests) {
        await this.runTest(test.name, test.test);
    }

    this.printResults();
}
```

## 🔧 Available Methods

### Base Class Methods

#### Setup & Teardown
- `initialize()` - Sets up Discord client, MongoDB, and test entities
- `cleanup()` - Cleans up resources
- `validateEnvironment()` - Validates required environment variables

#### Mock Interactions
- `createMockInteraction(type, customId, values)` - Creates realistic mock interactions
  - `type`: 2=Slash, 3=Button, 5=Select, 6=Modal
  - `customId`: Interaction custom ID
  - `values`: Array for selects, Object for modal fields

#### Test Management
- `runTest(name, testFunction)` - Runs a test with error handling
- `recordTestResult(name, passed, details)` - Records test results
- `printResults()` - Prints comprehensive test summary

#### Database Testing
- `testDatabaseConnection()` - Tests MongoDB connectivity

### Mock Interaction Features

Mock interactions include all standard Discord.js methods:
- `reply(options)` - Mock reply with Components v2 validation
- `followUp(options)` - Mock follow-up message
- `update(options)` - Mock interaction update
- `editReply(options)` - Mock reply edit
- `deferUpdate()` - Mock defer update
- `deferReply(options)` - Mock defer reply
- `showModal(modal)` - Mock modal display

### Utility Functions

#### TestUtils
- `wait(ms)` - Wait for specified time
- `generateTestId()` - Generate random test ID
- `validateComponentsV2Response(response)` - Validate Components v2 compliance
- `createTimeout(ms)` - Create timeout promise for testing

## 📊 Interaction Types

| Type | Description | Example Usage |
|------|-------------|---------------|
| 2 | Slash Command | `/exp`, `/items`, `/you` |
| 3 | Button | `exp-create-level`, `items-delete` |
| 5 | String Select | `exp-levels-select`, `items-type-select` |
| 6 | Modal Submit | `exp-create-modal`, `items-edit-modal` |

## 🎯 Best Practices

### 1. **Use Descriptive Test Names**
```javascript
// Good
await this.runTest('EXP level creation workflow', () => this.testLevelCreation());

// Bad  
await this.runTest('test1', () => this.test1());
```

### 2. **Handle Expected Errors Gracefully**
```javascript
async testFeatureWithExpectedError() {
    try {
        // Test code that might fail in test environment
        await someOperation();
        return true;
    } catch (error) {
        console.log(`   Expected error in test environment: ${error.message}`);
        return true; // Don't fail the test for expected errors
    }
}
```

### 3. **Validate Response Structure**
```javascript
async testFeatureResponse() {
    const interaction = this.createMockInteraction(2, 'command');
    await command.execute(interaction);
    
    // Validate response exists
    if (interaction._responses.length === 0) {
        throw new Error('No responses generated');
    }
    
    // Validate Components v2 compliance
    const response = interaction._responses[0];
    TestUtils.validateComponentsV2Response(response.options);
    
    return true;
}
```

### 4. **Use Database Tests Appropriately**
```javascript
async testDatabaseOperations() {
    const collection = this.mongoClient.db('test').collection('my_collection');
    
    // Test read operations
    const count = await collection.countDocuments();
    console.log(`   Documents in collection: ${count}`);
    
    // Avoid write operations in tests unless specifically testing them
    return count >= 0; // Always true, just testing connectivity
}
```

## 🔍 Debugging Tests

### Enable Verbose Logging
Set `NODE_ENV=development` in your `.env` file to enable detailed logging.

### Check Response Tracking
All mock interactions track responses in `_responses` array:
```javascript
console.log(`Responses: ${interaction._responses.length}`);
interaction._responses.forEach((response, i) => {
    console.log(`Response ${i}: ${response.type}`);
});
```

### Validate Environment
The base class validates required environment variables:
- `TOKEN` - Discord bot token
- `MONGO` - MongoDB connection string
- `OWNER` - Bot owner user ID
- `GUILDIDTWO` - Test guild ID

## 📈 Performance Considerations

- Tests run against live Discord API and database
- Each test initializes a new Discord client (isolated)
- Database connections are shared for efficiency
- Mock interactions are lightweight and fast
- Cleanup is automatic but can be called manually

## 🔗 Integration with CI/CD

Tests return appropriate exit codes:
- `0` - All tests passed
- `1` - Some tests failed or error occurred

Example GitHub Actions:
```yaml
- name: Run Bot Tests
  run: npm test
- name: Run Feature Tests
  run: |
    npm run test:exp
    npm run test:items
```

## 📝 Example Test Files

- `test_bot_comprehensive_refactored.js` - Full comprehensive test
- `test_exp_system_focused.js` - EXP system specific test
- `test_items_system_focused.js` - Items system specific test
- `examples/test_example_feature.js` - Template for new tests
