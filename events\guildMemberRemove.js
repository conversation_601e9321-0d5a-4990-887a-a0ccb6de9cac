const { Events, EmbedBuilder } = require('discord.js');

const { optimizedFindOne, optimizedInsertOne } = require("../utils/database-optimizer.js");
const { defaults } = require("../utils/default_db_structures");
const { sendLogContainer } = require('../utils/sendLog.js');
const { createMemberLeaveContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');

module.exports = {
	name: Events.GuildMemberRemove,
	async execute(client, member) {
		try {
			if (member.user.bot) return;

		var guildData = await optimizedFindOne("guilds", { id: member.guild.id });
		// if no guild data then create one??...
		if (guildData == null) {
			await optimizedInsertOne("guilds",
				defaults.guild(member.guild.id)
			)
			guildData = await optimizedFindOne("guilds", { id: member.guild.id });
		}

		var userData = await optimizedFindOne("member", { guildId: member.guild.id, userId: member.id });
		// if no guild data then create one??...
		if (userData == null) {
			await optimizedInsertOne("member",
				defaults.member({guildId: member.guild.id, userId: member.id })
			)
			userData = await optimizedFindOne("member", { guildId: member.guild.id, userId: member.id });
		}

		// Logging
		if (guildData.logs.enabled) {
			const channels = guildData.logs.channels.filter(l => l.events.includes("guildMemberRemove")).map(l => member.guild.channels.cache.get(l.id)).filter(ch => ch);

			// Only proceed if there are channels configured for this event
			if (channels.length > 0) {
				// Create Components v2 container for member leave
				const container = createMemberLeaveContainer({
					userMention: `<@${member.id}>`,
					userTag: member.user.tag,
					userId: member.id,
					joinedAt: member.joinedAt ? `<t:${Math.floor(member.joinedAt.getTime() / 1000)}:R>` : 'Unknown',
					memberCount: member.guild.memberCount
				});

				// Send container to all configured channels
				await sendLogContainer(member.guild.id, 'guildMemberRemove', container, client);
			}
		}
		// Sticky roles/nicknames
		// (Removed: sticky save logic is now only in guildMemberUpdate.js to prevent overwriting with empty data on leave)
		} catch (error) {
			console.error('[guildMemberRemove] Error processing member remove:', error);
			logger.error('guildMemberRemove', `Error processing member remove: ${error.message}`, client);
		}
	},
};