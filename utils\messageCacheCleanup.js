const { cleanupOldMessages, initializeIndexes } = require('./messageCache.js');

/**
 * Message Cache Cleanup Job
 * Runs periodically to clean up old cached messages
 */

/**
 * Run the cleanup job
 * @param {number} retentionDays - Number of days to retain messages (default: 7)
 */
async function runCleanup(retentionDays = 7) {
    console.log('[messageCacheCleanup] Starting cleanup job...');
    
    try {
        const deletedCount = await cleanupOldMessages(retentionDays);
        console.log(`[messageCacheCleanup] Cleanup completed. Removed ${deletedCount} old messages.`);
        return deletedCount;
    } catch (error) {
        console.error('[messageCacheCleanup] Cleanup failed:', error);
        return 0;
    }
}

/**
 * Initialize the message cache system
 */
async function initializeMessageCache() {
    console.log('[messageCacheCleanup] Initializing message cache system...');
    
    try {
        await initializeIndexes();
        console.log('[messageCacheCleanup] Message cache system initialized successfully.');
    } catch (error) {
        console.error('[messageCacheCleanup] Failed to initialize message cache:', error);
    }
}

/**
 * Start periodic cleanup (runs every 24 hours)
 * @param {number} intervalHours - Cleanup interval in hours (default: 24)
 * @param {number} retentionDays - Number of days to retain messages (default: 7)
 */
function startPeriodicCleanup(intervalHours = 24, retentionDays = 7) {
    const intervalMs = intervalHours * 60 * 60 * 1000;
    
    console.log(`[messageCacheCleanup] Starting periodic cleanup every ${intervalHours} hours (retention: ${retentionDays} days)`);
    
    // Run initial cleanup
    runCleanup(retentionDays);
    
    // Schedule periodic cleanup
    setInterval(() => {
        runCleanup(retentionDays);
    }, intervalMs);
}

module.exports = {
    runCleanup,
    initializeMessageCache,
    startPeriodicCleanup
};
