require('dotenv').config();

async function debugDatabaseUpdates() {
    console.log('🔍 Debugging Database Updates - Why EXP State Not Saving...\n');

    const { optimizedFindOne, optimizedUpdateOne } = require('../utils/database-optimizer.js');
    const testGuildId = process.env.GUILDIDTWO;
    
    console.log(`📊 Testing with guild: ${testGuildId}`);

    // Step 1: Check current database state
    console.log('\n1️⃣ Current Database State:');
    try {
        let guildData = await optimizedFindOne('guilds', { id: testGuildId });
        console.log(`   Guild document exists: ${!!guildData}`);
        console.log(`   EXP object: ${JSON.stringify(guildData?.exp || {}, null, 2)}`);
        console.log(`   Current enabled state: ${guildData?.exp?.enabled}`);
    } catch (error) {
        console.log(`   ❌ Error reading database: ${error.message}`);
        return;
    }

    // Step 2: Test manual database update (simulate button click)
    console.log('\n2️⃣ Testing Manual Database Update:');
    try {
        console.log('   Attempting to set enabled: true...');
        const updateResult = await optimizedUpdateOne("guilds",
            { id: testGuildId },
            { $set: { 'exp.enabled': true } }
        );
        console.log(`   Update result: ${JSON.stringify(updateResult, null, 2)}`);
        
        // Check if update worked
        let guildData = await optimizedFindOne('guilds', { id: testGuildId });
        console.log(`   After update - enabled state: ${guildData?.exp?.enabled}`);
        
        if (guildData?.exp?.enabled === true) {
            console.log('   ✅ Manual update WORKED');
        } else {
            console.log('   ❌ Manual update FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Error updating database: ${error.message}`);
    }

    // Step 3: Test the opposite state
    console.log('\n3️⃣ Testing Opposite State Update:');
    try {
        console.log('   Attempting to set enabled: false...');
        const updateResult = await optimizedUpdateOne("guilds",
            { id: testGuildId },
            { $set: { 'exp.enabled': false } }
        );
        console.log(`   Update result: ${JSON.stringify(updateResult, null, 2)}`);
        
        // Check if update worked
        let guildData = await optimizedFindOne('guilds', { id: testGuildId });
        console.log(`   After update - enabled state: ${guildData?.exp?.enabled}`);
        
        if (guildData?.exp?.enabled === false) {
            console.log('   ✅ Opposite update WORKED');
        } else {
            console.log('   ❌ Opposite update FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Error updating database: ${error.message}`);
    }

    // Step 4: Check if guild document exists at all
    console.log('\n4️⃣ Checking Guild Document Structure:');
    try {
        let guildData = await optimizedFindOne('guilds', { id: testGuildId });
        if (!guildData) {
            console.log('   ❌ Guild document does NOT exist - this could be the problem!');
            console.log('   Creating guild document...');
            
            // Try to create the guild document
            const insertResult = await require('../utils/database-optimizer.js').optimizedInsertOne('guilds', {
                id: testGuildId,
                exp: { enabled: true }
            });
            console.log(`   Insert result: ${JSON.stringify(insertResult, null, 2)}`);
            
            // Check if creation worked
            guildData = await optimizedFindOne('guilds', { id: testGuildId });
            if (guildData) {
                console.log('   ✅ Guild document created successfully');
            } else {
                console.log('   ❌ Guild document creation failed');
            }
        } else {
            console.log('   ✅ Guild document exists');
            console.log(`   Document structure: ${JSON.stringify(guildData, null, 2)}`);
        }
    } catch (error) {
        console.log(`   ❌ Error checking guild document: ${error.message}`);
    }

    // Step 5: Test upsert operation (what the button handler should use)
    console.log('\n5️⃣ Testing Upsert Operation:');
    try {
        console.log('   Attempting upsert with enabled: true...');
        const upsertResult = await optimizedUpdateOne("guilds",
            { id: testGuildId },
            { $set: { 'exp.enabled': true } },
            { upsert: true }
        );
        console.log(`   Upsert result: ${JSON.stringify(upsertResult, null, 2)}`);
        
        // Check if upsert worked
        let guildData = await optimizedFindOne('guilds', { id: testGuildId });
        console.log(`   After upsert - enabled state: ${guildData?.exp?.enabled}`);
        
        if (guildData?.exp?.enabled === true) {
            console.log('   ✅ Upsert WORKED');
        } else {
            console.log('   ❌ Upsert FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Error with upsert: ${error.message}`);
    }

    // Step 6: Check what the actual button handler is doing
    console.log('\n6️⃣ Checking Button Handler Code:');
    const expCode = require('fs').readFileSync('commands/utility/exp.js', 'utf8');
    
    // Find the button handler
    const buttonHandlerMatch = expCode.match(/if \(interaction\.customId === 'exp-global-disable'[\s\S]*?return \[selectMenu, container, globalButtonRow\];/);
    if (buttonHandlerMatch) {
        console.log('   ✅ Button handler found in code');
        
        // Check if it uses upsert
        const hasUpsert = buttonHandlerMatch[0].includes('upsert');
        console.log(`   Uses upsert: ${hasUpsert}`);
        
        if (!hasUpsert) {
            console.log('   ❌ PROBLEM: Button handler does NOT use upsert!');
            console.log('   This means if guild document doesn\'t exist, update will fail silently');
        }
    } else {
        console.log('   ❌ Button handler NOT found in code');
    }

    // Step 7: Test the exact same operation as button handler
    console.log('\n7️⃣ Simulating Exact Button Handler Operation:');
    try {
        // This is exactly what the button handler does
        let guildData = await optimizedFindOne("guilds", { id: testGuildId });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };

        console.log(`   Before update - guildData.exp.enabled: ${guildData.exp.enabled}`);
        
        // Toggle the enabled state (simulate clicking enable)
        const newState = true; // exp-global-enable
        console.log(`   Setting newState to: ${newState}`);
        
        const updateResult = await optimizedUpdateOne("guilds",
            { id: testGuildId },
            { $set: { 'exp.enabled': newState } }
        );
        console.log(`   Update result: ${JSON.stringify(updateResult, null, 2)}`);
        
        // Check result
        guildData = await optimizedFindOne("guilds", { id: testGuildId });
        console.log(`   After update - enabled state: ${guildData?.exp?.enabled}`);
        
        if (guildData?.exp?.enabled === newState) {
            console.log('   ✅ Button handler simulation WORKED');
        } else {
            console.log('   ❌ Button handler simulation FAILED');
            console.log('   🔍 This is why the button isn\'t working!');
        }
    } catch (error) {
        console.log(`   ❌ Error simulating button handler: ${error.message}`);
    }

    console.log('\n🎯 Diagnosis Complete');
    process.exit(0);
}

debugDatabaseUpdates().catch(console.error);
