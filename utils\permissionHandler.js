/**
 * Permission-Based Feature Disabling System
 * Provides consistent permission checking and UI state management
 * for features that require specific Discord permissions
 */

const { PermissionFlagsBits, ButtonBuilder, ButtonStyle } = require('discord.js');
const { TextDisplayBuilder } = require('discord.js');

/**
 * Feature permission requirements mapping
 * Maps feature names to their required Discord permissions
 * Based on the comprehensive permissions from the bot invite link
 */
const FEATURE_PERMISSIONS = {
    // Core bot functionality (ALL features need these)
    core: [
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ReadMessageHistory,
        PermissionFlagsBits.EmbedLinks,
        PermissionFlagsBits.AttachFiles,
        PermissionFlagsBits.UseExternalEmojis,
        PermissionFlagsBits.AddReactions,
        PermissionFlagsBits.UseApplicationCommands
    ],

    // Features with enable/disable buttons
    sticky: [
        PermissionFlagsBits.ManageRoles,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel
    ],
    exp: [
        PermissionFlagsBits.ManageRoles, // For level-up role assignments
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel
    ],
    logs: [
        PermissionFlagsBits.ViewAuditLog,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.ManageMessages // For log message management
    ],
    opener: [
        PermissionFlagsBits.ManageThreads,
        PermissionFlagsBits.CreatePublicThreads,
        PermissionFlagsBits.CreatePrivateThreads,
        PermissionFlagsBits.SendMessagesInThreads,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel
    ],
    dehoist: [
        PermissionFlagsBits.ManageNicknames,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel
    ],
    items: [
        PermissionFlagsBits.ManageMessages, // For item drop notifications
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.UseExternalEmojis // For item emojis
    ],

    // Features without enable/disable buttons but need permission checking
    'transcribe-voice': [
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.ReadMessageHistory,
        PermissionFlagsBits.AttachFiles // For transcription files
    ],
    you: [
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.ReadMessageHistory,
        PermissionFlagsBits.UseExternalEmojis,
        PermissionFlagsBits.EmbedLinks
    ],
    '17': [
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.UseExternalEmojis,
        PermissionFlagsBits.EmbedLinks,
        PermissionFlagsBits.ViewGuildInsights // For stats display
    ],
    lookup: [
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.ReadMessageHistory,
        PermissionFlagsBits.UseExternalEmojis,
        PermissionFlagsBits.EmbedLinks
    ],

    // Voice-related features
    voice: [
        PermissionFlagsBits.Connect,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ViewChannel
    ],

    // Admin features (require Administrator)
    clearData: [PermissionFlagsBits.Administrator],
    backup: [PermissionFlagsBits.Administrator],
    owner: [PermissionFlagsBits.Administrator]
};

/**
 * Human-readable permission names for display
 */
const PERMISSION_NAMES = {
    // Core permissions
    [PermissionFlagsBits.ViewChannel]: 'View Channel',
    [PermissionFlagsBits.SendMessages]: 'Send Messages',
    [PermissionFlagsBits.ReadMessageHistory]: 'Read Message History',
    [PermissionFlagsBits.EmbedLinks]: 'Embed Links',
    [PermissionFlagsBits.AttachFiles]: 'Attach Files',
    [PermissionFlagsBits.UseExternalEmojis]: 'Use External Emojis',
    [PermissionFlagsBits.AddReactions]: 'Add Reactions',
    [PermissionFlagsBits.UseApplicationCommands]: 'Use Application Commands',

    // Feature-specific permissions
    [PermissionFlagsBits.ManageRoles]: 'Manage Roles',
    [PermissionFlagsBits.ViewAuditLog]: 'View Audit Log',
    [PermissionFlagsBits.ManageThreads]: 'Manage Threads',
    [PermissionFlagsBits.CreatePublicThreads]: 'Create Public Threads',
    [PermissionFlagsBits.CreatePrivateThreads]: 'Create Private Threads',
    [PermissionFlagsBits.SendMessagesInThreads]: 'Send Messages in Threads',
    [PermissionFlagsBits.ManageNicknames]: 'Manage Nicknames',
    [PermissionFlagsBits.ManageMessages]: 'Manage Messages',
    [PermissionFlagsBits.Connect]: 'Connect to Voice',
    [PermissionFlagsBits.ViewGuildInsights]: 'View Guild Insights',

    // Admin permissions
    [PermissionFlagsBits.Administrator]: 'Administrator'
};

/**
 * Critical permissions that completely break bot functionality
 */
const CRITICAL_PERMISSIONS = [
    PermissionFlagsBits.ViewChannel,
    PermissionFlagsBits.SendMessages,
    PermissionFlagsBits.UseApplicationCommands
];

/**
 * Check if bot has required permissions for a feature
 * @param {Guild} guild - Discord guild
 * @param {string} feature - Feature name
 * @returns {Object} Permission check result
 */
function checkFeaturePermissions(guild, feature) {
    const requiredPermissions = FEATURE_PERMISSIONS[feature];

    if (!requiredPermissions || requiredPermissions.length === 0) {
        return {
            hasPermissions: true,
            missingPermissions: [],
            missingPermissionNames: [],
            hasCriticalPermissions: true,
            missingCriticalPermissions: [],
            missingCriticalPermissionNames: []
        };
    }

    const botMember = guild.members.me;
    if (!botMember) {
        return {
            hasPermissions: false,
            missingPermissions: requiredPermissions,
            missingPermissionNames: requiredPermissions.map(perm => PERMISSION_NAMES[perm] || 'Unknown Permission'),
            hasCriticalPermissions: false,
            missingCriticalPermissions: CRITICAL_PERMISSIONS,
            missingCriticalPermissionNames: CRITICAL_PERMISSIONS.map(perm => PERMISSION_NAMES[perm] || 'Unknown Permission'),
            error: 'Bot member not found'
        };
    }

    const botPermissions = botMember.permissions;
    const missingPermissions = requiredPermissions.filter(permission =>
        !botPermissions.has(permission)
    );

    const missingPermissionNames = missingPermissions.map(permission =>
        PERMISSION_NAMES[permission] || 'Unknown Permission'
    );

    // Check critical permissions separately
    const missingCriticalPermissions = CRITICAL_PERMISSIONS.filter(permission =>
        !botPermissions.has(permission)
    );

    const missingCriticalPermissionNames = missingCriticalPermissions.map(permission =>
        PERMISSION_NAMES[permission] || 'Unknown Permission'
    );

    return {
        hasPermissions: missingPermissions.length === 0,
        missingPermissions,
        missingPermissionNames,
        hasCriticalPermissions: missingCriticalPermissions.length === 0,
        missingCriticalPermissions,
        missingCriticalPermissionNames,
        requiredPermissions,
        requiredPermissionNames: requiredPermissions.map(perm => PERMISSION_NAMES[perm] || 'Unknown Permission')
    };
}

/**
 * Create a status message for missing permissions
 * @param {Array<string>} missingPermissionNames - Array of missing permission names
 * @returns {TextDisplayBuilder} Status message component
 */
function createPermissionStatusMessage(missingPermissionNames) {
    const permissionList = missingPermissionNames.join(', ');
    const permissionWord = missingPermissionNames.length === 1 ? 'permission' : 'permissions';
    return new TextDisplayBuilder()
        .setContent(`**status:** 🔒 Missing required ${permissionWord}: ${permissionList}`);
}

/**
 * Create a disabled toggle button for missing permissions
 * @param {string} feature - Feature name
 * @param {Array<string>} missingPermissionNames - Array of missing permission names
 * @param {string} customId - Button custom ID
 * @param {boolean} currentlyEnabled - Whether feature is currently enabled
 * @returns {ButtonBuilder} Disabled button with consistent styling
 */
function createDisabledToggleButton(feature, missingPermissionNames, customId = 'feature-toggle', currentlyEnabled = false) {
    // Keep the same visual styling as normal buttons, just disable them
    return new ButtonBuilder()
        .setCustomId(customId)
        .setLabel(currentlyEnabled ? 'disable' : 'enable') // lowercase to match other features
        .setStyle(currentlyEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(true); // Only difference is disabled state
}

/**
 * Create an enabled toggle button when permissions are available
 * @param {boolean} currentlyEnabled - Whether feature is currently enabled
 * @param {string} customId - Button custom ID
 * @returns {ButtonBuilder} Enabled toggle button
 */
function createEnabledToggleButton(currentlyEnabled, customId = 'feature-toggle') {
    return new ButtonBuilder()
        .setCustomId(customId)
        .setLabel(currentlyEnabled ? 'disable' : 'enable') // lowercase to match other features
        .setStyle(currentlyEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(false);
}

/**
 * Get permission-aware toggle button for a feature
 * @param {Guild} guild - Discord guild
 * @param {string} feature - Feature name
 * @param {boolean} currentlyEnabled - Whether feature is currently enabled
 * @param {string} customId - Button custom ID
 * @returns {Object} Button and permission status
 */
function getPermissionAwareToggleButton(guild, feature, currentlyEnabled, customId = 'feature-toggle') {
    const permissionCheck = checkFeaturePermissions(guild, feature);

    if (!permissionCheck.hasPermissions) {
        return {
            button: createDisabledToggleButton(feature, permissionCheck.missingPermissionNames, customId, currentlyEnabled),
            statusMessage: createPermissionStatusMessage(permissionCheck.missingPermissionNames),
            hasPermissions: false,
            missingPermissions: permissionCheck.missingPermissionNames
        };
    }

    return {
        button: createEnabledToggleButton(currentlyEnabled, customId),
        statusMessage: null,
        hasPermissions: true,
        missingPermissions: []
    };
}

/**
 * Check if user has permission to configure a feature (separate from bot permissions)
 * @param {GuildMember} member - Discord guild member
 * @param {string} feature - Feature name
 * @returns {boolean} Whether user can configure the feature
 */
function hasUserFeaturePermission(member, feature) {
    if (!member || !member.guild) return false;
    
    // Server owners always have full access
    if (member.guild.ownerId === member.user.id) return true;
    
    // Bot owner always has access
    if (member.user.id === process.env.OWNER) return true;
    
    // Use global permission function if available
    if (global.hasFeaturePermission) {
        return global.hasFeaturePermission(member, feature);
    }
    
    // Fallback: require Administrator permission
    return member.permissions.has(PermissionFlagsBits.Administrator);
}

/**
 * Create a comprehensive feature status for permission-aware interfaces
 * @param {Guild} guild - Discord guild
 * @param {GuildMember} member - Discord guild member
 * @param {string} feature - Feature name
 * @param {boolean} currentlyEnabled - Whether feature is currently enabled
 * @param {string} customId - Button custom ID
 * @returns {Object} Complete feature status
 */
function getFeatureStatus(guild, member, feature, currentlyEnabled, customId = 'feature-toggle') {
    const userHasPermission = hasUserFeaturePermission(member, feature);
    const botPermissionCheck = checkFeaturePermissions(guild, feature);
    
    // If user doesn't have permission, show access denied
    if (!userHasPermission) {
        return {
            button: new ButtonBuilder()
                .setCustomId(customId)
                .setLabel('Access Denied')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(true)
                .setEmoji('🚫'),
            statusMessage: new TextDisplayBuilder()
                .setContent('**status:** ❌ Access denied. Administrator permission required.'),
            hasPermissions: false,
            userHasPermission: false,
            botHasPermissions: botPermissionCheck.hasPermissions,
            missingPermissions: []
        };
    }
    
    // If bot doesn't have permissions, show permission requirements
    if (!botPermissionCheck.hasPermissions) {
        return {
            button: createDisabledToggleButton(feature, botPermissionCheck.missingPermissionNames, customId, currentlyEnabled),
            statusMessage: createPermissionStatusMessage(botPermissionCheck.missingPermissionNames),
            hasPermissions: false,
            userHasPermission: true,
            botHasPermissions: false,
            missingPermissions: botPermissionCheck.missingPermissionNames
        };
    }
    
    // Both user and bot have permissions
    return {
        button: createEnabledToggleButton(currentlyEnabled, customId),
        statusMessage: null,
        hasPermissions: true,
        userHasPermission: true,
        botHasPermissions: true,
        missingPermissions: []
    };
}

/**
 * Add required permissions to a feature (for dynamic permission requirements)
 * @param {string} feature - Feature name
 * @param {Array<bigint>} permissions - Array of PermissionFlagsBits
 */
function addFeaturePermissions(feature, permissions) {
    if (!FEATURE_PERMISSIONS[feature]) {
        FEATURE_PERMISSIONS[feature] = [];
    }
    
    permissions.forEach(permission => {
        if (!FEATURE_PERMISSIONS[feature].includes(permission)) {
            FEATURE_PERMISSIONS[feature].push(permission);
        }
    });
}

/**
 * Remove permissions from a feature
 * @param {string} feature - Feature name
 * @param {Array<bigint>} permissions - Array of PermissionFlagsBits to remove
 */
function removeFeaturePermissions(feature, permissions) {
    if (!FEATURE_PERMISSIONS[feature]) return;
    
    FEATURE_PERMISSIONS[feature] = FEATURE_PERMISSIONS[feature].filter(
        permission => !permissions.includes(permission)
    );
}

/**
 * Check permissions for features without enable/disable buttons
 * Returns a status message if critical permissions are missing
 * @param {Guild} guild - Discord guild
 * @param {string} feature - Feature name
 * @returns {Object|null} Status message if permissions missing, null if OK
 */
function checkNonToggleFeaturePermissions(guild, feature) {
    const permissionCheck = checkFeaturePermissions(guild, feature);

    // If critical permissions are missing, return error status
    if (!permissionCheck.hasCriticalPermissions) {
        const criticalPermissionWord = permissionCheck.missingCriticalPermissionNames.length === 1 ? 'permission' : 'permissions';
        return {
            statusMessage: new TextDisplayBuilder()
                .setContent(`**status:** 🚫 Bot cannot function: Missing ${criticalPermissionWord} ${permissionCheck.missingCriticalPermissionNames.join(', ')}`),
            canFunction: false,
            missingPermissions: permissionCheck.missingCriticalPermissionNames
        };
    }

    // If feature-specific permissions are missing, return warning status
    if (!permissionCheck.hasPermissions) {
        const nonCriticalMissing = permissionCheck.missingPermissionNames.filter(
            name => !permissionCheck.missingCriticalPermissionNames.includes(name)
        );

        if (nonCriticalMissing.length > 0) {
            const nonCriticalPermissionWord = nonCriticalMissing.length === 1 ? 'permission' : 'permissions';
            return {
                statusMessage: new TextDisplayBuilder()
                    .setContent(`**status:** ⚠️ Limited functionality: Missing ${nonCriticalPermissionWord} ${nonCriticalMissing.join(', ')}`),
                canFunction: true,
                missingPermissions: nonCriticalMissing
            };
        }
    }

    return null; // All permissions OK
}

/**
 * Get all features that require specific permissions
 * @returns {Object} Features and their permission requirements
 */
function getAllFeaturePermissions() {
    const result = {};

    Object.entries(FEATURE_PERMISSIONS).forEach(([feature, permissions]) => {
        result[feature] = {
            permissions,
            permissionNames: permissions.map(perm => PERMISSION_NAMES[perm] || 'Unknown Permission'),
            hasCriticalPermissions: permissions.some(perm => CRITICAL_PERMISSIONS.includes(perm)),
            criticalPermissions: permissions.filter(perm => CRITICAL_PERMISSIONS.includes(perm)),
            nonCriticalPermissions: permissions.filter(perm => !CRITICAL_PERMISSIONS.includes(perm))
        };
    });

    return result;
}

/**
 * Get features by permission requirement type
 * @returns {Object} Categorized features
 */
function getFeaturesByPermissionType() {
    const toggleFeatures = ['sticky', 'exp', 'logs', 'opener', 'dehoist', 'items'];
    const nonToggleFeatures = ['transcribe-voice', 'you', '17', 'lookup'];
    const adminFeatures = ['clearData', 'backup', 'owner'];

    return {
        toggleFeatures,      // Features with enable/disable buttons
        nonToggleFeatures,   // Features without toggle buttons
        adminFeatures,       // Admin-only features
        allFeatures: [...toggleFeatures, ...nonToggleFeatures, ...adminFeatures]
    };
}

module.exports = {
    // Core functions
    checkFeaturePermissions,
    createPermissionStatusMessage,
    createDisabledToggleButton,
    createEnabledToggleButton,
    getPermissionAwareToggleButton,
    hasUserFeaturePermission,
    getFeatureStatus,

    // Non-toggle feature functions
    checkNonToggleFeaturePermissions,
    getFeaturesByPermissionType,

    // Configuration functions
    addFeaturePermissions,
    removeFeaturePermissions,
    getAllFeaturePermissions,

    // Constants
    FEATURE_PERMISSIONS,
    PERMISSION_NAMES,
    CRITICAL_PERMISSIONS
};
