/**
 * Global Level Notifications System (Enterprise-Grade Performance Optimized)
 * Handles notifications for global level-ups in notification center and DMs
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */


const { optimizedInsertOne, optimizedFind, optimizedUpdateOne, optimizedFindOne } = require('./database-optimizer.js');
const { ContainerBuilder, TextDisplayBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, SectionBuilder, SeparatorBuilder, SeparatorSpacingSize, ThumbnailBuilder, MessageFlags } = require('discord.js');
const { OPERATION_COLORS } = require('./colors.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const globalLevelNotificationsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    notificationsProcessed: 0,
    dmsSent: 0,
    notificationsDismissed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const userNotificationsCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user notifications
const userSettingsCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user DM settings
const notificationDisplayCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for notification displays

// Register caches for global cleanup (MANDATORY)
registerCache(userNotificationsCache);
registerCache(userSettingsCache);
registerCache(notificationDisplayCache);

/**
 * Build level-up container for notification center
 * @param {Object} levelData - Level up data
 * @param {string} userAvatar - User avatar URL
 * @param {Object} globalLevelData - Global level configuration data
 * @returns {ContainerBuilder} Level-up container
 */
function buildLevelUpContainer(levelData, userAvatar, globalLevelData) {
    // Get level name and emoji from global level data
    const levelName = globalLevelData?.name || 'Unknown';
    const levelEmoji = globalLevelData?.icon || '🌟';

    const profileThumbnail = new ThumbnailBuilder({
        media: { url: userAvatar }
    });

    const thumbnailSection = new SectionBuilder()
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent('## level up'),
            new TextDisplayBuilder().setContent(`> you reached **global level ${levelData.newLevel}**`)
        )
        .setThumbnailAccessory(profileThumbnail);

    const container = new ContainerBuilder()
        .addSectionComponents(thumbnailSection)
        .addSeparatorComponents(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false))
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(`**title gained:** ${levelEmoji} ${levelName}\n**total global exp:** ${levelData.newExp.toLocaleString()}`)
        )
        .setAccentColor(OPERATION_COLORS.ENTITY);

    return container;
}

/**
 * Get cached user DM settings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User settings data
 */
async function getCachedUserSettings(userId) {
    const startTime = Date.now();
    const cacheKey = `user_settings_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = userSettingsCache.get(cacheKey);
        if (cached) {
            globalLevelNotificationsMetrics.cacheHits++;
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] ⚡ User settings cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        globalLevelNotificationsMetrics.cacheMisses++;
        globalLevelNotificationsMetrics.databaseQueries++;

        const userData = await optimizedFindOne('users', { id: userId });
        const settings = userData || { globalLevelDMNotificationsEnabled: false };

        // Cache the result
        userSettingsCache.set(cacheKey, settings);

        // Performance tracking
        const duration = Date.now() - startTime;
        globalLevelNotificationsMetrics.averageQueryTime =
            (globalLevelNotificationsMetrics.averageQueryTime * (globalLevelNotificationsMetrics.databaseQueries - 1) + duration) /
            globalLevelNotificationsMetrics.databaseQueries;

        if (globalLevelNotificationsMetrics.verboseLogging || duration > 100) {
            console.log(`[globalLevelNotifications] ✅ User settings fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        return settings;
    } catch (error) {
        console.error(`[globalLevelNotifications] ❌ Error getting user settings for ${userId}:`, error);
        return { globalLevelDMNotificationsEnabled: false };
    }
}

/**
 * Add global level-up notification to user's queue (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced error handling with performance monitoring and cache invalidation
 * @param {string} userId - User ID
 * @param {Object} levelUpData - Level up information
 * @returns {Promise<boolean>} Success status
 */
async function addGlobalLevelNotification(userId, levelUpData) {
    const startTime = Date.now();

    try {
        globalLevelNotificationsMetrics.databaseQueries++;
        globalLevelNotificationsMetrics.notificationsProcessed++;

        const notification = {
            userId: userId,
            levelUpData: levelUpData,
            createdAt: new Date(),
            viewed: false
        };

        await optimizedInsertOne('global_level_notifications', notification);

        // Invalidate user notifications cache
        invalidateUserNotificationsCache(userId);

        // Performance tracking
        const duration = Date.now() - startTime;
        globalLevelNotificationsMetrics.averageQueryTime =
            (globalLevelNotificationsMetrics.averageQueryTime * (globalLevelNotificationsMetrics.databaseQueries - 1) + duration) /
            globalLevelNotificationsMetrics.databaseQueries;

        if (globalLevelNotificationsMetrics.verboseLogging || duration > 100) {
            console.log(`[globalLevelNotifications] ✅ Added global level notification for ${userId} (level ${levelUpData.newLevel}) in ${duration}ms`);
        }

        return true;

    } catch (error) {
        console.error('[globalLevelNotifications] ❌ Error adding notification:', error);
        return false;
    }
}

/**
 * Get unviewed global level notifications for a user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} userId - User ID
 * @param {number} limit - Maximum number of notifications to return
 * @returns {Promise<Array>} Array of notifications
 */
async function getGlobalLevelNotifications(userId, limit = 5) {
    const startTime = Date.now();
    const cacheKey = `notifications_${userId}_${limit}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = userNotificationsCache.get(cacheKey);
        if (cached) {
            globalLevelNotificationsMetrics.cacheHits++;
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] ⚡ Notifications cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        globalLevelNotificationsMetrics.cacheMisses++;
        globalLevelNotificationsMetrics.databaseQueries++;

        const notifications = await optimizedFind('global_level_notifications', {
            userId: userId,
            viewed: false
        }, {
            projection: {
                levelUpData: 1,
                createdAt: 1,
                _id: 1
            },
            sort: { createdAt: -1 },
            limit: limit
        });

        // Cache the result
        userNotificationsCache.set(cacheKey, notifications);

        // Performance tracking
        const duration = Date.now() - startTime;
        globalLevelNotificationsMetrics.averageQueryTime =
            (globalLevelNotificationsMetrics.averageQueryTime * (globalLevelNotificationsMetrics.databaseQueries - 1) + duration) /
            globalLevelNotificationsMetrics.databaseQueries;

        if (globalLevelNotificationsMetrics.verboseLogging || duration > 100) {
            console.log(`[globalLevelNotifications] ✅ Notifications fetched for ${userId}: ${notifications.length} found in ${duration}ms - cached for future access`);
        }

        return notifications;

    } catch (error) {
        console.error('[globalLevelNotifications] ❌ Error getting notifications:', error);
        return [];
    }
}

/**
 * Dismiss (mark as viewed) a global level notification (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced error handling with performance monitoring and cache invalidation
 * @param {string} notificationId - Notification ID
 * @param {string} userId - User ID for cache invalidation
 * @returns {Promise<boolean>} Success status
 */
async function dismissGlobalLevelNotification(notificationId, userId = null) {
    const startTime = Date.now();

    try {
        globalLevelNotificationsMetrics.databaseQueries++;
        globalLevelNotificationsMetrics.notificationsDismissed++;

        const { ObjectId } = require('mongodb');

        await optimizedUpdateOne('global_level_notifications',
            { _id: new ObjectId(notificationId) },
            { $set: { viewed: true, viewedAt: new Date() } }
        );

        // Invalidate related caches
        if (userId) {
            invalidateUserNotificationsCache(userId);
            invalidateNotificationDisplayCache(userId);
        }

        // Performance tracking
        const duration = Date.now() - startTime;
        globalLevelNotificationsMetrics.averageQueryTime =
            (globalLevelNotificationsMetrics.averageQueryTime * (globalLevelNotificationsMetrics.databaseQueries - 1) + duration) /
            globalLevelNotificationsMetrics.databaseQueries;

        if (globalLevelNotificationsMetrics.verboseLogging || duration > 100) {
            console.log(`[globalLevelNotifications] ✅ Dismissed global level notification ${notificationId} in ${duration}ms`);
        }

        return true;

    } catch (error) {
        console.error('[globalLevelNotifications] ❌ Error dismissing notification:', error);
        return false;
    }
}

/**
 * Build global level notification display container (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and parallel processing
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} Notification container or null if no notifications
 */
async function buildGlobalLevelNotificationDisplay(userId) {
    const startTime = Date.now();
    const cacheKey = `display_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = notificationDisplayCache.get(cacheKey);
        if (cached) {
            globalLevelNotificationsMetrics.cacheHits++;
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] ⚡ Display cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        globalLevelNotificationsMetrics.cacheMisses++;

        // OPTIMIZED: Enhanced parallel operations with cached data fetching
        const [notificationsResult, levelsResult] = await Promise.allSettled([
            getGlobalLevelNotifications(userId, 1),
            require('./globalLevels.js').getCachedGlobalLevels()
        ]);

        // Track parallel operation
        globalLevelNotificationsMetrics.parallelOperations++;

        // Handle results with graceful fallbacks
        const notifications = notificationsResult.status === 'fulfilled' ? notificationsResult.value : [];
        const levels = levelsResult.status === 'fulfilled' ? levelsResult.value : [];

        // Track partial failures
        const failures = [notificationsResult, levelsResult].filter(r => r.status === 'rejected');
        if (failures.length > 0) {
            globalLevelNotificationsMetrics.partialFailures++;
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] ⚠️ ${failures.length} partial failures in parallel data fetching`);
            }
        }

        if (notifications.length === 0) {
            const result = null; // No notifications to display
            notificationDisplayCache.set(cacheKey, result);
            return result;
        }

        const notification = notifications[0];
        const levelData = notification.levelUpData;
        const globalLevelData = levels.find(level => level.level === levelData.newLevel);

        // For notification center, we need a user avatar - try to get it from client if available
        let userAvatar = 'https://cdn.discordapp.com/embed/avatars/0.png'; // Default avatar
        try {
            // Try to get user from client if available (this might not always work in notification center context)
            if (global.client) {
                const user = await global.client.users.fetch(userId).catch(() => null);
                if (user) {
                    userAvatar = user.displayAvatarURL();
                }
            }
        } catch (error) {
            // Use default avatar if we can't fetch user
        }

        // Build level-up container for notification center
        const container = buildLevelUpContainer(levelData, userAvatar, globalLevelData);

        // Add dismiss button
        const dismissButton = new ButtonBuilder()
            .setCustomId(`dismiss-global-level-${notification._id}`)
            .setLabel('dismiss')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('✅');

        const buttonRow = new ActionRowBuilder().addComponents(dismissButton);

        const result = { container, buttonRow, notificationId: notification._id };

        // Cache the result (short TTL since notifications change frequently)
        notificationDisplayCache.set(cacheKey, result);

        // Performance tracking
        const duration = Date.now() - startTime;
        if (globalLevelNotificationsMetrics.verboseLogging || duration > 200) {
            console.log(`[globalLevelNotifications] ✅ Display built for ${userId} in ${duration}ms - cached for future access`);
        }

        return result;

    } catch (error) {
        console.error('[globalLevelNotifications] ❌ Error building notification display:', error);
        return null;
    }
}

/**
 * Send global level-up DM notification with new Components v2 design (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced parallel processing with cached user settings and performance monitoring
 * @param {string} userId - User ID
 * @param {Object} levelUpData - Level up information
 * @param {Object} client - Discord client
 * @returns {Promise<boolean>} Success status
 */
async function sendGlobalLevelDM(userId, levelUpData, client) {
    const startTime = Date.now();

    try {
        globalLevelNotificationsMetrics.parallelOperations++;

        // OPTIMIZED: Enhanced parallel operations with cached data fetching
        const [userSettingsResult, userResult, levelsResult] = await Promise.allSettled([
            getCachedUserSettings(userId),
            client.users.fetch(userId).catch(() => null),
            require('./globalLevels.js').getCachedGlobalLevels()
        ]);

        // Handle results with graceful fallbacks
        const userSettings = userSettingsResult.status === 'fulfilled' ? userSettingsResult.value : { globalLevelDMNotificationsEnabled: false };
        const user = userResult.status === 'fulfilled' ? userResult.value : null;
        const levels = levelsResult.status === 'fulfilled' ? levelsResult.value : [];

        // Track partial failures
        const failures = [userSettingsResult, userResult, levelsResult].filter(r => r.status === 'rejected');
        if (failures.length > 0) {
            globalLevelNotificationsMetrics.partialFailures++;
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] ⚠️ ${failures.length} partial failures in DM preparation`);
            }
        }

        const globalLevelDMEnabled = userSettings?.globalLevelDMNotificationsEnabled === true;

        if (!globalLevelDMEnabled) {
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] Global level DM notifications disabled for user ${userId}`);
            }
            return false;
        }

        if (!user) {
            if (globalLevelNotificationsMetrics.verboseLogging) {
                console.log(`[globalLevelNotifications] Could not fetch user ${userId}`);
            }
            return false;
        }

        const globalLevelData = levels.find(level => level.level === levelUpData.newLevel);

        // Build level-up container
        const levelUpContainer = buildLevelUpContainer(levelUpData, user.displayAvatarURL(), globalLevelData);

        // Create dynamic message text based on rewards
        const levelName = globalLevelData?.name || 'Unknown';
        const levelEmoji = globalLevelData?.icon || '🌟';

        const hasItems = levelUpData.levelRewards && levelUpData.levelRewards.items && levelUpData.levelRewards.items.length > 0;
        const hasOtherRewards = (levelUpData.levelRewards && levelUpData.levelRewards.xpBooster) || (levelUpData.levelRewards && levelUpData.levelRewards.dropBooster);
        const hasAnyRewards = hasItems || hasOtherRewards;

        // Build main message text
        let dmMessageText = `You leveled up to ${levelEmoji} **${levelName}**, global level ${levelUpData.newLevel}`;

        if (hasAnyRewards) {
            dmMessageText += ', and received multiple rewards:';
        } else {
            dmMessageText += '.';
        }

        const dmMessageHeader = new TextDisplayBuilder().setContent(dmMessageText);

        // Prepare components array starting with main message
        const components = [dmMessageHeader];

        // Add rewards list if there are any rewards
        if (hasAnyRewards) {
            let rewardsText = '';

            // Add title reward (always present on level up)
            rewardsText += `- ${levelEmoji} ${levelName} title\n`;

            // Add item rewards
            if (hasItems) {
                for (const item of levelUpData.levelRewards.items) {
                    const itemName = item.itemName || item.name || 'Unknown Item';
                    const itemEmote = item.itemEmote || item.emote || '📦';
                    rewardsText += `- 1 item (${itemEmote} ${itemName})\n`;
                }
            }

            // Add other rewards
            if (levelUpData.levelRewards && levelUpData.levelRewards.xpBooster) {
                rewardsText += `- ${levelUpData.levelRewards.xpBooster}x exp boost\n`;
            }
            if (levelUpData.levelRewards && levelUpData.levelRewards.dropBooster) {
                rewardsText += `- ${levelUpData.levelRewards.dropBooster}x drop boost\n`;
            }

            const rewardsDisplay = new TextDisplayBuilder().setContent(rewardsText.trim());
            components.push(rewardsDisplay);
        }

        // Add spacer with no divider before level up container
        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large).setDivider(false));

        // Add the level up container
        components.push(levelUpContainer);

        // Add item containers for LEVEL_UP items
        if (hasItems) {
            const { buildFoundItemContainer } = require('../commands/utility/items.js');

            for (const item of levelUpData.levelRewards.items) {
                // FIXED: Create item data structure with correct field mapping for container
                const itemData = {
                    // Use inventory item field names (itemName, itemType, etc.)
                    itemName: item.itemName || item.name,
                    itemType: item.itemType || item.type,
                    itemRarity: item.itemRarity || item.rarity,
                    itemEmote: item.itemEmote || item.emote,
                    itemDescription: item.itemDescription || item.description,
                    // Also provide fallback field names for compatibility
                    name: item.itemName || item.name,
                    type: item.itemType || item.type,
                    rarity: item.itemRarity || item.rarity,
                    emote: item.itemEmote || item.emote,
                    description: item.itemDescription || item.description
                };

                // Create catch data from the inventory item
                const catchData = item.catchData || {};

                // Create context
                const context = {
                    user: user,
                    server: null, // Global level-ups have no server context
                    location: 'level up'
                };

                // Create leaderboard results from the item
                const leaderboardResults = item.leaderboardResults || {
                    serverRank: null,
                    globalRank: null,
                    isNewServerRecord: false,
                    isNewGlobalRecord: false
                };

                try {
                    const itemContainer = await buildFoundItemContainer(itemData, catchData, context, leaderboardResults);
                    components.push(itemContainer);
                } catch (itemError) {
                    console.error('[globalLevelNotifications] Error building item container:', itemError);
                }
            }
        }

        // Send DM with Components v2
        await user.send({
            flags: MessageFlags.IsComponentsV2,
            components: components,
            allowedMentions: { parse: [] }
        });

        globalLevelNotificationsMetrics.dmsSent++;

        // Performance tracking
        const duration = Date.now() - startTime;
        globalLevelNotificationsMetrics.averageQueryTime =
            (globalLevelNotificationsMetrics.averageQueryTime * (globalLevelNotificationsMetrics.databaseQueries - 1) + duration) /
            globalLevelNotificationsMetrics.databaseQueries;

        if (globalLevelNotificationsMetrics.verboseLogging || duration > 200) {
            console.log(`[globalLevelNotifications] ✅ Sent global level DM to ${userId} with ${components.length} components in ${duration}ms`);
        }

        return true;

    } catch (error) {
        console.error('[globalLevelNotifications] ❌ Error sending global level DM:', error);
        return false;
    }
}

/**
 * Process global level-up (add to notification queue, send DM, and handle item drop notifications)
 * @param {string} userId - User ID
 * @param {Object} levelUpData - Level up information
 * @param {Object} client - Discord client
 * @param {string} contextGuildId - Guild ID where the level-up occurred (for server name in DMs)
 */
async function processGlobalLevelUp(userId, levelUpData, client, contextGuildId = null) {
    try {
        // Add to notification queue
        await addGlobalLevelNotification(userId, levelUpData);

        // Send DM notification (async, don't wait)
        sendGlobalLevelDM(userId, levelUpData, client).catch(error => {
            console.error('[globalLevelNotifications] Error sending global level DM:', error);
        });

        // Process item drop notifications if items were awarded (HYBRID SYSTEM)
        if (levelUpData.levelRewards && levelUpData.levelRewards.items && levelUpData.levelRewards.items.length > 0) {
            try {
                const { processItemNotifications } = require('./itemDropsHybrid.js');

                // Process each item for notifications using hybrid system
                for (const awardedItem of levelUpData.levelRewards.items) {
                    console.log(`[globalLevelNotifications] 🎁 Processing GLOBAL level-up item: ${awardedItem.itemName} for user ${userId} in guild ${contextGuildId}`);

                    // FIXED: Pass contextGuildId so DMs show correct server name
                    await processItemNotifications(userId, contextGuildId, awardedItem, 'LEVEL_UP', client);
                }
            } catch (itemNotificationError) {
                console.error('[globalLevelNotifications] Error processing item drop notifications:', itemNotificationError);
            }
        }

        console.log(`[globalLevelNotifications] ✅ Processed global level-up for ${userId}`);

    } catch (error) {
        console.error('[globalLevelNotifications] Error processing global level-up:', error);
    }
}

/**
 * Cache invalidation functions (Enterprise-Grade Cache Management)
 * OPTIMIZED: Intelligent cache invalidation for data consistency
 */
function invalidateUserNotificationsCache(userId) {
    // FIXED: Use getKeysByAccessTime() instead of keys() for LRU cache compatibility
    const allKeys = userNotificationsCache.getKeysByAccessTime();
    const keys = allKeys.filter(key => key.startsWith(`notifications_${userId}_`));
    keys.forEach(key => userNotificationsCache.delete(key));

    if (globalLevelNotificationsMetrics.verboseLogging && keys.length > 0) {
        console.log(`[globalLevelNotifications] 🗑️ Invalidated ${keys.length} notification cache entries for ${userId}`);
    }
}

function invalidateUserSettingsCache(userId) {
    const cacheKey = `user_settings_${userId}`;
    userSettingsCache.delete(cacheKey);

    if (globalLevelNotificationsMetrics.verboseLogging) {
        console.log(`[globalLevelNotifications] 🗑️ Invalidated user settings cache for ${userId}`);
    }
}

function invalidateNotificationDisplayCache(userId) {
    const cacheKey = `display_${userId}`;
    notificationDisplayCache.delete(cacheKey);

    if (globalLevelNotificationsMetrics.verboseLogging) {
        console.log(`[globalLevelNotifications] 🗑️ Invalidated notification display cache for ${userId}`);
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getGlobalLevelNotificationsStats() {
    const cacheHitRate = globalLevelNotificationsMetrics.cacheHits + globalLevelNotificationsMetrics.cacheMisses > 0 ?
        (globalLevelNotificationsMetrics.cacheHits / (globalLevelNotificationsMetrics.cacheHits + globalLevelNotificationsMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: globalLevelNotificationsMetrics.cacheHits,
            cacheMisses: globalLevelNotificationsMetrics.cacheMisses,
            databaseQueries: globalLevelNotificationsMetrics.databaseQueries,
            averageQueryTime: `${globalLevelNotificationsMetrics.averageQueryTime.toFixed(2)}ms`,
            notificationsProcessed: globalLevelNotificationsMetrics.notificationsProcessed,
            dmsSent: globalLevelNotificationsMetrics.dmsSent,
            notificationsDismissed: globalLevelNotificationsMetrics.notificationsDismissed,
            parallelOperations: globalLevelNotificationsMetrics.parallelOperations,
            partialFailures: globalLevelNotificationsMetrics.partialFailures,
            lastOptimization: new Date(globalLevelNotificationsMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            userNotifications: userNotificationsCache.getStats(),
            userSettings: userSettingsCache.getStats(),
            notificationDisplay: notificationDisplayCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            userNotifications: userNotificationsCache.getStats().memoryUsage,
            userSettings: userSettingsCache.getStats().memoryUsage,
            notificationDisplay: notificationDisplayCache.getStats().memoryUsage,
            total: userNotificationsCache.getStats().memoryUsage +
                   userSettingsCache.getStats().memoryUsage +
                   notificationDisplayCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            parallelEfficiency: globalLevelNotificationsMetrics.parallelOperations > 0 ?
                ((globalLevelNotificationsMetrics.parallelOperations - globalLevelNotificationsMetrics.partialFailures) / globalLevelNotificationsMetrics.parallelOperations * 100).toFixed(2) + '%' : 'N/A'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    globalLevelNotificationsMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getGlobalLevelNotificationsStats();
    console.log(`[globalLevelNotifications] 📊 Performance Report:`);
    console.log(`[globalLevelNotifications]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[globalLevelNotifications]   Database Queries: ${stats.performance.databaseQueries}`);
    console.log(`[globalLevelNotifications]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[globalLevelNotifications]   Notifications Processed: ${stats.performance.notificationsProcessed}`);
    console.log(`[globalLevelNotifications]   DMs Sent: ${stats.performance.dmsSent}`);
    console.log(`[globalLevelNotifications]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[globalLevelNotifications]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[globalLevelNotifications]   System Health: ${stats.systemHealth.status}`);

    return stats;
}

/**
 * Clear all global level notifications caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllGlobalLevelNotificationsCaches() {
    userNotificationsCache.clear();
    userSettingsCache.clear();
    notificationDisplayCache.clear();

    console.log('[globalLevelNotifications] 🗑️ Cleared all global level notifications caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, globalLevelNotificationsMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    addGlobalLevelNotification,
    getGlobalLevelNotifications,
    dismissGlobalLevelNotification,
    buildGlobalLevelNotificationDisplay,
    sendGlobalLevelDM,
    processGlobalLevelUp,

    // Enhanced optimization functions
    getCachedUserSettings,
    getGlobalLevelNotificationsStats,
    performanceCleanupAndOptimization,
    clearAllGlobalLevelNotificationsCaches,
    invalidateUserNotificationsCache,
    invalidateUserSettingsCache,
    invalidateNotificationDisplayCache,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...globalLevelNotificationsMetrics })
};
