# ⭐ Specialized Systems - Comprehensive Documentation

## Overview
The specialized systems provide advanced functionality including daily rewards (Starfall), global leveling, voice transcription, and whisper AI model management. These systems extend the bot's capabilities beyond basic server management.

## Core Architecture

### Main Files
- `utils/starfall.js` - Daily rewards system with timezone handling
- `utils/globalLevels.js` - Cross-server EXP tracking and leaderboards
- `utils/transcriptionHandlers.js` - Voice message processing logic
- `commands/utility/whisperModels.js` - AI model management
- `commands/utility/transcribe-voice.js` - Voice transcription context menu

## Starfall Daily Rewards System

### Overview
Sophisticated daily reward system with timezone handling, streak tracking, and real-time updates that provides consistent user engagement incentives.

### Key Functions

#### Core Reward Logic
```javascript
async function calculateStarfallReward(userId) {
    const userData = await getStarfallData(userId);
    const now = new Date();
    const userTimezone = userData.timezone || 'UTC';
    
    // Calculate user's local midnight
    const userMidnight = getNextMidnight(now, userTimezone);
    const timeSinceLastClaim = now - userData.lastClaim;
    
    if (timeSinceLastClaim >= 24 * 60 * 60 * 1000) {
        return calculateRewardAmount(userData.streak);
    }
    
    return null; // No reward available
}
```

#### Streak Management
```javascript
function updateStreak(userData, claimTime) {
    const lastClaim = new Date(userData.lastClaim);
    const timeDiff = claimTime - lastClaim;
    const daysDiff = Math.floor(timeDiff / (24 * 60 * 60 * 1000));
    
    if (daysDiff === 1) {
        // Consecutive day - increment streak
        userData.streak += 1;
    } else if (daysDiff > 1) {
        // Missed days - reset streak
        userData.streak = 1;
    }
    // Same day claims don't affect streak
    
    return userData;
}
```

### Special Behaviors
- **Timezone Awareness**: Calculates daily resets based on user's timezone
- **Streak Bonuses**: Increasing rewards for consecutive daily claims
- **Real-Time Updates**: Dynamic countdown timers for next available claim
- **Grace Period**: Small buffer for timezone edge cases

### Internal Logic Details

#### Timezone Handling
```javascript
function getNextMidnight(date, timezone) {
    const userDate = new Date(date.toLocaleString("en-US", {timeZone: timezone}));
    const nextMidnight = new Date(userDate);
    nextMidnight.setHours(24, 0, 0, 0);
    
    // Convert back to UTC for storage
    const utcOffset = date.getTimezoneOffset() * 60000;
    return new Date(nextMidnight.getTime() + utcOffset);
}
```

#### Reward Calculation
```javascript
function calculateRewardAmount(streak) {
    const baseReward = 50;
    const streakBonus = Math.min(streak * 5, 100); // Cap at 100 bonus
    const randomBonus = Math.floor(Math.random() * 20); // 0-19 random
    
    return baseReward + streakBonus + randomBonus;
}
```

### Testing Requirements
- Timezone calculation accuracy
- Streak logic validation
- Reward amount consistency
- Real-time update functionality

## Global Levels System

### Overview
Cross-server EXP tracking system that maintains user progression across all servers where the bot is present, providing unified leveling experience.

### Key Functions

#### Global EXP Accumulation
```javascript
async function addGlobalExp(userId, expAmount, source) {
    const globalData = await globalLevelsCol.findOne({ userId });
    
    if (!globalData) {
        await globalLevelsCol.insertOne({
            userId,
            totalExp: expAmount,
            level: calculateGlobalLevel(expAmount),
            sources: { [source]: expAmount },
            lastUpdated: new Date(),
            rank: null // Will be calculated in batch
        });
    } else {
        const newTotalExp = globalData.totalExp + expAmount;
        const newLevel = calculateGlobalLevel(newTotalExp);
        
        await globalLevelsCol.updateOne(
            { userId },
            {
                $inc: { 
                    totalExp: expAmount,
                    [`sources.${source}`]: expAmount
                },
                $set: { 
                    level: newLevel,
                    lastUpdated: new Date()
                }
            }
        );
        
        // Check for level up
        if (newLevel > globalData.level) {
            await handleGlobalLevelUp(userId, newLevel);
        }
    }
}
```

#### Leaderboard Generation
```javascript
async function generateGlobalLeaderboard(limit = 100) {
    const topUsers = await globalLevelsCol
        .find({})
        .sort({ totalExp: -1 })
        .limit(limit)
        .toArray();
    
    // Update ranks
    for (let i = 0; i < topUsers.length; i++) {
        await globalLevelsCol.updateOne(
            { userId: topUsers[i].userId },
            { $set: { rank: i + 1 } }
        );
    }
    
    return topUsers;
}
```

### Special Behaviors
- **Cross-Server Synchronization**: EXP from all servers contributes to global total
- **Rank Calculation**: Periodic rank updates based on global EXP totals
- **Source Tracking**: Maintains breakdown of EXP sources (text, voice, etc.)
- **Achievement System**: Unlocks achievements based on global milestones

### Internal Logic Details

#### Level Calculation
```javascript
function calculateGlobalLevel(totalExp) {
    // Progressive EXP requirements: 100, 250, 450, 700, 1000, etc.
    let level = 0;
    let expRequired = 100;
    let currentExp = totalExp;
    
    while (currentExp >= expRequired) {
        currentExp -= expRequired;
        level++;
        expRequired = 100 + (level * 50); // Increasing requirements
    }
    
    return level;
}
```

#### Achievement Triggers
```javascript
async function checkGlobalAchievements(userId, newLevel, totalExp) {
    const achievements = [];
    
    // Level-based achievements
    if (newLevel >= 10) achievements.push('GLOBAL_LEVEL_10');
    if (newLevel >= 50) achievements.push('GLOBAL_LEVEL_50');
    if (newLevel >= 100) achievements.push('GLOBAL_LEVEL_100');
    
    // EXP-based achievements
    if (totalExp >= 10000) achievements.push('EXP_MASTER');
    if (totalExp >= 100000) achievements.push('EXP_LEGEND');
    
    // Award new achievements
    for (const achievement of achievements) {
        await awardAchievement(userId, achievement);
    }
}
```

### Testing Requirements
- EXP accumulation accuracy across servers
- Rank calculation correctness
- Achievement trigger validation
- Leaderboard generation performance

## Voice Transcription System

### Overview
Advanced voice message transcription using Python integration and OpenAI Whisper models, providing accessibility and convenience for voice communications.

### Key Functions

#### Audio Processing Pipeline
```javascript
async function processVoiceMessage(audioUrl, options = {}) {
    try {
        // Download audio file
        const audioBuffer = await downloadAudio(audioUrl);
        
        // Convert to supported format if needed
        const processedAudio = await convertAudioFormat(audioBuffer);
        
        // Send to Python transcription service
        const transcription = await callPythonTranscriber(processedAudio, options);
        
        // Post-process transcription
        const cleanedText = cleanTranscriptionText(transcription);
        
        return {
            success: true,
            text: cleanedText,
            confidence: transcription.confidence,
            language: transcription.language,
            duration: transcription.duration
        };
    } catch (error) {
        console.error('[transcription] Processing failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}
```

#### Python Service Integration
```javascript
async function callPythonTranscriber(audioData, options) {
    const pythonPath = process.env.PYTHON_PATH || 'python';
    const scriptPath = path.join(__dirname, '../transcriber.py');
    
    return new Promise((resolve, reject) => {
        const python = spawn(pythonPath, [scriptPath], {
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        // Send audio data to Python process
        python.stdin.write(audioData);
        python.stdin.end();
        
        let output = '';
        let error = '';
        
        python.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        python.stderr.on('data', (data) => {
            error += data.toString();
        });
        
        python.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output);
                    resolve(result);
                } catch (parseError) {
                    reject(new Error('Failed to parse transcription result'));
                }
            } else {
                reject(new Error(`Transcription failed: ${error}`));
            }
        });
        
        // Timeout after 30 seconds
        setTimeout(() => {
            python.kill();
            reject(new Error('Transcription timeout'));
        }, 30000);
    });
}
```

### Special Behaviors
- **Format Support**: Handles various audio formats (MP3, WAV, OGG, etc.)
- **Language Detection**: Automatic language identification
- **Confidence Scoring**: Provides transcription confidence levels
- **Error Recovery**: Graceful handling of service unavailability

### Internal Logic Details

#### Audio Format Conversion
```javascript
async function convertAudioFormat(audioBuffer) {
    // Check if conversion is needed
    const format = detectAudioFormat(audioBuffer);
    
    if (SUPPORTED_FORMATS.includes(format)) {
        return audioBuffer;
    }
    
    // Convert using ffmpeg or similar
    return await convertToWAV(audioBuffer);
}
```

#### Transcription Caching
```javascript
async function getCachedTranscription(audioHash) {
    const cached = await transcriptionCache.get(audioHash);
    
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
        return cached.transcription;
    }
    
    return null;
}
```

### Testing Requirements
- Audio file processing accuracy
- Python service integration
- Error handling for service failures
- Performance with various audio formats

## Whisper Models Management

### Overview
Administrative system for downloading, managing, and configuring OpenAI Whisper models for voice transcription.

### Key Functions

#### Model Management
```javascript
async function downloadWhisperModel(modelName) {
    const availableModels = ['tiny', 'base', 'small', 'medium', 'large'];
    
    if (!availableModels.includes(modelName)) {
        throw new Error('Invalid model name');
    }
    
    const downloadPath = path.join(__dirname, '../models', modelName);
    
    try {
        // Download model using Python whisper library
        const result = await execAsync(`python -c "import whisper; whisper.load_model('${modelName}')"`);
        
        return {
            success: true,
            model: modelName,
            path: downloadPath,
            size: await getModelSize(downloadPath)
        };
    } catch (error) {
        throw new Error(`Failed to download model ${modelName}: ${error.message}`);
    }
}
```

#### Model Configuration
```javascript
const MODEL_CONFIGS = {
    tiny: { size: '39 MB', speed: 'fastest', accuracy: 'lowest' },
    base: { size: '74 MB', speed: 'fast', accuracy: 'low' },
    small: { size: '244 MB', speed: 'medium', accuracy: 'medium' },
    medium: { size: '769 MB', speed: 'slow', accuracy: 'high' },
    large: { size: '1550 MB', speed: 'slowest', accuracy: 'highest' }
};
```

### Special Behaviors
- **Model Selection**: Choose appropriate model based on accuracy/speed requirements
- **Storage Management**: Monitor disk space usage for models
- **Update Checking**: Periodic checks for model updates
- **Performance Monitoring**: Track transcription quality by model

### Testing Requirements
- Model download functionality
- Configuration interface testing
- Storage management validation
- Performance comparison between models

## Testing Guidelines

### Specialized System Testing
```javascript
class SpecializedSystemTester extends BotTestBase {
    async testStarfallRewards() {
        // Test daily reward calculation
        const reward = await calculateStarfallReward(this.testUser.id);
        
        // Validate timezone handling
        const userTimezone = 'America/New_York';
        const nextClaim = calculateNextClaimTime(userTimezone);
        
        assert(nextClaim instanceof Date);
    }
    
    async testGlobalLevels() {
        // Test EXP accumulation
        await addGlobalExp(this.testUser.id, 100, 'TEXT');
        
        const userData = await getGlobalLevelData(this.testUser.id);
        assert(userData.totalExp >= 100);
    }
    
    async testTranscription() {
        // Test transcription service (if available)
        if (await isTranscriptionServiceAvailable()) {
            const result = await processVoiceMessage(testAudioUrl);
            assert(result.success === true || result.error);
        }
    }
}
```

### Performance Considerations
- Starfall calculations should be cached for frequent access
- Global level updates should be batched for efficiency
- Transcription should have reasonable timeouts
- Model downloads should not block other operations

## Maintenance Requirements

### Regular Tasks
1. **Starfall Data Cleanup**: Remove old claim data for inactive users
2. **Global Rank Updates**: Periodic recalculation of user ranks
3. **Transcription Cache Management**: Clean old cached transcriptions
4. **Model Updates**: Check for and apply Whisper model updates

### Scaling Considerations
- Starfall calculations for large user bases
- Global leaderboard performance with many users
- Transcription service load balancing
- Model storage optimization

## Development Guidelines

### Code Patterns
- Use timezone-aware date calculations for Starfall
- Implement proper error handling for external services
- Cache frequently accessed data appropriately
- Follow consistent patterns for specialized system interfaces

### Testing Standards
- Test timezone edge cases for Starfall
- Validate global data consistency across servers
- Include transcription service availability checks
- Test model management operations safely

### Documentation Updates
- Document timezone handling changes
- Maintain transcription service integration notes
- Update model configuration documentation
- Keep performance benchmarks current
