/**
 * Comprehensive diagnostic script to identify root cause of getLiveDiscoveryTotal failures
 * Tests database connectivity, query structure, data consistency, and cache functionality
 */

require('dotenv').config();

async function testDiscoveryTotalRootCause() {
    console.log('🔧 Comprehensive root cause analysis for getLiveDiscoveryTotal failures...');
    
    try {
        console.log('\n=== Test 1: Module and Database Connectivity ===');
        
        // Test module loading
        const itemsModule = require('../commands/utility/items.js');
        console.log('✅ Items module loaded successfully');
        
        // Test database optimizer
        const { optimizedCountDocuments, optimizedFind } = require('../utils/database-optimizer.js');
        console.log('✅ Database optimizer loaded successfully');
        
        // Test MongoDB connection
        const mongoClient = require('../mongo/client.js').mongoClient;
        if (mongoClient && mongoClient.topology && mongoClient.topology.isConnected()) {
            console.log('✅ MongoDB connection is active');
        } else {
            console.log('❌ MongoDB connection issue detected');
            return false;
        }
        
        console.log('\n=== Test 2: Database Schema Verification ===');
        
        // Check if item_records collection exists and has data
        const itemRecordsCount = await optimizedCountDocuments("item_records", {});
        console.log(`✅ item_records collection contains ${itemRecordsCount} total records`);
        
        if (itemRecordsCount === 0) {
            console.log('⚠️  No records in item_records collection - this could be the root cause');
        }
        
        // Check for discovery records specifically
        const discoveryRecordsCount = await optimizedCountDocuments("item_records", {
            parameter: '_item_discovery'
        });
        console.log(`✅ Found ${discoveryRecordsCount} discovery records with parameter '_item_discovery'`);
        
        if (discoveryRecordsCount === 0) {
            console.log('❌ ROOT CAUSE IDENTIFIED: No discovery records found in database');
            console.log('   This explains why getLiveDiscoveryTotal returns 0');
        }
        
        console.log('\n=== Test 3: Sample Data Structure Analysis ===');
        
        // Get sample records to verify structure
        const sampleRecords = await optimizedFind("item_records", 
            { parameter: '_item_discovery' }, 
            { limit: 3, sort: { recordedAt: -1 } }
        );
        
        if (sampleRecords.length > 0) {
            console.log('✅ Sample discovery records found:');
            sampleRecords.forEach((record, index) => {
                console.log(`   Record ${index + 1}:`, {
                    scope: record.scope,
                    guildId: record.guildId,
                    itemName: record.itemName,
                    itemType: record.itemType,
                    parameter: record.parameter,
                    userId: record.userId,
                    recordedAt: record.recordedAt
                });
            });
        } else {
            console.log('❌ No sample discovery records found');
        }
        
        console.log('\n=== Test 4: Query Structure Validation ===');
        
        if (sampleRecords.length > 0) {
            const sampleRecord = sampleRecords[0];
            
            // Test the exact query structure used by getLiveDiscoveryTotal
            const testQueries = [
                {
                    name: 'Guild-specific query',
                    query: {
                        scope: 'guild',
                        itemName: sampleRecord.itemName,
                        itemType: sampleRecord.itemType,
                        parameter: '_item_discovery',
                        guildId: sampleRecord.guildId
                    }
                },
                {
                    name: 'Global query',
                    query: {
                        scope: 'global',
                        itemName: sampleRecord.itemName,
                        itemType: sampleRecord.itemType,
                        parameter: '_item_discovery'
                    }
                }
            ];
            
            for (const testQuery of testQueries) {
                try {
                    const count = await optimizedCountDocuments("item_records", testQuery.query);
                    console.log(`✅ ${testQuery.name}: ${count} records found`);
                    console.log(`   Query:`, testQuery.query);
                } catch (error) {
                    console.log(`❌ ${testQuery.name} failed:`, error.message);
                }
            }
        }
        
        console.log('\n=== Test 5: Cache System Verification ===');
        
        // Test LRU cache functionality
        const { CacheFactory } = require('../utils/LRUCache.js');
        const testCache = CacheFactory.createCache('HighFreq');
        
        // Test cache operations
        testCache.set('test_key', { count: 5, timestamp: Date.now() });
        const cached = testCache.get('test_key');
        
        if (cached && cached.count === 5) {
            console.log('✅ LRU cache system working correctly');
        } else {
            console.log('❌ LRU cache system issue detected');
        }
        
        console.log('\n=== Test 6: Live Function Testing ===');
        
        if (sampleRecords.length > 0) {
            const sampleRecord = sampleRecords[0];
            
            try {
                // Test the actual getLiveDiscoveryTotal function
                console.log('🔍 Testing getLiveDiscoveryTotal function directly...');
                
                // We need to access the function - it's not exported, so we'll test the logic
                const testScope = sampleRecord.guildId ? 'guild' : 'global';
                const testQuery = {
                    scope: testScope,
                    itemName: sampleRecord.itemName,
                    itemType: sampleRecord.itemType,
                    parameter: '_item_discovery'
                };
                
                if (sampleRecord.guildId) {
                    testQuery.guildId = sampleRecord.guildId;
                }
                
                const directCount = await optimizedCountDocuments("item_records", testQuery);
                console.log(`✅ Direct query result: ${directCount} records`);
                
                if (directCount === 0) {
                    console.log('❌ ROOT CAUSE CONFIRMED: Query returns 0 records');
                    console.log('   This indicates either:');
                    console.log('   1. No discovery records exist for this item');
                    console.log('   2. Query parameters don\'t match stored data');
                    console.log('   3. Data structure mismatch');
                }
                
            } catch (error) {
                console.log('❌ Function testing failed:', error.message);
            }
        }
        
        console.log('\n=== Test 7: Data Consistency Check ===');
        
        // Check for common data issues
        const inconsistencyChecks = [
            {
                name: 'Records with null itemName',
                query: { parameter: '_item_discovery', itemName: null }
            },
            {
                name: 'Records with null itemType', 
                query: { parameter: '_item_discovery', itemType: null }
            },
            {
                name: 'Records with wrong parameter',
                query: { parameter: { $ne: '_item_discovery' } }
            },
            {
                name: 'Records with invalid scope',
                query: { scope: { $nin: ['guild', 'global'] } }
            }
        ];
        
        for (const check of inconsistencyChecks) {
            try {
                const count = await optimizedCountDocuments("item_records", check.query);
                if (count > 0) {
                    console.log(`⚠️  ${check.name}: ${count} records found`);
                } else {
                    console.log(`✅ ${check.name}: No issues found`);
                }
            } catch (error) {
                console.log(`❌ ${check.name} check failed:`, error.message);
            }
        }
        
        console.log('\n🎉 Root cause analysis completed!');
        
        return true;
        
    } catch (error) {
        console.error('❌ Error during root cause analysis:', error);
        return false;
    }
}

// Test specific error scenarios
async function testErrorScenarios() {
    console.log('\n🔧 Testing specific error scenarios...');
    
    try {
        console.log('\n=== Scenario 1: Invalid Parameters ===');
        
        // Test with invalid parameters that might cause errors
        const { optimizedCountDocuments } = require('../utils/database-optimizer.js');
        
        const invalidTests = [
            { name: 'Null itemName', itemName: null, itemType: 'test' },
            { name: 'Undefined itemName', itemName: undefined, itemType: 'test' },
            { name: 'Empty itemName', itemName: '', itemType: 'test' },
            { name: 'Null itemType', itemName: 'test', itemType: null },
            { name: 'Special characters', itemName: 'test$item', itemType: 'test@type' }
        ];
        
        for (const test of invalidTests) {
            try {
                const query = {
                    scope: 'guild',
                    itemName: test.itemName,
                    itemType: test.itemType,
                    parameter: '_item_discovery',
                    guildId: 'test_guild'
                };
                
                const count = await optimizedCountDocuments("item_records", query);
                console.log(`✅ ${test.name}: Query succeeded, returned ${count}`);
            } catch (error) {
                console.log(`❌ ${test.name}: Query failed - ${error.message}`);
            }
        }
        
        console.log('\n=== Scenario 2: Database Connection Issues ===');
        
        // Test database connection stability
        try {
            const mongoClient = require('../mongo/client.js').mongoClient;
            const db = mongoClient.db("seventeen_bot");
            const collections = await db.listCollections().toArray();
            console.log(`✅ Database connection stable, ${collections.length} collections found`);
        } catch (error) {
            console.log(`❌ Database connection issue: ${error.message}`);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error testing scenarios:', error);
        return false;
    }
}

// Run if called directly
if (require.main === module) {
    Promise.all([
        testDiscoveryTotalRootCause(),
        testErrorScenarios()
    ]).then(([rootCauseSuccess, scenariosSuccess]) => {
        if (rootCauseSuccess && scenariosSuccess) {
            console.log('\n🏁 Root cause analysis completed successfully');
            console.log('🎯 Check the output above for identified issues');
            process.exit(0);
        } else {
            console.log('\n💥 Analysis failed - check error messages above');
            process.exit(1);
        }
    }).catch(error => {
        console.error('💥 Fatal error during analysis:', error);
        process.exit(1);
    });
}

module.exports = { testDiscoveryTotalRootCause, testErrorScenarios };
