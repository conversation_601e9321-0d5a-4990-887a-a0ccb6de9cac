const { optimizedFind, optimizedFindOne, optimizedInsertOne, optimizedUpdateOne, optimizedBulkWrite } = require('./database-optimizer.js');
const { ObjectId } = require('mongodb');
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * HYBRID ITEM DROP SYSTEM
 * Combines enterprise-grade performance optimizations with bulletproof separation logic
 * 
 * CORE PRINCIPLES:
 * 1. GLOBAL ITEMS (guildId: null) → DMs + Notification Center ONLY (never guild channels)
 * 2. GUILD ITEMS (guildId: specific) → Guild channels + DMs + Notification Center
 * 3. Full performance optimizations: LRU caching, parallel processing, metrics
 * 4. Clear separation logic prevents cross-contamination
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring (ENHANCED with guild context tracking)
const itemDropsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    itemsProcessed: 0,
    dropsSuccessful: 0,
    parallelOperations: 0,
    globalItemsProcessed: 0,
    guildItemsProcessed: 0,
    crossContaminationPrevented: 0,

    // ENHANCED: Guild context tracking metrics
    guildContextProperlyPassed: 0,
    guildContextMissing: 0,
    serverNameResolutionSuccess: 0,
    serverNameResolutionFailure: 0,

    // ENHANCED: Drop location distribution
    dropLocationStats: {
        TEXT: 0,
        VOICE: 0,
        STARFALL: 0,
        LEVEL_UP: 0
    },

    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// Multi-tier LRU caching system (KEPT from regular system)
const guildDropConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes
const itemNotificationConfigCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes
const userDropSettingsCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes
const droppableItemsCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes

// Register caches for global management
registerCache('guildDropConfig', guildDropConfigCache);
registerCache('itemNotificationConfig', itemNotificationConfigCache);
registerCache('userDropSettings', userDropSettingsCache);
registerCache('droppableItems', droppableItemsCache);

/**
 * Get cached guild drop configuration (OPTIMIZED from regular system)
 * @param {string} guildId - Guild ID
 * @returns {Object} Guild configuration
 */
async function getCachedGuildDropConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = guildId;

    // Check cache first
    const cached = guildDropConfigCache.get(cacheKey);
    if (cached) {
        itemDropsMetrics.cacheHits++;
        if (itemDropsMetrics.verboseLogging) {
            console.log(`[itemDropsHybrid] ⚡ Guild config cache hit for ${guildId} (0ms)`);
        }
        return cached;
    }

    try {
        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const config = guildData || {};

        // Cache using LRU cache (automatic TTL and size management)
        guildDropConfigCache.set(cacheKey, config);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        itemDropsMetrics.averageQueryTime =
            (itemDropsMetrics.averageQueryTime * (itemDropsMetrics.databaseQueries - 1) + duration) /
            itemDropsMetrics.databaseQueries;

        if (itemDropsMetrics.verboseLogging || duration > 50) {
            console.log(`[itemDropsHybrid] ✅ Guild config fetched for ${guildId} (${duration}ms) - cached for future access`);
        }

        return config;

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error fetching guild config:', error);
        return {};
    }
}

/**
 * Get cached item notification configuration (OPTIMIZED from regular system)
 * @param {string} key - Configuration key ('global' or guildId)
 * @returns {Object} Notification configuration
 */
async function getCachedItemNotificationConfig(key) {
    const startTime = Date.now();
    const cacheKey = `notification_config_${key}`;

    // Check cache first
    const cached = itemNotificationConfigCache.get(cacheKey);
    if (cached) {
        itemDropsMetrics.cacheHits++;
        if (itemDropsMetrics.verboseLogging) {
            console.log(`[itemDropsHybrid] ⚡ Notification config cache hit for ${key} (0ms)`);
        }
        return cached;
    }

    try {
        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        const config = await optimizedFindOne("item_notifications", { key: key });
        const result = config || { enabled: true, dmMessage: 'You found {items} in {server}, dropped from {location}:' };

        // Cache the result
        itemNotificationConfigCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        if (itemDropsMetrics.verboseLogging || duration > 50) {
            console.log(`[itemDropsHybrid] ✅ Notification config fetched for ${key} (${duration}ms) - cached for future access`);
        }

        return result;

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error fetching notification config:', error);
        return { enabled: true, dmMessage: 'You found {items} in {server}, dropped from {location}:' };
    }
}

/**
 * Get cached user drop settings (OPTIMIZED from regular system)
 * @param {string} userId - User ID
 * @returns {Object} User drop settings
 */
async function getCachedUserDropSettings(userId) {
    const startTime = Date.now();
    const cacheKey = userId;

    // Check cache first
    const cached = userDropSettingsCache.get(cacheKey);
    if (cached) {
        itemDropsMetrics.cacheHits++;
        if (itemDropsMetrics.verboseLogging) {
            console.log(`[itemDropsHybrid] ⚡ User settings cache hit for ${userId} (0ms)`);
        }
        return cached;
    }

    try {
        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        const userData = await optimizedFindOne("users", { id: userId });
        const settings = userData?.dropSettings || { enabled: true, multiplier: 1.0 };

        // Cache the result
        userDropSettingsCache.set(cacheKey, settings);

        const duration = Date.now() - startTime;
        if (itemDropsMetrics.verboseLogging || duration > 50) {
            console.log(`[itemDropsHybrid] ✅ User settings fetched for ${userId} (${duration}ms) - cached for future access`);
        }

        return settings;

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error fetching user settings:', error);
        return { enabled: true, multiplier: 1.0 };
    }
}

/**
 * Get droppable items with caching (OPTIMIZED from regular system)
 * @param {string} guildId - Guild ID (null for global)
 * @param {string} location - Drop location
 * @returns {Array} Droppable items
 */
async function getDroppableItems(guildId, location) {
    const startTime = Date.now();
    const cacheKey = `${guildId || 'global'}_${location}`;

    // Check cache first
    const cached = droppableItemsCache.get(cacheKey);
    if (cached) {
        itemDropsMetrics.cacheHits++;
        if (itemDropsMetrics.verboseLogging) {
            console.log(`[itemDropsHybrid] ⚡ Droppable items cache hit for ${cacheKey} (0ms)`);
        }
        return cached;
    }

    try {
        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        // Build query for both global and guild-specific items (FIXED: Use correct field names)
        const query = {
            dropLocations: location,
            disabled: { $ne: true },
            $or: [
                { guildId: null }, // Global items
                { guildId: guildId } // Guild-specific items
            ]
        };

        // Use projection to only fetch needed fields for better performance
        const items = await optimizedFind("custom_items", query, {
            projection: {
                id: 1,
                name: 1,
                type: 1,
                rarity: 1,
                emote: 1,
                description: 1,
                parameters: 1,
                guildId: 1
            }
        });

        // Cache using LRU cache (automatic TTL and size management)
        droppableItemsCache.set(cacheKey, items);

        const duration = Date.now() - startTime;
        if (itemDropsMetrics.verboseLogging || duration > 100) {
            console.log(`[itemDropsHybrid] ✅ Droppable items fetched for ${cacheKey}: ${items.length} items (${duration}ms)`);
        }

        return items;

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error fetching droppable items:', error);
        return [];
    }
}

/**
 * Perform master roll with multiplier support (KEPT from regular system)
 * @param {Array} items - Available items
 * @param {number} multiplier - Drop chance multiplier
 * @returns {Object|null} Selected item or null
 */
function performMasterRoll(items, multiplier = 1.0) {
    if (!items || items.length === 0) return null;

    // Apply multiplier to drop chances
    const adjustedItems = items.map(item => ({
        ...item,
        adjustedWeight: (item.rarity?.weight || 100000) / multiplier
    }));

    // Calculate total weight
    const totalWeight = adjustedItems.reduce((sum, item) => sum + item.adjustedWeight, 0);

    // Generate random number
    const roll = Math.random() * totalWeight;

    // Find selected item
    let currentWeight = 0;
    for (const item of adjustedItems) {
        currentWeight += item.adjustedWeight;
        if (roll <= currentWeight) {
            return item;
        }
    }

    return null;
}

/**
 * Generate random parameters for item (KEPT from regular system)
 * @param {Object} itemData - Item data
 * @returns {Object} Generated parameters
 */
function generateRandomParameters(itemData) {
    const catchData = {};

    if (itemData.parameters) {
        for (const [key, param] of Object.entries(itemData.parameters)) {
            if (param.min !== undefined && param.max !== undefined) {
                const value = Math.random() * (param.max - param.min) + param.min;
                catchData[key] = parseFloat(value.toFixed(param.decimals || 1));
            }
        }
    }

    return catchData;
}

/**
 * HYBRID: Process item drops with performance optimizations + clear separation logic
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID where drop occurred
 * @param {string} location - Drop location
 * @param {number} expGained - EXP gained (for multiplier calculation)
 * @param {Object} client - Discord client
 * @returns {Array} Dropped items
 */
async function processItemDrops(userId, guildId, location, expGained = 0, client = null) {
    const startTime = Date.now();

    try {
        itemDropsMetrics.itemsProcessed++;



        // PERFORMANCE: Parallel fetch of all required data
        itemDropsMetrics.parallelOperations++;
        const [droppableItems, userSettings] = await Promise.allSettled([
            getDroppableItems(guildId, location),
            getCachedUserDropSettings(userId)
        ]);

        // Extract results from Promise.allSettled
        const items = droppableItems.status === 'fulfilled' ? droppableItems.value : [];
        const settings = userSettings.status === 'fulfilled' ? userSettings.value : { enabled: true, multiplier: 1.0 };

        if (items.length === 0) {
            return []; // No items configured for this location
        }

        if (!settings.enabled) {
            return []; // User has drops disabled
        }

        // PERFORMANCE: Calculate drop chance multiplier (level rewards, boosters, etc.)
        const multiplier = settings.multiplier || 1.0;

        // Perform single master roll with booster applied
        const selectedItem = performMasterRoll(items, multiplier);

        if (!selectedItem) {
            return []; // Nothing dropped
        }

        // Enhanced logging with performance context
        if (multiplier > 1.0 && itemDropsMetrics.verboseLogging) {
            console.log(`[itemDropsHybrid] Applied ${multiplier}x drop chance booster for ${userId}`);
        }

        // Add item to inventory
        const droppedItem = await addItemToInventory(userId, guildId, selectedItem, location);

        if (droppedItem) {
            itemDropsMetrics.dropsSuccessful++;

            if (itemDropsMetrics.verboseLogging) {
                console.log('[itemDropsHybrid] Added to inventory:', droppedItem);
            }

            // HYBRID: Process notifications with clear separation logic + parallel processing
            await processItemNotifications(userId, guildId, droppedItem, location, client);

            // PERFORMANCE: Process additional operations in parallel
            itemDropsMetrics.parallelOperations++;
            const parallelTasks = await Promise.allSettled([
                checkFirstItemDropInServer(guildId, selectedItem.id),
                // Log the item drop
                (async () => {
                    try {
                        const { sendItemDropLog } = require('./sendLog.js');
                        await sendItemDropLog(guildId, userId, droppedItem, location, expGained, false);
                    } catch (logError) {
                        console.error('[itemDropsHybrid] Error sending item drop log:', logError);
                    }
                })()
            ]);

            const duration = Date.now() - startTime;
            if (itemDropsMetrics.verboseLogging || duration > 200) {
                console.log(`[itemDropsHybrid] ✅ Item drop processed in ${duration}ms`);
            }

            return [droppedItem];
        }

        return [];

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error processing item drops:', error);
        return [];
    }
}

/**
 * HYBRID: Process item notifications with bulletproof separation logic + performance optimizations
 * @param {string} userId - User ID
 * @param {string} contextGuildId - Guild context where item was found
 * @param {Object} droppedItem - Dropped item
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 */
async function processItemNotifications(userId, contextGuildId, droppedItem, location, client) {
    try {
        // ENHANCED: Track drop location distribution
        if (itemDropsMetrics.dropLocationStats[location] !== undefined) {
            itemDropsMetrics.dropLocationStats[location]++;
        }

        // ENHANCED: Track guild context passing
        if (contextGuildId) {
            itemDropsMetrics.guildContextProperlyPassed++;
        } else {
            itemDropsMetrics.guildContextMissing++;
        }

        // BULLETPROOF LOGIC: Global items (guildId: null) = DMs + Notification Center ONLY
        if (droppedItem.guildId === null) {
            itemDropsMetrics.globalItemsProcessed++;
            console.log(`[itemDropsHybrid] 🌍 Processing GLOBAL item notification: ${droppedItem.itemName} found in guild ${contextGuildId}`);

            // OPTION 4: Queue delayed messages with accurate discovery ranks
            itemDropsMetrics.parallelOperations++;
            await Promise.allSettled([
                queueDelayedItemMessage(userId, contextGuildId, droppedItem, location, 'notification', client), // Notification center (delayed)
                queueDelayedItemMessage(userId, contextGuildId, droppedItem, location, 'dm', client) // DM (delayed)
            ]);

            // SAFETY CHECK: Prevent global items from going to guild channels
            itemDropsMetrics.crossContaminationPrevented++;
            if (itemDropsMetrics.verboseLogging) {
                console.log(`[itemDropsHybrid] 🛡️ SAFETY: Prevented global item from going to guild channel`);
            }
            return;
        }

        // BULLETPROOF LOGIC: Guild items (guildId: specific) = Guild channels + DMs + Notification Center
        if (droppedItem.guildId && contextGuildId === droppedItem.guildId) {
            itemDropsMetrics.guildItemsProcessed++;
            console.log(`[itemDropsHybrid] 🏰 Processing GUILD item notification: ${droppedItem.itemName} in guild ${contextGuildId}`);

            // OPTION 4: Queue delayed messages with accurate discovery ranks
            itemDropsMetrics.parallelOperations++;
            await Promise.allSettled([
                queueDelayedItemMessage(userId, contextGuildId, droppedItem, location, 'notification', client), // Notification center (delayed)
                queueDelayedItemMessage(userId, contextGuildId, droppedItem, location, 'dm', client), // DM (delayed)
                queueDelayedItemMessage(userId, contextGuildId, droppedItem, location, 'guild', client) // Guild channel (delayed)
            ]);
            return;
        }

        // SAFETY: This should never happen, but log it if it does
        console.warn(`[itemDropsHybrid] ⚠️ Unexpected item context: item.guildId=${droppedItem.guildId}, contextGuildId=${contextGuildId}`);

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error processing item notifications:', error);
    }
}

/**
 * Add item to inventory with leaderboard calculation (FIXED: Now includes discovery ranks)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID where item was found
 * @param {Object} itemData - Item data
 * @param {string} location - Drop location
 * @returns {Object} Inventory item with leaderboard results
 */
async function addItemToInventory(userId, guildId, itemData, location) {
    try {
        const inventoryItem = {
            userId: userId,
            guildId: itemData.guildId, // CRITICAL: Use item's guildId, not context guildId

            // CRITICAL: Context where item was found
            foundInGuild: guildId, // null for global level-ups, specific for guild drops

            itemId: itemData.id,
            itemName: itemData.name,
            itemType: itemData.type,
            itemRarity: itemData.rarity,
            itemEmote: itemData.emote,
            itemDescription: itemData.description,
            droppedAt: new Date(),
            droppedFrom: location,
            catchData: generateRandomParameters(itemData)
        };

        // FIXED: Sequential execution - leaderboard calculation AFTER inventory insertion
        const dbOperationsStart = Date.now();
        console.log(`[itemDropsHybrid] 💾 DATABASE OPERATIONS START for ${itemData.name}`);

        // Step 1: Insert inventory item first
        const insertStart = Date.now();
        console.log(`[itemDropsHybrid] 📝 Inserting inventory item...`);

        const insertResult = await optimizedInsertOne('user_inventory', inventoryItem);
        const insertDuration = Date.now() - insertStart;
        console.log(`[itemDropsHybrid] ✅ Inventory insert completed in ${insertDuration}ms`);

        if (insertDuration > 200) {
            console.warn(`[itemDropsHybrid] ⚠️  Slow inventory insert: ${insertDuration}ms`);
        }

        // Step 2: Update leaderboards AFTER inventory insertion is complete
        const leaderboardResults = await (async () => {
            const leaderboardStart = Date.now();
            console.log(`[itemDropsHybrid] 🏆 Updating leaderboards...`);

            try {
                const { updateItemLeaderboards } = require('./itemRecords.js');
                const result = await updateItemLeaderboards(inventoryItem);
                const leaderboardDuration = Date.now() - leaderboardStart;
                console.log(`[itemDropsHybrid] ✅ Leaderboard update completed in ${leaderboardDuration}ms`);

                if (leaderboardDuration > 500) {
                    console.warn(`[itemDropsHybrid] ⚠️  Slow leaderboard update: ${leaderboardDuration}ms`);
                }

                return result;
            } catch (error) {
                console.error(`[itemDropsHybrid] ❌ Leaderboard update failed after ${Date.now() - leaderboardStart}ms:`, error);
                return { guildRecords: [], globalRecords: [], guildRanks: {}, globalRanks: {} };
            }
        })();

        const totalDbDuration = Date.now() - dbOperationsStart;
        console.log(`[itemDropsHybrid] 💾 DATABASE OPERATIONS COMPLETE: ${totalDbDuration}ms total`);

        if (totalDbDuration > 800) {
            console.error(`[itemDropsHybrid] 🚨 PERFORMANCE ISSUE: Database operations took ${totalDbDuration}ms`);
        }

        // FIXED: Store leaderboard results on the inventory item for UI display
        inventoryItem.leaderboardResults = leaderboardResults;

        // FIXED: Invalidate both guild-specific AND global inventory caches after item drop
        try {
            const { invalidateInventoryCache } = require('./itemCache.js');

            // Invalidate guild-specific cache
            invalidateInventoryCache(inventoryItem.userId, inventoryItem.guildId);
            console.log(`[itemDropsHybrid] 🔄 Invalidated guild inventory cache for ${inventoryItem.userId} in ${inventoryItem.guildId}`);

            // CRITICAL FIX: Also invalidate global inventory cache (used for inventory displays)
            invalidateInventoryCache(inventoryItem.userId, null); // null = global cache
            console.log(`[itemDropsHybrid] 🔄 Invalidated global inventory cache for ${inventoryItem.userId}`);

        } catch (cacheError) {
            console.error('[itemDropsHybrid] Error invalidating inventory cache:', cacheError);
        }

        // Log any records achieved
        if (leaderboardResults.guildRecords && leaderboardResults.guildRecords.length > 0) {
            console.log(`[itemDropsHybrid] 🏆 Guild records achieved:`, leaderboardResults.guildRecords);
        }
        if (leaderboardResults.globalRecords && leaderboardResults.globalRecords.length > 0) {
            console.log(`[itemDropsHybrid] 🌟 Global records achieved:`, leaderboardResults.globalRecords);
        }

        // Enhanced logging with context clarity
        const itemContext = itemData.guildId ? 'GUILD' : 'GLOBAL';
        const foundContext = guildId ? `guild ${guildId}` : 'global context';
        console.log(`[itemDropsHybrid] ✅ Added ${itemContext} item to inventory: ${itemData.name} (${location}) found in ${foundContext}`);

        return inventoryItem;
    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error adding item to inventory:', error);
        return null;
    }
}

/**
 * Queue delayed item message with accurate discovery ranks (OPTION 4)
 * @param {string} userId - User ID
 * @param {string} contextGuildId - Guild ID where item was found
 * @param {Object} droppedItem - Dropped item data
 * @param {string} location - Drop location
 * @param {string} messageType - 'dm' or 'guild'
 * @param {Object} client - Discord client
 */
async function queueDelayedItemMessage(userId, contextGuildId, droppedItem, location, messageType, client) {
    try {
        console.log(`[itemDropsHybrid] ⏰ Queuing delayed ${messageType} message for ${droppedItem.itemName} (${userId})`);

        // Delay for 8 seconds to ensure database consistency
        setTimeout(async () => {
            try {
                console.log(`[itemDropsHybrid] 🚀 Processing delayed ${messageType} message for ${droppedItem.itemName} (${userId})`);

                // Calculate accurate discovery ranks using the working simple calculator
                const { calculateSimpleDiscoveryRank } = require('./discoveryRanks.js');

                const discoveryItem = {
                    itemName: droppedItem.itemName,
                    itemType: droppedItem.itemType,
                    foundInGuild: contextGuildId,
                    droppedAt: droppedItem.droppedAt || new Date()
                };

                const discoveryRanks = await calculateSimpleDiscoveryRank(discoveryItem, contextGuildId);
                console.log(`[itemDropsHybrid] 📊 Calculated discovery ranks: ${discoveryRanks.guildRank}/${discoveryRanks.guildTotal} server, ${discoveryRanks.globalRank}/${discoveryRanks.globalTotal} global`);

                // Create enhanced item with discovery ranks
                const enhancedItem = {
                    ...droppedItem,
                    discoveryRanks: discoveryRanks
                };

                // Send the appropriate message type
                if (messageType === 'dm') {
                    await sendItemDropDMWithRanks(userId, contextGuildId, enhancedItem, location, client);
                } else if (messageType === 'guild') {
                    await sendGuildChannelNotificationWithRanks(userId, contextGuildId, enhancedItem, location, client);
                } else if (messageType === 'notification') {
                    await addItemDropNotificationWithRanks(userId, contextGuildId, enhancedItem, location);
                }

            } catch (delayedError) {
                console.error(`[itemDropsHybrid] ❌ Error in delayed ${messageType} message:`, delayedError);
            }
        }, 8000); // 8 second delay

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error queuing delayed message:', error);
    }
}

/**
 * Add item drop notification to queue (OPTIMIZED from regular system)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (null for global)
 * @param {Array} droppedItems - Array of dropped items
 * @param {string} location - Drop location
 * @returns {boolean} Success status
 */
async function addItemDropNotification(userId, guildId, droppedItems, location) {
    try {
        const notification = {
            userId: userId,
            guildId: guildId, // null for global, specific for guild
            items: droppedItems,
            location: location,
            createdAt: new Date(),
            viewed: false
        };

        await optimizedInsertOne("item_notifications_queue", notification);

        const context = guildId ? `guild ${guildId}` : 'global';
        console.log(`[itemDropsHybrid] ✅ Added ${context} notification for ${userId} (${droppedItems[0].itemName})`);
        return true;

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error adding notification:', error);
        return false;
    }
}

/**
 * Send DM with accurate discovery ranks (OPTION 4)
 * @param {string} userId - User ID
 * @param {string} contextGuildId - Guild ID where item was found
 * @param {Object} enhancedItem - Item with discovery ranks
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 */
async function sendItemDropDMWithRanks(userId, contextGuildId, enhancedItem, location, client) {
    try {
        // FIXED: Use the same logic as original buildItemDMMessage but with discovery ranks
        const messageComponents = await buildItemDMMessageWithRanks(enhancedItem, contextGuildId, location, client);
        const components = Array.isArray(messageComponents) ? messageComponents : [messageComponents];

        // Send DM
        const user = await client.users.fetch(userId);
        await user.send({
            flags: require('discord.js').MessageFlags.IsComponentsV2,
            components: components
        });

        const contextText = contextGuildId ? `guild ${contextGuildId}` : 'global';
        console.log(`[itemDropsHybrid] ✅ Sent delayed DM to user ${userId} for ${enhancedItem.itemName} (${contextText}) with discovery ranks`);

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error sending delayed DM:', error);
    }
}

/**
 * Build DM message with discovery ranks (OPTION 4 - preserves original formatting)
 * @param {Object} droppedItem - Dropped item with discovery ranks
 * @param {string} contextGuildId - Guild ID where item was found
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 * @returns {Array} Message components
 */
async function buildItemDMMessageWithRanks(droppedItem, contextGuildId, location, client) {
    try {
        // Use the EXACT same logic as original buildItemDMMessage
        const { buildFoundItemContainer, DROP_LOCATIONS } = require('../commands/utility/items.js');

        // Convert location to display name
        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Get actual server name from context (where user found the item)
        let serverName = 'Server'; // Fallback

        if (contextGuildId && client) {
            try {
                const guild = await client.guilds.fetch(contextGuildId);
                serverName = guild.name; // Don't add markdown here, template will handle it
                itemDropsMetrics.serverNameResolutionSuccess++;
            } catch (error) {
                console.error('[itemDropsHybrid] Could not fetch guild name:', error);
                serverName = 'Server';
                itemDropsMetrics.serverNameResolutionFailure++;
            }
        }

        // OPTION 4: Add discovery ranks to context
        const context = {
            timestamp: droppedItem.droppedAt || new Date(),
            userId: droppedItem.userId,
            server: serverName,
            location: locationText,
            discoveryRanks: droppedItem.discoveryRanks  // ADDED: Discovery ranks
        };

        // Get custom DM message template (same as original)
        let dmMessageTemplate;
        try {
            const ownerConfig = await getCachedItemNotificationConfig('global');
            dmMessageTemplate = ownerConfig?.dmMessage || 'You found {items} in {server}, dropped from {location}:';
        } catch (error) {
            console.error('[itemDropsHybrid] Error fetching DM template:', error);
            dmMessageTemplate = 'You found {items} in {server}, dropped from {location}:';
        }

        // Build contextual message using template (same as original)
        const { TextDisplayBuilder } = require('discord.js');
        const { getArticle: getItemArticle } = require('../commands/utility/items.js');
        const article = getItemArticle(droppedItem.itemName);
        const itemText = `${article} ${droppedItem.itemEmote} **${droppedItem.itemName}**`;

        const contextMessage = dmMessageTemplate
            .replace('{items}', itemText)
            .replace('{server}', serverName)
            .replace('{location}', locationText);

        const contextComponent = new TextDisplayBuilder().setContent(contextMessage);

        // FIXED: Create itemData structure from droppedItem (same as original)
        const itemData = {
            name: droppedItem.itemName,
            type: droppedItem.itemType,
            rarity: droppedItem.itemRarity,
            emote: droppedItem.itemEmote,
            description: droppedItem.itemDescription,
            guildId: droppedItem.guildId
        };

        // Build item container with discovery ranks (preserves all original formatting)
        const itemContainer = await buildFoundItemContainer(itemData, droppedItem.catchData || {}, context);

        // FIXED: Return both components like the original
        return [contextComponent, itemContainer];

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error building DM message with ranks:', error);
        return [];
    }
}

/**
 * Add notification center item with accurate discovery ranks (OPTION 4)
 * @param {string} userId - User ID
 * @param {string} contextGuildId - Guild ID where item was found
 * @param {Object} enhancedItem - Item with discovery ranks
 * @param {string} location - Drop location
 */
async function addItemDropNotificationWithRanks(userId, contextGuildId, enhancedItem, location) {
    try {
        // Determine the correct guildId for notification storage
        const notificationGuildId = enhancedItem.guildId; // null for global, specific for guild

        const notification = {
            userId: userId,
            guildId: notificationGuildId,
            items: [enhancedItem], // Include enhanced item with discovery ranks
            location: location,
            createdAt: new Date(),
            viewed: false
        };

        console.log(`[itemDropsHybrid] 🔍 DEBUG: Adding notification with discovery ranks:`, {
            itemName: enhancedItem.itemName,
            discoveryRanks: enhancedItem.discoveryRanks
        });

        await optimizedInsertOne("item_notifications_queue", notification);

        const context = notificationGuildId ? `guild ${notificationGuildId}` : 'global';
        console.log(`[itemDropsHybrid] ✅ Added delayed ${context} notification for ${userId} (${enhancedItem.itemName}) with discovery ranks`);

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error adding delayed notification:', error);
    }
}

/**
 * Send guild channel notification with accurate discovery ranks (OPTION 4)
 * @param {string} userId - User ID
 * @param {string} contextGuildId - Guild ID
 * @param {Object} enhancedItem - Item with discovery ranks
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 */
async function sendGuildChannelNotificationWithRanks(userId, contextGuildId, enhancedItem, location, client) {
    try {
        // FIXED: Create guild channel message with discovery ranks (like DM system)
        const guildConfig = await getCachedGuildDropConfig(contextGuildId);

        if (!guildConfig.items?.dropNotificationsEnabled || !guildConfig.items?.dropChannel) {
            console.log(`[itemDropsHybrid] Guild notifications disabled or no channel configured for ${contextGuildId}`);
            return false;
        }

        const guild = await client.guilds.fetch(contextGuildId).catch(() => null);
        const channel = await client.channels.fetch(guildConfig.items.dropChannel).catch(() => null);

        if (!guild || !channel) {
            console.log(`[itemDropsHybrid] Could not fetch guild or channel for ${contextGuildId}`);
            return false;
        }

        // Build guild message with discovery ranks (same as DM system)
        const messageComponents = await buildItemGuildMessageWithRanks(userId, enhancedItem, location);

        if (messageComponents && messageComponents.length > 0) {
            await channel.send({
                flags: require('discord.js').MessageFlags.IsComponentsV2,
                components: messageComponents
            });
            console.log(`[itemDropsHybrid] ✅ Sent delayed guild notification for ${enhancedItem.itemName} with discovery ranks`);
            return true;
        } else {
            console.error('[itemDropsHybrid] ❌ No message components generated for guild notification');
            return false;
        }

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error sending delayed guild notification:', error);
        return false;
    }
}

/**
 * Build guild channel message with discovery ranks (OPTION 4)
 * @param {string} userId - User ID
 * @param {Object} enhancedItem - Item with discovery ranks
 * @param {string} location - Drop location
 * @returns {Array} Message components
 */
async function buildItemGuildMessageWithRanks(userId, enhancedItem, location) {
    try {
        // Use the same logic as DM but for guild channel format
        const { buildFoundItemContainer, DROP_LOCATIONS } = require('../commands/utility/items.js');

        // Convert location to display name
        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Create context with discovery ranks (same as DM system)
        const context = {
            timestamp: enhancedItem.droppedAt || new Date(),
            userId: userId,
            server: null, // Guild channel doesn't need server name (it's obvious)
            location: locationText,
            discoveryRanks: enhancedItem.discoveryRanks  // ADDED: Discovery ranks
        };

        // Create itemData structure from enhancedItem (same as DM system)
        const itemData = {
            name: enhancedItem.itemName,
            type: enhancedItem.itemType,
            rarity: enhancedItem.itemRarity,
            emote: enhancedItem.itemEmote,
            description: enhancedItem.itemDescription,
            guildId: enhancedItem.guildId
        };

        // Build item container with discovery ranks
        const itemContainer = await buildFoundItemContainer(itemData, enhancedItem.catchData || {}, context);

        return [itemContainer];

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error building guild message with ranks:', error);
        return [];
    }
}

/**
 * Build DM message for item drop (HYBRID: Self-contained, no external dependencies)
 * @param {Object} droppedItem - Dropped item
 * @param {string} contextGuildId - Guild ID where item was found
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 * @returns {Array} Message components
 */
async function buildItemDMMessage(droppedItem, contextGuildId, location, client) {
    try {
        // Use static item container builder for DM notifications
        const { buildFoundItemContainer, DROP_LOCATIONS } = require('../commands/utility/items.js');

        // Convert location to display name
        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Get actual server name from context (where user found the item)
        let serverName = 'Server'; // Fallback

        if (contextGuildId && client) {
            try {
                const guild = await client.guilds.fetch(contextGuildId);
                serverName = guild.name; // Don't add markdown here, template will handle it
                itemDropsMetrics.serverNameResolutionSuccess++;
            } catch (error) {
                console.error('[itemDropsHybrid] Could not fetch guild name:', error);
                serverName = 'Server';
                itemDropsMetrics.serverNameResolutionFailure++;
            }
        }

        // Build context for DM (no server context since it's already in the message)
        const context = {
            user: 'You',
            server: null, // Don't show server in DM container - it's redundant
            location: locationText,
            timestamp: droppedItem.droppedAt
        };

        // Create item data structure
        const itemData = {
            itemName: droppedItem.itemName,
            itemType: droppedItem.itemType,
            rarity: droppedItem.itemRarity,
            emote: droppedItem.itemEmote,
            description: droppedItem.itemDescription,
            guildId: droppedItem.guildId
        };

        // Get custom DM message template
        let dmMessageTemplate;
        try {
            const ownerConfig = await getCachedItemNotificationConfig('global');
            dmMessageTemplate = ownerConfig?.dmMessage || 'You found {items} in {server}, dropped from {location}:';
        } catch (error) {
            console.error('[itemDropsHybrid] Error fetching DM template:', error);
            dmMessageTemplate = 'You found {items} in {server}, dropped from {location}:';
        }

        // Build contextual message using template
        const { TextDisplayBuilder } = require('discord.js');
        const { getArticle: getItemArticle } = require('../commands/utility/items.js');
        const article = getItemArticle(droppedItem.itemName);
        const itemText = `${article} ${droppedItem.itemEmote} **${droppedItem.itemName}**`;

        const contextMessage = dmMessageTemplate
            .replace('{items}', itemText)
            .replace('{server}', serverName)
            .replace('{location}', locationText);

        const contextComponent = new TextDisplayBuilder().setContent(contextMessage);

        // Build item container (separate component)
        const itemContainer = await buildFoundItemContainer(itemData, droppedItem.catchData || {}, context, droppedItem.leaderboardResults || null);

        // Return both components as array (like server notifications)
        return [contextComponent, itemContainer];

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error building DM message:', error);
        return null;
    }
}

/**
 * Send item drop DM (HYBRID: Self-contained)
 * @param {string} userId - User ID
 * @param {string} contextGuildId - Guild ID where item was found (context)
 * @param {Array} droppedItems - Array of dropped items
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 * @returns {Object} Result object
 */
async function sendItemDropDM(userId, contextGuildId, droppedItems, location, client) {
    try {
        // Check if DM notifications are enabled
        const config = await getCachedItemNotificationConfig('global');
        if (!config.enabled) {
            return { success: false, reason: 'DM notifications disabled' };
        }

        const user = await client.users.fetch(userId).catch(() => null);
        if (!user) {
            return { success: false, reason: 'User not found' };
        }

        // Build DM message components (self-contained)
        const messageComponents = await buildItemDMMessage(droppedItems[0], contextGuildId, location, client);
        const components = Array.isArray(messageComponents) ? messageComponents : [messageComponents];

        await user.send({
            flags: require('discord.js').MessageFlags.IsComponentsV2,
            components: components
        });

        const context = contextGuildId ? `guild ${contextGuildId}` : 'global';
        console.log(`[itemDropsHybrid] ✅ Sent DM to user ${userId} for ${droppedItems[0].itemName} (${context})`);
        return { success: true };

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error sending DM:', error);
        return { success: false, error: error.message };
    }
}

/**
 * Build guild message for item drop (HYBRID: Self-contained)
 * @param {string} userId - User ID
 * @param {Object} droppedItem - Dropped item
 * @param {string} location - Drop location
 * @returns {Array} Message components
 */
async function buildItemGuildMessage(userId, droppedItem, location) {
    try {
        // Use server container builder for guild notifications
        const { buildServerFoundItemContainer } = require('../commands/utility/items.js');

        // Create item data structure
        const itemData = {
            itemName: droppedItem.itemName,
            itemType: droppedItem.itemType,
            rarity: droppedItem.itemRarity,
            emote: droppedItem.itemEmote,
            description: droppedItem.itemDescription,
            guildId: droppedItem.guildId
        };

        // Build context for guild message
        const context = {
            user: `<@${userId}>`,
            server: null, // Server context not needed in guild channels
            location: location,
            timestamp: droppedItem.droppedAt
        };

        // Build contextual message with emoji (matching DM format)
        const { TextDisplayBuilder } = require('discord.js');
        const { getArticle: getItemArticle } = require('../commands/utility/items.js');
        const article = getItemArticle(droppedItem.itemName);
        const itemText = `${article} ${droppedItem.itemEmote} **${droppedItem.itemName}**`;

        // Get location display name
        const { DROP_LOCATIONS } = require('../commands/utility/items.js');
        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        const contextMessage = `<@${userId}> found ${itemText}, dropped from ${locationText}:`;

        // Update context to use display name for the item container
        const updatedContext = {
            ...context,
            location: locationText  // Use display name instead of raw location
        };

        // Build item container (AWAIT THE PROMISE!)
        const itemContainerResult = await buildServerFoundItemContainer(itemData, droppedItem.catchData || {}, updatedContext, droppedItem.leaderboardResults || null);

        // buildServerFoundItemContainer returns [contextText, container]
        // We want to replace the contextText with our own contextMessage
        if (Array.isArray(itemContainerResult) && itemContainerResult.length >= 2) {
            // Replace the first component (contextText) with our contextMessage
            const contextComponent = new TextDisplayBuilder().setContent(contextMessage);
            const itemContainer = itemContainerResult[1]; // Keep the actual item container

            return [contextComponent, itemContainer];
        } else {
            // Fallback: create our own context component
            const contextComponent = new TextDisplayBuilder().setContent(contextMessage);
            const itemComponents = Array.isArray(itemContainerResult) ? itemContainerResult : [itemContainerResult];

            return [contextComponent, ...itemComponents];
        }

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error building guild message:', error);
        return null;
    }
}

/**
 * Send guild channel notification (HYBRID: Self-contained)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Array} droppedItems - Array of dropped items
 * @param {string} location - Drop location
 * @param {Object} client - Discord client
 * @returns {boolean} Success status
 */
async function sendGuildChannelNotification(userId, guildId, droppedItems, location, client) {
    try {
        const guildConfig = await getCachedGuildDropConfig(guildId);



        if (!guildConfig.items?.dropNotificationsEnabled || !guildConfig.items?.dropChannel) {
            console.log(`[itemDropsHybrid] Guild notifications disabled or no channel configured for ${guildId}: enabled=${guildConfig.items?.dropNotificationsEnabled}, channelId=${guildConfig.items?.dropChannel}`);
            return false; // Guild notifications disabled or no channel configured
        }

        const guild = await client.guilds.fetch(guildId).catch(() => null);
        const channel = await client.channels.fetch(guildConfig.items.dropChannel).catch(() => null);

        if (!guild || !channel) {
            console.log(`[itemDropsHybrid] Could not fetch guild or channel for ${guildId}`);
            return false;
        }

        // Build guild message components (self-contained) - AWAIT THE PROMISE!
        const messageComponents = await buildItemGuildMessage(userId, droppedItems[0], location);

        if (!messageComponents) {
            console.log(`[itemDropsHybrid] Failed to build guild message components`);
            return false;
        }

        const components = Array.isArray(messageComponents) ? messageComponents : [messageComponents];

        await channel.send({
            flags: require('discord.js').MessageFlags.IsComponentsV2,
            allowedMentions: { parse: [] },
            components: components
        });


        console.log(`[itemDropsHybrid] ✅ Sent guild notification to #${channel.name} for ${droppedItems[0].itemName}`);
        return true;

    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error sending guild notification:', error);
        return false;
    }
}

/**
 * Check if this is the first item drop in server (KEPT from regular system)
 * @param {string} guildId - Guild ID
 * @param {string} itemId - Item ID
 * @returns {boolean} True if first drop
 */
async function checkFirstItemDropInServer(guildId, itemId) {
    try {
        const existingDrop = await optimizedFindOne('user_inventory', {
            guildId: guildId,
            itemId: itemId
        });

        return !existingDrop;
    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error checking first drop:', error);
        return false;
    }
}

/**
 * Get user inventory (KEPT from regular system)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Array} User inventory
 */
async function getUserInventory(userId, guildId) {
    try {
        return await optimizedFind('user_inventory', {
            userId: userId,
            guildId: guildId
        });
    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error getting user inventory:', error);
        return [];
    }
}

/**
 * Get user global inventory (KEPT from regular system)
 * @param {string} userId - User ID
 * @returns {Array} User global inventory
 */
async function getUserGlobalInventory(userId) {
    try {
        return await optimizedFind('user_inventory', {
            userId: userId,
            guildId: null
        });
    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error getting user global inventory:', error);
        return [];
    }
}

/**
 * Get user item notifications (KEPT from regular system)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (null for global)
 * @returns {Array} User notifications
 */
async function getUserItemNotifications(userId, guildId) {
    try {
        return await optimizedFind('item_notifications_queue', {
            userId: userId,
            guildId: guildId,
            viewed: false
        });
    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error getting user notifications:', error);
        return [];
    }
}

/**
 * Dismiss item notification (KEPT from regular system with enhanced logging)
 * @param {string} notificationId - Notification ID
 * @returns {boolean} Success status
 */
async function dismissItemNotification(notificationId) {
    try {
        const result = await optimizedUpdateOne('item_notifications_queue',
            { _id: new ObjectId(notificationId) },
            { $set: { viewed: true, viewedAt: new Date() } }
        );

        if (itemDropsMetrics.verboseLogging) {
            console.log(`[itemDropsHybrid] ✅ Dismissed notification ${notificationId}: ${result.modifiedCount} modified`);
        }

        return result.modifiedCount > 0;
    } catch (error) {
        console.error('[itemDropsHybrid] ❌ Error dismissing notification:', error);
        return false;
    }
}

/**
 * Get cache statistics (ENHANCED from regular system with hybrid metrics)
 * @returns {Object} Cache statistics
 */
function getCacheStats() {
    const totalCacheHits = itemDropsMetrics.cacheHits;
    const totalCacheMisses = itemDropsMetrics.cacheMisses;
    const totalRequests = totalCacheHits + totalCacheMisses;
    const cacheHitRate = totalRequests > 0 ? ((totalCacheHits / totalRequests) * 100).toFixed(2) : 0;

    // Calculate memory usage for all caches
    const cacheMemoryUsage = {
        guildDropConfig: guildDropConfigCache.size,
        itemNotificationConfig: itemNotificationConfigCache.size,
        userDropSettings: userDropSettingsCache.size,
        droppableItems: droppableItemsCache.size,
        total: guildDropConfigCache.size + itemNotificationConfigCache.size +
               userDropSettingsCache.size + droppableItemsCache.size
    };

    // System health assessment
    const systemHealth = {
        status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
        cacheEfficiency: cacheHitRate,
        separationIntegrity: itemDropsMetrics.crossContaminationPrevented
    };

    return {
        // Performance metrics (enhanced with hybrid-specific data)
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: itemDropsMetrics.cacheHits,
            cacheMisses: itemDropsMetrics.cacheMisses,
            databaseQueries: itemDropsMetrics.databaseQueries,
            averageQueryTime: `${itemDropsMetrics.averageQueryTime.toFixed(2)}ms`,
            itemsProcessed: itemDropsMetrics.itemsProcessed,
            dropsSuccessful: itemDropsMetrics.dropsSuccessful,
            parallelOperations: itemDropsMetrics.parallelOperations,
            globalItemsProcessed: itemDropsMetrics.globalItemsProcessed,
            guildItemsProcessed: itemDropsMetrics.guildItemsProcessed,
            crossContaminationPrevented: itemDropsMetrics.crossContaminationPrevented,
            successRate: itemDropsMetrics.itemsProcessed > 0 ?
                `${((itemDropsMetrics.dropsSuccessful / itemDropsMetrics.itemsProcessed) * 100).toFixed(2)}%` : '0%',
            lastOptimization: new Date(itemDropsMetrics.lastOptimization).toISOString()
        },

        // Cache memory usage
        totalMemoryUsage: cacheMemoryUsage,

        // System health
        systemHealth: systemHealth,

        // Individual cache stats
        caches: {
            guildDropConfig: {
                size: guildDropConfigCache.size,
                maxSize: guildDropConfigCache.maxSize || 'unlimited'
            },
            itemNotificationConfig: {
                size: itemNotificationConfigCache.size,
                maxSize: itemNotificationConfigCache.maxSize || 'unlimited'
            },
            userDropSettings: {
                size: userDropSettingsCache.size,
                maxSize: userDropSettingsCache.maxSize || 'unlimited'
            },
            droppableItems: {
                size: droppableItemsCache.size,
                maxSize: droppableItemsCache.maxSize || 'unlimited'
            }
        }
    };
}

/**
 * Generate performance recommendations (ENHANCED from regular system)
 * @returns {Array} Performance recommendations
 */
function generatePerformanceRecommendations() {
    const stats = getCacheStats();
    const metrics = itemDropsMetrics;
    const recommendations = [];

    // Cache performance recommendations
    const cacheHitRate = parseFloat(stats.performance.cacheHitRate);
    if (cacheHitRate < 60) {
        recommendations.push('Low cache hit rate - consider increasing cache TTL or size');
    }

    // Database query recommendations
    if (metrics.averageQueryTime > 100) {
        recommendations.push('High average query time - investigate database performance');
    }

    // Parallel processing recommendations
    if (metrics.parallelOperations < metrics.itemsProcessed * 0.5) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    // Drop success rate recommendations
    if (metrics.itemsProcessed > 0 && (metrics.dropsSuccessful / metrics.itemsProcessed) < 0.8) {
        recommendations.push('Low drop success rate - investigate error patterns');
    }

    // Hybrid-specific recommendations
    if (metrics.crossContaminationPrevented > 0) {
        recommendations.push(`Prevented ${metrics.crossContaminationPrevented} cross-contamination attempts - separation logic working correctly`);
    }

    if (metrics.globalItemsProcessed === 0 && metrics.guildItemsProcessed === 0) {
        recommendations.push('No items processed yet - system ready for drops');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal - hybrid separation logic functioning perfectly');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (ENHANCED from regular system)
 * OPTIMIZED: Automatic performance analysis and cache optimization with hybrid metrics
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    itemDropsMetrics.lastOptimization = now;

    // Log performance statistics with enhanced hybrid-specific metrics
    const stats = getCacheStats();
    console.log(`[itemDropsHybrid] 📊 Performance Report:`);
    console.log(`[itemDropsHybrid]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[itemDropsHybrid]   Items Processed: ${stats.performance.itemsProcessed}`);
    console.log(`[itemDropsHybrid]   Success Rate: ${stats.performance.successRate}`);
    console.log(`[itemDropsHybrid]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[itemDropsHybrid]   Global Items: ${stats.performance.globalItemsProcessed}`);
    console.log(`[itemDropsHybrid]   Guild Items: ${stats.performance.guildItemsProcessed}`);
    console.log(`[itemDropsHybrid]   Cross-Contamination Prevented: ${stats.performance.crossContaminationPrevented}`);
    console.log(`[itemDropsHybrid]   Guild Context Properly Passed: ${itemDropsMetrics.guildContextProperlyPassed}`);
    console.log(`[itemDropsHybrid]   Guild Context Missing: ${itemDropsMetrics.guildContextMissing}`);
    console.log(`[itemDropsHybrid]   Server Name Resolution Success: ${itemDropsMetrics.serverNameResolutionSuccess}`);
    console.log(`[itemDropsHybrid]   Server Name Resolution Failure: ${itemDropsMetrics.serverNameResolutionFailure}`);
    console.log(`[itemDropsHybrid]   Drop Locations: TEXT:${itemDropsMetrics.dropLocationStats.TEXT} VOICE:${itemDropsMetrics.dropLocationStats.VOICE} STARFALL:${itemDropsMetrics.dropLocationStats.STARFALL} LEVEL_UP:${itemDropsMetrics.dropLocationStats.LEVEL_UP}`);
    console.log(`[itemDropsHybrid]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[itemDropsHybrid]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[itemDropsHybrid]   System Health: ${stats.systemHealth.status}`);

    // Generate and log recommendations
    const recommendations = generatePerformanceRecommendations();
    recommendations.forEach(rec => {
        console.log(`[itemDropsHybrid] ⚠️  ${rec}`);
    });

    // Reset metrics for next interval (keep cumulative counters)
    const cumulativeMetrics = {
        cacheHits: itemDropsMetrics.cacheHits,
        cacheMisses: itemDropsMetrics.cacheMisses,
        databaseQueries: itemDropsMetrics.databaseQueries,
        itemsProcessed: itemDropsMetrics.itemsProcessed,
        dropsSuccessful: itemDropsMetrics.dropsSuccessful,
        globalItemsProcessed: itemDropsMetrics.globalItemsProcessed,
        guildItemsProcessed: itemDropsMetrics.guildItemsProcessed,
        crossContaminationPrevented: itemDropsMetrics.crossContaminationPrevented
    };

    // Reset interval-based metrics
    itemDropsMetrics.parallelOperations = 0;
    itemDropsMetrics.averageQueryTime = 0;
}

/**
 * Clear all item drop caches (KEPT from regular system)
 */
function clearAllItemDropCaches() {
    guildDropConfigCache.clear();
    itemNotificationConfigCache.clear();
    userDropSettingsCache.clear();
    droppableItemsCache.clear();

    console.log('[itemDropsHybrid] 🧹 All item drop caches cleared');
}

/**
 * Clear specific cache (KEPT from regular system)
 * @param {string} cacheType - Cache type to clear
 */
function clearSpecificCache(cacheType) {
    switch (cacheType) {
        case 'guildDropConfig':
            guildDropConfigCache.clear();
            break;
        case 'itemNotificationConfig':
            itemNotificationConfigCache.clear();
            break;
        case 'userDropSettings':
            userDropSettingsCache.clear();
            break;
        case 'droppableItems':
            droppableItemsCache.clear();
            break;
        default:
            console.warn(`[itemDropsHybrid] Unknown cache type: ${cacheType}`);
            return;
    }

    console.log(`[itemDropsHybrid] 🧹 Cleared ${cacheType} cache`);
}

/**
 * Invalidate item drop caches (KEPT from regular system)
 * @param {string} guildId - Guild ID to invalidate
 */
function invalidateItemDropCaches(guildId) {
    // Clear guild-specific caches
    guildDropConfigCache.delete(guildId);

    // Clear droppable items cache for this guild
    const keysToDelete = [];
    droppableItemsCache.forEach((value, key) => {
        if (key.startsWith(guildId)) {
            keysToDelete.push(key);
        }
    });

    keysToDelete.forEach(key => droppableItemsCache.delete(key));

    console.log(`[itemDropsHybrid] 🧹 Invalidated caches for guild ${guildId}`);
}

// Set up automatic performance monitoring (ENHANCED from regular system)
if (itemDropsMetrics.performanceReportInterval > 0) {
    setInterval(performanceCleanupAndOptimization, itemDropsMetrics.performanceReportInterval);
    console.log(`[itemDropsHybrid] 📊 Performance monitoring enabled (${itemDropsMetrics.performanceReportInterval / 1000}s intervals)`);
}

module.exports = {
    // Core functions (HYBRID: Performance + Clear Logic)
    performMasterRoll,
    getDroppableItems,
    processItemDrops,
    addItemToInventory,
    getUserInventory,
    getUserGlobalInventory,
    checkFirstItemDropInServer,
    sendItemDropDM,
    sendGuildChannelNotification,
    addItemDropNotification,
    getUserItemNotifications,
    dismissItemNotification,

    // Notification processing (HYBRID: Bulletproof separation + Performance)
    processItemNotifications,

    // Enhanced optimization functions (KEPT from regular system)
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllItemDropCaches,
    clearSpecificCache,
    invalidateItemDropCaches,
    getCachedGuildDropConfig,
    getCachedItemNotificationConfig,
    getCachedUserDropSettings,

    // Performance metrics (read-only, ENHANCED with hybrid data)
    getPerformanceMetrics: () => ({ ...itemDropsMetrics })
};
