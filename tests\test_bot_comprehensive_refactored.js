/**
 * COMPREHENSIVE BOT TESTING - REFACTORED VERSION
 * Uses the shared BotTestBase for reusable setup and utilities
 * 
 * This test validates all major bot functionality using real Discord API
 * connections and live database operations.
 */

const { BotTestBase, TestUtils } = require('./shared/BotTestBase.js');

class ComprehensiveBotTester extends BotTestBase {
    constructor() {
        super('ComprehensiveBotTest');
    }

    /**
     * Test owner.js items flow functionality
     */
    async testOwnerItemsFlow() {
        const interaction = this.createMockInteraction(5, 'owner-features-select', ['items']);
        
        const owner = require('../commands/utility/owner.js');
        await owner.select(interaction, []);
        
        console.log(`   Responses: ${interaction._responses.length}`);
        
        // Validate response structure
        if (interaction._responses.length === 0) {
            throw new Error('No responses generated');
        }
        
        return true;
    }

    /**
     * Test notification views integration
     */
    async testNotificationViewsIntegration() {
        // Test items collection for LEVEL_UP items
        const itemsCol = this.mongoClient.db('test').collection('custom_items');
        const levelUpItems = await itemsCol.find({ 
            dropLocations: 'LEVEL_UP',
            disabled: { $ne: true }
        }).toArray();
        
        console.log(`   Found ${levelUpItems.length} LEVEL_UP items in database`);
        
        if (levelUpItems.length === 0) {
            console.log('   ⚠️  No LEVEL_UP items found - notification views will use fallback');
        } else {
            levelUpItems.slice(0, 3).forEach(item => {
                console.log(`   - ${item.name} (${item.rarity})`);
            });
        }
        
        // Test notification view creation
        const you = require('../commands/utility/you.js');
        const interaction = this.createMockInteraction(5, 'you-hub-select', ['notifications']);
        
        await you.select(interaction, []);
        
        return interaction._responses.length > 0;
    }

    /**
     * Test global level system functionality
     */
    async testGlobalLevelSystem() {
        try {
            const ownerGlobalLevels = require('../commands/utility/owner-global-levels.js');
            const interaction = this.createMockInteraction(5, 'owner-global-levels-select', ['view']);

            // Use the correct function name
            await ownerGlobalLevels.handleGlobalLevelsSelect(interaction);

            console.log(`   Global levels responses: ${interaction._responses.length}`);

            // Validate that global levels data is accessible
            const globalLevelsCol = this.mongoClient.db('test').collection('global_levels');
            const levelCount = await globalLevelsCol.countDocuments();
            console.log(`   Global levels in database: ${levelCount}`);

            return interaction._responses.length > 0;
        } catch (error) {
            console.log(`   Global levels error: ${error.message}`);
            return false;
        }
    }

    /**
     * Test /you command performance
     */
    async testYouCommandPerformance() {
        const startTime = Date.now();
        
        const you = require('../commands/utility/you.js');
        const interaction = this.createMockInteraction(2, 'you'); // Chat input command
        interaction.options = {
            getString: () => null,
            getUser: () => null
        };
        
        await you.execute(interaction);
        
        const duration = Date.now() - startTime;
        console.log(`   /you command execution time: ${duration}ms`);
        
        // Performance threshold: should complete within 2 seconds
        if (duration > 2000) {
            console.log(`   ⚠️  Performance warning: /you took ${duration}ms (>2000ms threshold)`);
        }
        
        return interaction._responses.length > 0;
    }

    /**
     * Test /17 command performance
     */
    async test17CommandPerformance() {
        const startTime = Date.now();
        
        const seventeen = require('../commands/utility/17.js');
        const interaction = this.createMockInteraction(2, '17'); // Chat input command
        interaction.options = {
            getString: () => null
        };
        
        await seventeen.execute(interaction);
        
        const duration = Date.now() - startTime;
        console.log(`   /17 command execution time: ${duration}ms`);
        
        // Performance threshold: should complete within 2 seconds
        if (duration > 2000) {
            console.log(`   ⚠️  Performance warning: /17 took ${duration}ms (>2000ms threshold)`);
        }
        
        return interaction._responses.length > 0;
    }

    /**
     * Test modular owner system functionality
     */
    async testModularOwnerSystem() {
        try {
            // Test owner status module
            const ownerStatus = require('../commands/utility/owner-status.js');
            const statusInteraction = this.createMockInteraction(5, 'owner-status-select', ['view']);

            await ownerStatus.handleStatusSelect(statusInteraction);
            console.log(`   Owner status responses: ${statusInteraction._responses.length}`);

            // Test owner servers module - check if function exists
            const ownerServers = require('../commands/utility/owner-servers.js');
            const serversInteraction = this.createMockInteraction(5, 'owner-servers-select', ['list']);

            if (typeof ownerServers.handleServersSelect === 'function') {
                await ownerServers.handleServersSelect(serversInteraction);
                console.log(`   Owner servers responses: ${serversInteraction._responses.length}`);
                return statusInteraction._responses.length > 0 && serversInteraction._responses.length > 0;
            } else {
                console.log(`   Owner servers function not available - testing status only`);
                return statusInteraction._responses.length > 0;
            }
        } catch (error) {
            console.log(`   Modular owner system error: ${error.message}`);
            return false;
        }
    }

    /**
     * Test all major bot interactions
     */
    async testAllInteractions() {
        const testInteractions = [
            { command: 'exp', customId: 'exp-levels-select', values: ['create'] },
            { command: 'items', customId: 'items-select', values: ['create'] },
            { command: 'sticky', customId: 'sticky-select', values: ['roles'] },
            { command: 'logs', customId: 'logs-select', values: ['message'] }
        ];

        let successCount = 0;
        
        for (const test of testInteractions) {
            try {
                const command = require(`../commands/utility/${test.command}.js`);
                const interaction = this.createMockInteraction(5, test.customId, test.values);
                
                if (command.select) {
                    await command.select(interaction, []);
                    console.log(`   ✅ ${test.command} select interaction successful`);
                    successCount++;
                } else {
                    console.log(`   ⚠️  ${test.command} has no select handler`);
                }
            } catch (error) {
                console.log(`   ❌ ${test.command} interaction failed: ${error.message}`);
            }
        }
        
        console.log(`   Successfully tested ${successCount}/${testInteractions.length} interactions`);
        return successCount > 0;
    }

    /**
     * Run all comprehensive tests
     */
    async runAllTests() {
        console.log('🚀 Starting comprehensive bot testing with real environment...\n');
        console.log('=' .repeat(80));
        console.log('TESTING WITH REAL BOT LOGIN, DATABASE, AND DISCORD API');
        console.log('=' .repeat(80));

        // Define all tests to run
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: 'Owner items flow', test: () => this.testOwnerItemsFlow() },
            { name: 'Notification views integration', test: () => this.testNotificationViewsIntegration() },
            { name: 'Global level system', test: () => this.testGlobalLevelSystem() },
            { name: 'You command performance', test: () => this.testYouCommandPerformance() },
            { name: '17 command performance', test: () => this.test17CommandPerformance() },
            { name: 'Modular owner system', test: () => this.testModularOwnerSystem() },
            { name: 'All bot interactions', test: () => this.testAllInteractions() }
        ];

        // Run all tests using the base class helper
        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        // Print comprehensive results
        this.printResults();
        
        // Return overall success status
        const passedTests = this.testResults.filter(r => r.passed).length;
        return passedTests === this.testResults.length;
    }
}

// Main execution function
async function runComprehensiveTest() {
    const tester = new ComprehensiveBotTester();
    
    try {
        // Validate environment before starting
        tester.validateEnvironment();
        
        // Initialize test environment
        await tester.initialize();
        
        // Run all tests
        const allPassed = await tester.runAllTests();
        
        // Exit with appropriate code
        process.exit(allPassed ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Test execution failed:', error.message);
        console.error('   Make sure .env file has TOKEN and MONGO set correctly');
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Run if this file is executed directly
if (require.main === module) {
    runComprehensiveTest();
}

module.exports = { ComprehensiveBotTester, runComprehensiveTest };
