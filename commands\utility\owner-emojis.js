const { Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ThumbnailBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ActionRowBuilder, MessageFlags, SeparatorBuilder, SeparatorSpacingSize, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne, optimizedDeleteOne, optimizedFind, optimizedCountDocuments } = require('../../utils/database-optimizer.js');
const { getRecentImagesFromChannel, uploadImageAsEmote, buildImageSelectMenu, buildNoImagesSelectMenu, handleImageSelection } = require('../../utils/imageUploader.js');
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');

// Performance metrics for emoji system (following items pattern)
const emojiMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    emojisCreated: 0,
    emojisDeleted: 0,
    lastOptimization: Date.now(),
    verboseLogging: process.env.NODE_ENV === 'development'
};

// State management for emoji creation/editing (like items system)
const emojiStateCache = new Map();

/**
 * Store emoji creation/editing state
 */
function storeEmojiState(userId, state) {
    emojiStateCache.set(userId, { ...state, updatedAt: new Date() });
}

/**
 * Get emoji creation/editing state
 */
function getEmojiState(userId) {
    return emojiStateCache.get(userId) || null;
}

/**
 * Clear emoji creation/editing state
 */
function clearEmojiState(userId) {
    emojiStateCache.delete(userId);
}

/**
 * Clear all feature states when switching between features (ENHANCED: Cross-feature state management)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (for items)
 */
async function clearAllFeatureStates(userId, guildId) {
    // Clear custom emoji state
    clearEmojiState(userId);

    // Clear global levels state (if available)
    try {
        const { clearGlobalLevelState } = require('./owner-global-levels.js');
        clearGlobalLevelState(userId);
    } catch (error) {
        // Global levels module might not be available
    }

    // Clear items state (if available)
    try {
        const { clearCreationState } = require('./items.js');
        await clearCreationState(userId, guildId);
    } catch (error) {
        // Items module might not be available
    }

    console.log(`[owner-emojis] 🧹 Cleared all feature states for user ${userId}`);
}

/**
 * Create a new custom emoji in the database
 * @param {Object} emojiData - Emoji data
 * @param {string} emojiData.name - Emoji name (becomes Discord emoji name)
 * @param {string} emojiData.description - Emoji description/usage
 * @param {string} emojiData.emote - Discord emoji string (<:name:id>)
 * @param {string} emojiData.emoteId - Discord emoji ID
 * @param {string} emojiData.createdBy - User ID who created the emoji
 * @returns {Object} Created emoji with ID
 */
async function createEmoji(emojiData) {
    try {
        const emoji = {
            ...emojiData,
            id: `emoji_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            createdAt: new Date()
        };

        await optimizedInsertOne('custom_emojis', emoji);
        emojiMetrics.emojisCreated++;

        if (emojiMetrics.verboseLogging) {
            console.log(`[owner-emojis] ✅ Created emoji: ${emoji.name} (${emoji.id})`);
        }

        return emoji;
    } catch (error) {
        console.error('[owner-emojis] Error creating emoji:', error);
        throw error;
    }
}

/**
 * Get emojis for display with pagination (following items pattern)
 * @param {number} page - Page number (0-based)
 * @param {number} limit - Items per page
 * @returns {Object} Emojis and pagination info
 */
async function getEmojisForDisplay(page = 0, limit = 10) {
    try {
        const startTime = Date.now();
        
        // Get total count and emojis in parallel
        const [totalCount, emojis] = await Promise.all([
            optimizedCountDocuments('custom_emojis', {}),
            optimizedFind('custom_emojis', {}, {
                sort: { createdAt: -1 },
                skip: page * limit,
                limit: limit
            })
        ]);

        emojiMetrics.databaseQueries += 2;
        emojiMetrics.averageQueryTime = Date.now() - startTime;

        const totalPages = Math.ceil(totalCount / limit);
        const hasNextPage = page < totalPages - 1;
        const hasPrevPage = page > 0;

        return {
            emojis,
            totalCount,
            totalPages,
            currentPage: page,
            hasNextPage,
            hasPrevPage,
            limit
        };
    } catch (error) {
        console.error('[owner-emojis] Error getting emojis for display:', error);
        throw error;
    }
}

/**
 * Build custom emojis container (following items pattern)
 * @param {number} page - Current page
 * @param {Object} client - Discord client
 * @param {boolean} showBackButton - Whether to show back button
 * @param {string} statusMessage - Optional status message
 * @returns {ContainerBuilder} The emojis container
 */
async function buildCustomEmojisContainer({ page = 0, client = null, showBackButton = false, statusMessage = null }) {
    const components = [];
    
    // Header section with back button (only in owner panel context)
    if (showBackButton) {
        const heading = new TextDisplayBuilder().setContent('# custom emojis');
        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(heading)
            .setButtonAccessory(backButton);
        components.push(backSection);
    } else {
        const heading = new TextDisplayBuilder().setContent('# custom emojis');
        components.push(heading);
    }

    const description = new TextDisplayBuilder().setContent('> manage custom emojis for the bot');
    components.push(description);

    // Get emojis for display
    const emojiData = await getEmojisForDisplay(page, 10);
    
    // Build emoji list display (following items format: <emote> Name | description)
    if (emojiData.emojis.length > 0) {
        let emojiListText = '';
        emojiData.emojis.forEach(emoji => {
            const truncatedDescription = emoji.description.length > 50 
                ? emoji.description.substring(0, 47) + '...' 
                : emoji.description;
            emojiListText += `${emoji.emote} ${emoji.name} | ${truncatedDescription}\n`;
        });

        const emojiList = new TextDisplayBuilder().setContent(emojiListText.trim());
        components.push(emojiList);

        // Pagination info
        if (emojiData.totalPages > 1) {
            const paginationText = `Page ${emojiData.currentPage + 1} of ${emojiData.totalPages} (${emojiData.totalCount} total emojis)`;
            const paginationDisplay = new TextDisplayBuilder().setContent(paginationText);
            components.push(paginationDisplay);
        }
    } else {
        const noEmojisText = new TextDisplayBuilder().setContent('__no custom emojis found.__');
        components.push(noEmojisText);
    }

    // Emoji selection menu (always show "Add New Emoji" option at top)
    const emojiSelect = new StringSelectMenuBuilder()
        .setCustomId('custom-emojis-select')
        .setPlaceholder('emoji(s)')
        .addOptions({
            label: 'Add New Emoji',
            value: 'add-new',
            description: 'Create a new custom emoji',
            emoji: '➕'
        });

    // Add existing emojis to select menu
    if (emojiData.emojis.length > 0) {
        emojiData.emojis.forEach(emoji => {
            const truncatedDescription = emoji.description.length > 100 
                ? emoji.description.substring(0, 97) + '...' 
                : emoji.description;
            
            emojiSelect.addOptions({
                label: emoji.name,
                value: emoji.id,
                description: truncatedDescription,
                emoji: emoji.emote
            });
        });
    }

    const selectRow = new ActionRowBuilder().addComponents(emojiSelect);
    components.push(selectRow);

    // Pagination buttons (if needed)
    if (emojiData.totalPages > 1) {
        const paginationButtons = [];
        
        if (emojiData.hasPrevPage) {
            paginationButtons.push(
                new ButtonBuilder()
                    .setCustomId(`custom-emojis-page-${emojiData.currentPage - 1}`)
                    .setLabel('◀ Previous')
                    .setStyle(ButtonStyle.Secondary)
            );
        }
        
        if (emojiData.hasNextPage) {
            paginationButtons.push(
                new ButtonBuilder()
                    .setCustomId(`custom-emojis-page-${emojiData.currentPage + 1}`)
                    .setLabel('Next ▶')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        if (paginationButtons.length > 0) {
            const paginationRow = new ActionRowBuilder().addComponents(...paginationButtons);
            components.push(paginationRow);
        }
    }

    // Build final container
    const container = new ContainerBuilder()
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        } else {
            container.addTextDisplayComponents(component);
        }
    });

    // FIXED: Add status message at the very bottom of the container
    if (statusMessage) {
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
        container.addTextDisplayComponents(statusDisplay);
    }

    return container;
}

/**
 * Build emoji creation/editing container (COPIED FROM ITEMS SYSTEM)
 * @param {string} userId - User ID
 * @param {Object} client - Discord client
 * @param {string} mode - 'create' or 'edit'
 * @param {Object} existingEmoji - Existing emoji data (for edit mode)
 * @param {Object} interaction - Discord interaction for fresh image fetching
 * @returns {ContainerBuilder} The emoji creation/editing container
 */
async function buildEmojiEditorContainer(userId, client, mode = 'create', existingEmoji = null, interaction = null) {
    const state = getEmojiState(userId) || {};
    const isEditing = mode === 'edit' && existingEmoji;

    // Get current values for config options FIRST (before using them)
    const currentEmoji = isEditing ? existingEmoji : null;
    const currentName = state.emojiName || (currentEmoji ? currentEmoji.name : '');
    const currentDescription = state.emojiDescription || (currentEmoji ? currentEmoji.description : '');
    const currentEmote = state.emojiEmote || (currentEmoji ? currentEmoji.emote : '');

    const components = [];

    // Header (show emoji name when editing)
    const heading = new TextDisplayBuilder().setContent(
        isEditing ? `# edit ${currentName || 'emoji'}` : '# create emoji'
    );
    const backButton = new ButtonBuilder()
        .setCustomId('custom-emojis-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);
    components.push(backSection);

    const description = new TextDisplayBuilder().setContent(
        isEditing ? `> edit ${currentName || 'your custom emoji'}` : '> create a new custom emoji'
    );
    components.push(description);

    // ALWAYS show main configuration select menu (COPIED FROM ITEMS SYSTEM)
    const configOptions = [];

    // Name configuration (COPIED FROM ITEMS SYSTEM FORMAT)
    configOptions.push({
        label: 'name',
        value: 'name',
        description: currentName || 'Set emoji name',
        emoji: '📝',
        default: state.currentConfig === 'name'
    });

    // Description configuration (COPIED FROM ITEMS SYSTEM FORMAT)
    configOptions.push({
        label: 'description',
        value: 'description',
        description: currentDescription || 'Set emoji description',
        emoji: '📄',
        default: state.currentConfig === 'description'
    });

    // Upload option (HIDDEN IN EDIT MODE - changing image would be creating new emoji)
    if (!isEditing) {
        // Extract clean emote name for display (COPIED FROM ITEMS SYSTEM)
        let emoteDescription = 'Set emoji image';
        if (currentEmote) {
            if (currentEmote.includes(':')) {
                // Custom emote format: <:name:id> or <a:name:id> - extract just the name
                const emoteMatch = currentEmote.match(/<a?:([^:]+):\d+>/);
                emoteDescription = emoteMatch ? emoteMatch[1] : currentEmote;
            } else {
                // Regular emoji
                emoteDescription = currentEmote;
            }
        }

        configOptions.push({
            label: 'upload',
            value: 'upload',
            description: emoteDescription,
            emoji: '🖼️',
            default: state.currentConfig === 'upload'
        });
    }

    const configSelect = new StringSelectMenuBuilder()
        .setCustomId('emoji-config-select')
        .setPlaceholder('configure emoji...')
        .addOptions(configOptions);

    const configRow = new ActionRowBuilder().addComponents(configSelect);
    components.push(configRow);

    // CASCADING: Show image upload interface as SECOND menu when upload is selected
    if (state.currentConfig === 'upload') {
        // COPIED FROM ITEMS SYSTEM: Use fresh image fetching when interaction is available, fallback to direct fetch
        let recentImages = [];

        if (interaction) {
            try {
                const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
                recentImages = await forceRefreshImageCache(interaction);
            } catch (error) {
                console.error('[owner-emojis] Error with forceRefreshImageCache, trying direct fetch:', error);
                // Fallback to direct image fetching if force refresh fails
                try {
                    const { getRecentImagesFromChannel } = require('../../utils/imageUploader.js');
                    recentImages = await getRecentImagesFromChannel(interaction, { limit: 8, cacheMinutes: 2 });
                } catch (fallbackError) {
                    console.error('[owner-emojis] Error with direct image fetch fallback:', fallbackError);
                    recentImages = [];
                }
            }
        } else {
            // No interaction available, return empty array (can't fetch without interaction)
            console.warn('[owner-emojis] No interaction available for image fetching');
            recentImages = [];
        }

        const imageSelectRow = buildImageSelectMenu(
            recentImages,
            'emoji-image-select',
            'select image for emoji'
        );

        if (imageSelectRow) {
            components.push(imageSelectRow);
        } else {
            // Always show the "no images" menu so users can still upload
            components.push(buildNoImagesSelectMenu('emoji-image-select', 'select image for emoji'));
        }
    }

    // Action buttons (MOVED BACK TO 1ST COMPONENT)
    const actionButtons = [];

    // Create/Update button (COPIED FROM ITEMS SYSTEM: check for changes in edit mode)
    const hasRequiredFields = currentName && currentDescription && currentEmote;
    let buttonDisabled = false;

    if (isEditing) {
        // COPIED FROM ITEMS SYSTEM: Only enable if changes have been made
        const hasChanges = hasEmojiBeenModified(state, existingEmoji);
        buttonDisabled = !hasChanges;
    } else {
        // For creating: disable until required fields are filled
        buttonDisabled = !hasRequiredFields;
    }

    const actionButton = new ButtonBuilder()
        .setCustomId(isEditing ? 'emoji-update-final' : 'emoji-create-final')
        .setLabel(isEditing ? 'update emoji' : 'create emoji')
        .setStyle(ButtonStyle.Success)
        .setDisabled(buttonDisabled);
    actionButtons.push(actionButton);

    // Delete button (only for editing)
    if (isEditing) {
        const deleteButton = new ButtonBuilder()
            .setCustomId('emoji-delete')
            .setLabel('delete emoji')
            .setStyle(ButtonStyle.Danger);
        actionButtons.push(deleteButton);
    }

    if (actionButtons.length > 0) {
        const actionRow = new ActionRowBuilder().addComponents(...actionButtons);
        components.push(actionRow);
    }

    // Build main container
    const mainContainer = new ContainerBuilder()
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            mainContainer.addSectionComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            mainContainer.addActionRowComponents(component);
        } else {
            mainContainer.addTextDisplayComponents(component);
        }
    });

    // COPIED FROM ITEMS SYSTEM: Return both main container and preview container
    const containers = [mainContainer];

    // Only build preview container if name is set (like items system checks for selectedType)
    if (currentName) {
        const previewContainer = buildEmojiPreviewContainer({
            emojiName: currentName,
            emojiDescription: currentDescription,
            emojiEmote: currentEmote,
            pendingImageUrl: state.pendingImageUrl, // ENHANCED: Pass pending image URL
            isComplete: false
        });
        containers.push(previewContainer);

        // Action buttons now in 1st component, not separate
    }

    return containers;
}

/**
 * Helper function to determine edit mode and get existing emoji data
 * @param {Object} state - Current emoji state
 * @returns {Object} { isEditing, existingEmoji, mode }
 */
async function getEmojiEditContext(state) {
    if (!state.editingEmojiId) {
        return { isEditing: false, existingEmoji: null, mode: 'create' };
    }

    const existingEmoji = await optimizedFindOne('custom_emojis', { id: state.editingEmojiId });
    return {
        isEditing: true,
        existingEmoji: existingEmoji,
        mode: 'edit'
    };
}

/**
 * Check if current emoji state has been modified from original (COPIED FROM ITEMS SYSTEM)
 * @param {Object} currentState - Current emoji state
 * @param {Object} originalEmoji - Original emoji data
 * @returns {boolean} True if emoji has been modified
 */
function hasEmojiBeenModified(currentState, originalEmoji) {
    if (!originalEmoji || !currentState.editingEmojiId) {
        return true; // For new emojis, always allow creation
    }

    // Compare current state with original emoji data
    const hasNameChanged = currentState.emojiName !== originalEmoji.name;
    const hasDescriptionChanged = currentState.emojiDescription !== originalEmoji.description;
    const hasEmoteChanged = currentState.emojiEmote !== originalEmoji.emote;

    return hasNameChanged || hasDescriptionChanged || hasEmoteChanged;
}

/**
 * Build emoji preview container (COPIED FROM ITEMS SYSTEM)
 * @param {Object} options - Preview options
 * @param {string} options.emojiName - Emoji name
 * @param {string} options.emojiDescription - Emoji description
 * @param {string} options.emojiEmote - Emoji emote string
 * @param {boolean} options.isComplete - Whether configuration is complete
 * @returns {ContainerBuilder} The emoji preview container
 */
function buildEmojiPreviewContainer({ emojiName, emojiDescription, emojiEmote, pendingImageUrl = null, isComplete = false }) {
    const components = [];

    // Header with thumbnail - use emoji name if set
    let headerText = '# emoji preview';
    if (emojiName) {
        headerText = `# ${emojiName}`;
    }

    // Extract emote URL if it's a custom emote (ENHANCED: Handle pending images)
    let thumbnailUrl = null;
    if (emojiEmote && emojiEmote.includes(':')) {
        // Custom emote format: <:name:id> or <a:name:id>
        const emoteMatch = emojiEmote.match(/<a?:([^:]+):(\d+)>/);
        if (emoteMatch) {
            const [, emoteName, emoteId] = emoteMatch;
            const isAnimated = emojiEmote.startsWith('<a:');
            thumbnailUrl = `https://cdn.discordapp.com/emojis/${emoteId}.${isAnimated ? 'gif' : 'png'}`;
        }
    } else if (pendingImageUrl && !emojiEmote) {
        // ENHANCED: Use pending image URL if no Discord emoji created yet
        thumbnailUrl = pendingImageUrl;
    }

    // Quote - use emoji description if set
    let quoteText = '> your emoji will look like this';
    if (emojiDescription) {
        quoteText = `> ${emojiDescription}`;
    }

    // Create header section with optional thumbnail (COPIED FROM ITEMS SYSTEM)
    if (thumbnailUrl) {
        // Use section with thumbnail accessory when we have an emote
        const thumbnailComponent = new ThumbnailBuilder({
            media: { url: thumbnailUrl }
        });

        const heading = new TextDisplayBuilder().setContent(headerText);
        const quote = new TextDisplayBuilder().setContent(quoteText);
        const headerSection = new SectionBuilder()
            .setThumbnailAccessory(thumbnailComponent)
            .addTextDisplayComponents(heading, quote);
        components.push(headerSection);
    } else {
        // Use regular text display when no emote
        const heading = new TextDisplayBuilder().setContent(headerText);
        const quote = new TextDisplayBuilder().setContent(quoteText);
        components.push(heading, quote);
    }

    if (!emojiName) {
        const placeholderText = new TextDisplayBuilder().setContent('*Set a name to see preview.*');
        components.push(placeholderText);
    } else if (pendingImageUrl && !emojiEmote) {
        // ENHANCED: Show pending state when image is uploaded but Discord emoji not created yet
        const pendingText = new TextDisplayBuilder().setContent('*Discord emoji will be created when you save.*');
        components.push(pendingText);
    } else if (!emojiEmote && !pendingImageUrl) {
        // No image selected yet
        const noImageText = new TextDisplayBuilder().setContent('*Upload an image to see preview.*');
        components.push(noImageText);
    }
    // Removed Discord Name display - title is sufficient for owner-only feature

    // Build container (COPIED FROM ITEMS SYSTEM)
    const container = new ContainerBuilder()
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
            container.addTextDisplayComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        }
    });

    return container;
}

/**
 * Handle emoji select menu interactions
 */
async function emojiSelect(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        if (interaction.user.id !== process.env.OWNER) {
            const container = await buildCustomEmojisContainer({
                page: 0,
                client: interaction.client,
                showBackButton: true,
                statusMessage: 'who r u? no.'
            });
            return [container];
        }

        const selectedValue = interaction.values[0];

        if (selectedValue === 'add-new') {
            // Start emoji creation (ENHANCED: Clear all feature states to prevent modal data persistence)
            await clearAllFeatureStates(interaction.user.id, interaction.guild?.id);
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
            return containers;
        } else {
            // Show emoji preview/edit
            const emoji = await optimizedFindOne('custom_emojis', { id: selectedValue });
            if (!emoji) {
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Emoji not found'
                });
                return [container];
            }

            // Set up edit state with existing emoji data (COPIED FROM ITEMS SYSTEM)
            storeEmojiState(interaction.user.id, {
                editingEmojiId: emoji.id,
                emojiName: emoji.name,
                emojiDescription: emoji.description,
                emojiEmote: emoji.emote,
                emojiEmoteId: emoji.emoteId
            });

            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'edit', emoji, interaction);
            return containers;
        }
    }, {
        autoDefer: false,
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing emoji selection.'
    });
}

/**
 * Handle emoji configuration select menu interactions (COPIED FROM ITEMS SYSTEM)
 */
async function emojiConfigSelect(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        if (interaction.user.id !== process.env.OWNER) {
            const container = await buildCustomEmojisContainer({
                page: 0,
                client: interaction.client,
                showBackButton: true,
                statusMessage: 'who r u? no.'
            });
            return [container];
        }

        const selectedConfig = interaction.values[0];
        const state = getEmojiState(interaction.user.id) || {};

        if (selectedConfig === 'name') {
            // CLEAR currentConfig to hide image select menu when switching to name
            storeEmojiState(interaction.user.id, {
                ...state,
                currentConfig: null
            });

            // Show name input modal
            const modal = new ModalBuilder()
                .setCustomId('emoji-name-modal')
                .setTitle('Set Emoji Name');

            const nameInput = new TextInputBuilder()
                .setCustomId('emoji-name')
                .setLabel('Emoji Name (becomes Discord emoji name)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('e.g., my_custom_emoji')
                .setRequired(true)
                .setMaxLength(32);

            if (state.emojiName) {
                nameInput.setValue(state.emojiName);
            }

            const nameRow = new ActionRowBuilder().addComponents(nameInput);
            modal.addComponents(nameRow);

            await interaction.showModal(modal);
            return null; // Modal response
        } else if (selectedConfig === 'description') {
            // CLEAR currentConfig to hide image select menu when switching to description
            storeEmojiState(interaction.user.id, {
                ...state,
                currentConfig: null
            });

            // Show description input modal
            const modal = new ModalBuilder()
                .setCustomId('emoji-description-modal')
                .setTitle('Set Emoji Description');

            const descInput = new TextInputBuilder()
                .setCustomId('emoji-description')
                .setLabel('Emoji Description/Usage')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Describe what this emoji is for...')
                .setRequired(true)
                .setMaxLength(500);

            if (state.emojiDescription) {
                descInput.setValue(state.emojiDescription);
            }

            const descRow = new ActionRowBuilder().addComponents(descInput);
            modal.addComponents(descRow);

            await interaction.showModal(modal);
            return null; // Modal response
        } else if (selectedConfig === 'upload') {
            // PERFORMANCE FIX: Don't fetch images here, let the container building handle it
            // The image fetching will happen when the image select menu is actually shown

            // Set currentConfig to show cascading image select menu
            storeEmojiState(interaction.user.id, {
                ...state,
                currentConfig: 'upload'
            });

            // Rebuild editor container to show BOTH menus (main + cascading image select)
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
            return containers;
        }
    }, {
        autoDefer: false,
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing emoji configuration.'
    });
}

/**
 * Handle emoji image select menu interactions (COPIED FROM GLOBAL LEVELS SYSTEM)
 */
async function emojiImageSelect(interaction) {
    if (interaction.user.id !== process.env.OWNER) {
        const container = await buildCustomEmojisContainer({
            page: 0,
            client: interaction.client,
            showBackButton: true,
            statusMessage: 'who r u? no.'
        });
        return [container];
    }

    return handleUIOperation(interaction, async (interaction) => {
        // Handle "no-images" selection
        if (interaction.values[0] === 'no-images') {
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
            return containers;
        }

        const selectedImageIndex = parseInt(interaction.values[0].replace('image-', ''));

        // PERFORMANCE OPTIMIZATION: Use cached images (already refreshed in config select)
        const { getRecentImagesFromChannel } = require('../../utils/imageUploader.js');
        const recentImages = await getRecentImagesFromChannel(interaction, { limit: 8, cacheMinutes: 2 });
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** Selected image not found. Please try again.');
            containers[0].addTextDisplayComponents(statusDisplay);
            return containers;
        }

        // Get current emoji state
        const currentState = getEmojiState(interaction.user.id) || {};

        // FIXED: Don't create Discord emoji yet if no name is set
        // Store image URL and defer Discord emoji creation until name is provided
        if (!currentState.emojiName || currentState.emojiName.trim() === '') {
            // Store image URL for later Discord emoji creation
            storeEmojiState(interaction.user.id, {
                ...currentState,
                pendingImageUrl: selectedImage.url,
                currentConfig: null // Clear to hide image select menu
            });

            console.log(`[owner-emojis] 📝 Image stored, waiting for name before creating Discord emoji`);

            // Return success - no Discord emoji created yet, but image is stored
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** Image uploaded. Set a name to create Discord emoji.');
            containers[0].addTextDisplayComponents(statusDisplay);
            return containers;
        } else {
            // Name is already set, create Discord emoji immediately
            const emojiName = currentState.emojiName;

            try {
                // COPIED FROM GLOBAL LEVELS: Upload image as custom emote (application emote)
                const emoteData = await uploadImageAsEmote(
                    selectedImage.url,
                    emojiName,
                    interaction.guild.id,
                    interaction.client,
                    { useApplicationEmote: true }
                );

                // Store the emote data in state
                storeEmojiState(interaction.user.id, {
                    ...currentState,
                    emojiEmote: emoteData.string,
                    emojiEmoteId: emoteData.id,
                    pendingImageUrl: null, // Clear pending image
                    currentConfig: null // Clear to hide image select menu
                });

                // ENHANCED: Track emoji usage with emojiCleanup system
                const { trackEmojiUsage } = require('../../utils/emojiCleanup.js');
                trackEmojiUsage(emoteData.id, 'custom_emoji_creation', interaction.user.id);

                // Return success
                const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** Discord emoji created successfully.');
                containers[0].addTextDisplayComponents(statusDisplay);
                return containers;
            } catch (error) {
                console.error('[owner-emojis] Error creating Discord emoji:', error);
                const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'create', null, interaction);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** Failed to create Discord emoji. Please try again.');
                containers[0].addTextDisplayComponents(statusDisplay);
                return containers;
            }
        }
    }, {
        autoDefer: false,
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing image selection.'
    });
}

/**
 * Handle emoji button interactions
 */
async function buttons(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        if (interaction.user.id !== process.env.OWNER) {
            const container = await buildCustomEmojisContainer({
                page: 0,
                client: interaction.client,
                showBackButton: true,
                statusMessage: 'who r u? no.'
            });
            return [container];
        }

        if (interaction.customId === 'custom-emojis-back') {
            // Return to main emoji list
            clearEmojiState(interaction.user.id);
            const container = await buildCustomEmojisContainer({
                page: 0,
                client: interaction.client,
                showBackButton: true
            });
            return [container];
        } else if (interaction.customId.startsWith('custom-emojis-page-')) {
            // Handle pagination
            const page = parseInt(interaction.customId.split('-').pop());
            const container = await buildCustomEmojisContainer({
                page: page,
                client: interaction.client,
                showBackButton: true
            });
            return [container];
        } else if (interaction.customId.startsWith('emoji-edit-')) {
            // Start editing emoji
            const emojiId = interaction.customId.replace('emoji-edit-', '');
            const emoji = await optimizedFindOne('custom_emojis', { id: emojiId });

            if (!emoji) {
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Emoji not found'
                });
                return [container];
            }

            // Set up editing state
            storeEmojiState(interaction.user.id, {
                editingEmojiId: emoji.id,
                emojiName: emoji.name,
                emojiDescription: emoji.description,
                emojiEmote: emoji.emote,
                emojiEmoteId: emoji.emoteId
            });

            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, 'edit', emoji, interaction);
            return containers;
        } else if (interaction.customId === 'emoji-create-final') {
            // Create new emoji
            const state = getEmojiState(interaction.user.id);
            if (!state || !state.emojiName || !state.emojiDescription || !state.emojiEmote) {
                // Return to main emoji list with error message (COPIED FROM ITEMS SYSTEM)
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Please fill in all required fields'
                });
                return [container];
            }

            try {
                const emojiData = {
                    name: state.emojiName,
                    description: state.emojiDescription,
                    emote: state.emojiEmote,
                    emoteId: state.emojiEmoteId,
                    createdBy: interaction.user.id
                };

                await createEmoji(emojiData);
                clearEmojiState(interaction.user.id);

                // ENHANCED: Mark emoji as permanently saved in cleanup system
                const { markEmojiAsSaved } = require('../../utils/emojiCleanup.js');
                markEmojiAsSaved(emojiData.emoteId);

                // COPIED FROM GLOBAL LEVELS: Invalidate image cache after creation
                const { invalidateImageCache } = require('../../utils/imageUploader.js');
                await invalidateImageCache(interaction.user.id, interaction.guild.id);

                // ENHANCED: Invalidate custom emoji cache for Starfall integration
                const { invalidateCustomEmojiCache } = require('../../utils/customEmojiIntegration.js');
                invalidateCustomEmojiCache(emojiData.name);

                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Emoji created successfully'
                });
                return [container];
            } catch (error) {
                console.error('[owner-emojis] Error creating emoji:', error);
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: `Creation failed: ${error.message}`
                });
                return [container];
            }
        } else if (interaction.customId === 'emoji-update-final') {
            // Update existing emoji
            const state = getEmojiState(interaction.user.id);
            if (!state || !state.editingEmojiId || !state.emojiName || !state.emojiDescription || !state.emojiEmote) {
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Please fill in all required fields'
                });
                return [container];
            }

            try {
                // Get original emoji data to check for changes
                const originalEmoji = await optimizedFindOne('custom_emojis', { id: state.editingEmojiId });

                // ENHANCED: Handle Discord emoji name changes (requires recreation)
                let finalEmote = state.emojiEmote;
                let finalEmoteId = state.emojiEmoteId;

                if (originalEmoji && originalEmoji.name !== state.emojiName && originalEmoji.emoteId) {
                    try {
                        // Name changed - need to recreate Discord emoji with new name
                        console.log(`[owner-emojis] 🔄 Emoji name changed from '${originalEmoji.name}' to '${state.emojiName}' - recreating Discord emoji`);

                        // Get image URL from Discord before deleting
                        const oldEmojiData = await interaction.client.application.emojis.fetch(originalEmoji.emoteId).catch(() => null);
                        if (oldEmojiData) {
                            const imageUrl = `https://cdn.discordapp.com/emojis/${originalEmoji.emoteId}.${oldEmojiData.animated ? 'gif' : 'png'}`;

                            // Delete old Discord emoji
                            await interaction.client.application.emojis.delete(originalEmoji.emoteId);
                            console.log(`[owner-emojis] 🗑️ Deleted old Discord emoji: ${originalEmoji.name} (${originalEmoji.emoteId})`);

                            // Create new Discord emoji with new name
                            const { uploadImageAsEmote } = require('../../utils/imageUploader.js');
                            const newEmoteData = await uploadImageAsEmote(
                                imageUrl,
                                state.emojiName,
                                interaction.guild.id,
                                interaction.client,
                                { useApplicationEmote: true }
                            );

                            finalEmote = newEmoteData.string;
                            finalEmoteId = newEmoteData.id;

                            console.log(`[owner-emojis] ✅ Created new Discord emoji: ${state.emojiName} (${newEmoteData.id})`);

                            // Update emoji cleanup tracking
                            const { removeEmojiFromTracker, markEmojiAsSaved } = require('../../utils/emojiCleanup.js');
                            removeEmojiFromTracker(originalEmoji.emoteId);
                            markEmojiAsSaved(newEmoteData.id);
                        } else {
                            console.warn(`[owner-emojis] ⚠️ Could not fetch old emoji data for ${originalEmoji.emoteId}, skipping recreation`);
                        }
                    } catch (error) {
                        console.error('[owner-emojis] Error recreating Discord emoji with new name:', error);
                        // Continue with database update even if Discord recreation fails
                    }
                }

                await optimizedUpdateOne('custom_emojis',
                    { id: state.editingEmojiId },
                    {
                        $set: {
                            name: state.emojiName,
                            description: state.emojiDescription,
                            emote: finalEmote,
                            emoteId: finalEmoteId,
                            updatedAt: new Date()
                        }
                    }
                );

                // ENHANCED: Handle emoji cleanup integration for other updates
                if (originalEmoji && originalEmoji.emoteId !== finalEmoteId && originalEmoji.name === state.emojiName) {
                    // Emoji image was changed (but not name) - remove old emoji from tracker and mark new one as saved
                    const { removeEmojiFromTracker, markEmojiAsSaved } = require('../../utils/emojiCleanup.js');
                    removeEmojiFromTracker(originalEmoji.emoteId);
                    markEmojiAsSaved(finalEmoteId);
                    console.log(`[owner-emojis] 🔄 Updated emoji tracking: removed ${originalEmoji.emoteId}, saved ${finalEmoteId}`);
                } else if (finalEmoteId && originalEmoji && originalEmoji.emoteId === finalEmoteId) {
                    // Same emoji, just ensure it's marked as saved
                    const { markEmojiAsSaved } = require('../../utils/emojiCleanup.js');
                    markEmojiAsSaved(finalEmoteId);
                }

                clearEmojiState(interaction.user.id);

                // COPIED FROM GLOBAL LEVELS: Invalidate image cache after update
                const { invalidateImageCache } = require('../../utils/imageUploader.js');
                await invalidateImageCache(interaction.user.id, interaction.guild.id);

                // ENHANCED: Invalidate custom emoji cache for Starfall integration
                const { invalidateCustomEmojiCache } = require('../../utils/customEmojiIntegration.js');
                invalidateCustomEmojiCache(); // Clear all cache since name might have changed

                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Emoji updated successfully'
                });
                return [container];
            } catch (error) {
                console.error('[owner-emojis] Error updating emoji:', error);
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: `Update failed: ${error.message}`
                });
                return [container];
            }
        } else if (interaction.customId === 'emoji-delete') {
            // Delete emoji
            const state = getEmojiState(interaction.user.id);
            if (!state || !state.editingEmojiId) {
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'No emoji selected for deletion'
                });
                return [container];
            }

            try {
                // Get emoji data before deletion for cleanup integration
                const emoji = await optimizedFindOne('custom_emojis', { id: state.editingEmojiId });

                // ENHANCED: Integrate with emojiCleanup system for proper Discord emoji management
                if (emoji && emoji.emoteId) {
                    try {
                        // Method 1: Direct deletion (immediate)
                        await interaction.client.application.emojis.delete(emoji.emoteId);
                        console.log(`[owner-emojis] ✅ Deleted Discord application emoji: ${emoji.name} (${emoji.emoteId})`);

                        // ENHANCED: Remove from emoji cleanup tracker using proper API
                        const { removeEmojiFromTracker } = require('../../utils/emojiCleanup.js');
                        removeEmojiFromTracker(emoji.emoteId);
                    } catch (discordError) {
                        console.warn(`[owner-emojis] ⚠️ Could not delete Discord emoji ${emoji.emoteId}:`, discordError.message);
                        // Continue with database deletion even if Discord deletion fails
                    }
                }

                await optimizedDeleteOne('custom_emojis', { id: state.editingEmojiId });
                emojiMetrics.emojisDeleted++;
                clearEmojiState(interaction.user.id);

                // COPIED FROM GLOBAL LEVELS: Invalidate image cache after deletion
                const { invalidateImageCache } = require('../../utils/imageUploader.js');
                await invalidateImageCache(interaction.user.id, interaction.guild.id);

                // ENHANCED: Invalidate custom emoji cache for Starfall integration
                const { invalidateCustomEmojiCache } = require('../../utils/customEmojiIntegration.js');
                invalidateCustomEmojiCache(); // Clear all cache since emoji was deleted

                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: 'Emoji deleted successfully'
                });
                return [container];
            } catch (error) {
                console.error('[owner-emojis] Error deleting emoji:', error);
                const container = await buildCustomEmojisContainer({
                    page: 0,
                    client: interaction.client,
                    showBackButton: true,
                    statusMessage: `Deletion failed: ${error.message}`
                });
                return [container];
            }
        }
    }, {
        autoDefer: false,
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing emoji button interaction.'
    });
}

/**
 * Handle emoji modal submissions
 */
async function modalSubmit(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        if (interaction.user.id !== process.env.OWNER) {
            const container = await buildCustomEmojisContainer({
                page: 0,
                client: interaction.client,
                showBackButton: true,
                statusMessage: 'who r u? no.'
            });
            return [container];
        }

        if (interaction.customId === 'emoji-name-modal') {
            // Handle emoji name input
            const emojiName = interaction.fields.getTextInputValue('emoji-name');
            const state = getEmojiState(interaction.user.id) || {};

            // Validate emoji name (Discord emoji naming rules)
            if (!/^[a-zA-Z0-9_]+$/.test(emojiName)) {
                // Determine correct mode and existing emoji
                const { mode, existingEmoji } = await getEmojiEditContext(state);
                const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, mode, existingEmoji, interaction);
                // Add status message to first container (COPIED FROM ITEMS SYSTEM)
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** Emoji name can only contain letters, numbers, and underscores');
                containers[0].addTextDisplayComponents(statusDisplay);
                return containers;
            }

            // ENHANCED: Create Discord emoji if there's a pending image URL
            let updatedState = { ...state, emojiName: emojiName };
            let statusMessage = 'Emoji name updated';

            if (state.pendingImageUrl && !state.emojiEmote) {
                try {
                    // Create Discord emoji now that we have both name and image
                    const { uploadImageAsEmote } = require('../../utils/imageUploader.js');
                    const emoteData = await uploadImageAsEmote(
                        state.pendingImageUrl,
                        emojiName,
                        interaction.guild.id,
                        interaction.client,
                        { useApplicationEmote: true }
                    );

                    // Update state with Discord emoji data
                    updatedState = {
                        ...updatedState,
                        emojiEmote: emoteData.string,
                        emojiEmoteId: emoteData.id,
                        pendingImageUrl: null // Clear pending image
                    };

                    // Track emoji usage with emojiCleanup system
                    const { trackEmojiUsage } = require('../../utils/emojiCleanup.js');
                    trackEmojiUsage(emoteData.id, 'custom_emoji_creation', interaction.user.id);

                    statusMessage = 'Emoji name set and Discord emoji created';
                    console.log(`[owner-emojis] ✅ Created Discord emoji with proper name: ${emojiName} (${emoteData.id})`);
                } catch (error) {
                    console.error('[owner-emojis] Error creating Discord emoji with name:', error);
                    statusMessage = 'Emoji name updated, but failed to create Discord emoji';
                }
            }

            storeEmojiState(interaction.user.id, updatedState);

            // Determine correct mode and existing emoji
            const { mode, existingEmoji } = await getEmojiEditContext(updatedState);
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, mode, existingEmoji, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
            containers[0].addTextDisplayComponents(statusDisplay);
            return containers;
        } else if (interaction.customId === 'emoji-description-modal') {
            // Handle emoji description input
            const emojiDescription = interaction.fields.getTextInputValue('emoji-description');
            const state = getEmojiState(interaction.user.id) || {};

            storeEmojiState(interaction.user.id, {
                ...state,
                emojiDescription: emojiDescription
            });

            // Determine correct mode and existing emoji
            const { mode, existingEmoji } = await getEmojiEditContext({ ...state, emojiDescription: emojiDescription });
            const containers = await buildEmojiEditorContainer(interaction.user.id, interaction.client, mode, existingEmoji, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** Emoji description updated');
            containers[0].addTextDisplayComponents(statusDisplay);
            return containers;
        }
    }, {
        autoDefer: false,
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing emoji modal submission.'
    });
}

module.exports = {
    buildCustomEmojisContainer,
    buildEmojiEditorContainer,
    buildEmojiPreviewContainer,
    createEmoji,
    getEmojisForDisplay,
    storeEmojiState,
    getEmojiState,
    clearEmojiState,

    // Universal Interaction Manager handlers
    selectMenuSubmit: emojiSelect,
    buttonSubmit: buttons,
    modalSubmit,

    // Legacy exports for backward compatibility
    emojiSelect,
    emojiConfigSelect,
    emojiImageSelect,
    buttons,
    emojiMetrics
};
