/**
 * Performance Monitoring Dashboard
 * Provides real-time performance metrics and optimization insights
 */

const { getGlobalCacheStats } = require('./LRUCache.js');
const { getDbPerformanceStats } = require('./database-optimizer.js');
const { getConnectionStats } = require('../mongo/client.js');

// Performance metrics aggregation
const performanceMetrics = {
    commandResponseTimes: new Map(),
    interactionCounts: new Map(),
    errorRates: new Map(),
    cacheEfficiency: new Map(),
    databasePerformance: new Map(),
    lastReset: Date.now(),
    monitoringEnabled: process.env.NODE_ENV === 'development'
};

/**
 * Track command performance
 * @param {string} commandName - Name of the command
 * @param {number} duration - Execution duration in ms
 * @param {boolean} success - Whether the command succeeded
 */
function trackCommandPerformance(commandName, duration, success = true) {
    if (!performanceMetrics.monitoringEnabled) return;
    
    if (!performanceMetrics.commandResponseTimes.has(commandName)) {
        performanceMetrics.commandResponseTimes.set(commandName, {
            count: 0,
            totalDuration: 0,
            avgDuration: 0,
            maxDuration: 0,
            minDuration: Infinity,
            successCount: 0,
            errorCount: 0
        });
    }
    
    const stats = performanceMetrics.commandResponseTimes.get(commandName);
    stats.count++;
    stats.totalDuration += duration;
    stats.avgDuration = stats.totalDuration / stats.count;
    stats.maxDuration = Math.max(stats.maxDuration, duration);
    stats.minDuration = Math.min(stats.minDuration, duration);
    
    if (success) {
        stats.successCount++;
    } else {
        stats.errorCount++;
    }
}

/**
 * Get comprehensive performance report
 * @returns {Object} Performance report
 */
async function getPerformanceReport() {
    const cacheStats = getGlobalCacheStats();
    const dbStats = getDbPerformanceStats();
    const connectionStats = getConnectionStats();
    
    // Calculate overall metrics
    const totalCommands = Array.from(performanceMetrics.commandResponseTimes.values())
        .reduce((sum, stats) => sum + stats.count, 0);
    
    const avgResponseTime = Array.from(performanceMetrics.commandResponseTimes.values())
        .reduce((sum, stats) => sum + stats.avgDuration, 0) / performanceMetrics.commandResponseTimes.size || 0;
    
    const totalErrors = Array.from(performanceMetrics.commandResponseTimes.values())
        .reduce((sum, stats) => sum + stats.errorCount, 0);
    
    const errorRate = totalCommands > 0 ? (totalErrors / totalCommands * 100).toFixed(2) : 0;
    
    return {
        overview: {
            totalCommands,
            avgResponseTime: Math.round(avgResponseTime),
            errorRate: `${errorRate}%`,
            uptime: Math.round((Date.now() - performanceMetrics.lastReset) / 1000 / 60), // minutes
            monitoringEnabled: performanceMetrics.monitoringEnabled
        },
        commands: Object.fromEntries(
            Array.from(performanceMetrics.commandResponseTimes.entries())
                .sort(([,a], [,b]) => b.count - a.count) // Sort by usage
                .slice(0, 10) // Top 10 commands
                .map(([name, stats]) => [name, {
                    ...stats,
                    avgDuration: Math.round(stats.avgDuration),
                    successRate: `${((stats.successCount / stats.count) * 100).toFixed(1)}%`
                }])
        ),
        cache: cacheStats,
        database: {
            ...dbStats,
            connections: connectionStats
        },
        recommendations: generatePerformanceRecommendations(avgResponseTime, errorRate, cacheStats)
    };
}

/**
 * Generate performance optimization recommendations
 * @param {number} avgResponseTime - Average response time
 * @param {number} errorRate - Error rate percentage
 * @param {Object} cacheStats - Cache statistics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(avgResponseTime, errorRate, cacheStats) {
    const recommendations = [];
    
    if (avgResponseTime > 1000) {
        recommendations.push({
            priority: 'HIGH',
            issue: 'Slow Response Times',
            suggestion: 'Consider enabling more aggressive caching or optimizing database queries',
            metric: `${Math.round(avgResponseTime)}ms average`
        });
    }
    
    if (errorRate > 5) {
        recommendations.push({
            priority: 'HIGH',
            issue: 'High Error Rate',
            suggestion: 'Review error handling and add more robust fallbacks',
            metric: `${errorRate}% error rate`
        });
    }
    
    if (cacheStats.totalHitRate < 80) {
        recommendations.push({
            priority: 'MEDIUM',
            issue: 'Low Cache Hit Rate',
            suggestion: 'Increase cache sizes or TTL values for better performance',
            metric: `${cacheStats.totalHitRate}% hit rate`
        });
    }
    
    if (avgResponseTime < 500 && errorRate < 2 && cacheStats.totalHitRate > 90) {
        recommendations.push({
            priority: 'INFO',
            issue: 'Excellent Performance',
            suggestion: 'System is performing optimally',
            metric: 'All metrics within target ranges'
        });
    }
    
    return recommendations;
}

/**
 * Reset performance metrics
 */
function resetPerformanceMetrics() {
    performanceMetrics.commandResponseTimes.clear();
    performanceMetrics.interactionCounts.clear();
    performanceMetrics.errorRates.clear();
    performanceMetrics.cacheEfficiency.clear();
    performanceMetrics.databasePerformance.clear();
    performanceMetrics.lastReset = Date.now();
}

/**
 * Enable/disable performance monitoring
 * @param {boolean} enabled - Whether to enable monitoring
 */
function setMonitoringEnabled(enabled) {
    performanceMetrics.monitoringEnabled = enabled;
}

/**
 * Get quick performance summary for logging
 * @returns {string} Performance summary
 */
function getQuickSummary() {
    const totalCommands = Array.from(performanceMetrics.commandResponseTimes.values())
        .reduce((sum, stats) => sum + stats.count, 0);
    
    const avgResponseTime = Array.from(performanceMetrics.commandResponseTimes.values())
        .reduce((sum, stats) => sum + stats.avgDuration, 0) / performanceMetrics.commandResponseTimes.size || 0;
    
    return `Commands: ${totalCommands}, Avg Response: ${Math.round(avgResponseTime)}ms`;
}

// Auto-report performance every 10 minutes in development
if (process.env.NODE_ENV === 'development') {
    setInterval(async () => {
        const report = await getPerformanceReport();
        if (report.overview.totalCommands > 0) {
            console.log(`[Performance] ${getQuickSummary()}`);
            
            // Log high-priority recommendations
            const highPriorityRecs = report.recommendations.filter(r => r.priority === 'HIGH');
            if (highPriorityRecs.length > 0) {
                console.warn('[Performance] High priority recommendations:', 
                    highPriorityRecs.map(r => `${r.issue}: ${r.suggestion}`).join(', '));
            }
        }
    }, 10 * 60 * 1000); // 10 minutes
}

module.exports = {
    trackCommandPerformance,
    getPerformanceReport,
    resetPerformanceMetrics,
    setMonitoringEnabled,
    getQuickSummary,
    generatePerformanceRecommendations
};
