# 🧪 Comprehensive Testing Guide

## Overview
This guide provides complete testing procedures for all bot features, using the shared `BotTestBase` infrastructure for consistent, reliable testing across all systems.

## Testing Architecture

### Shared Test Base
All tests extend `BotTestBase` which provides:
- Discord client initialization and login
- MongoDB connection with live database
- Mock interaction creation with Components v2 validation
- Test environment setup and cleanup
- Result tracking and reporting

### Test Categories
1. **Unit Tests** - Individual function and component testing
2. **Integration Tests** - Cross-system functionality validation
3. **Performance Tests** - Response time and scalability validation
4. **Security Tests** - Permission boundaries and access control
5. **End-to-End Tests** - Complete user workflow validation

## Feature-Specific Testing

### EXP System Testing

#### Test Suite: `test_exp_system_focused.js`
```javascript
class ExpSystemTester extends BotTestBase {
    constructor() {
        super('ExpSystemTest');
    }

    async testLevelCreation() {
        // Test level creation workflow
        const selectInteraction = this.createMockInteraction(5, 'exp-levels-select', ['create']);
        await exp.select(selectInteraction, []);
        
        const buttonInteraction = this.createMockInteraction(3, 'exp-create-level-exp');
        await exp.buttons(buttonInteraction);
        
        return selectInteraction._responses.length > 0 && buttonInteraction._responses.length > 0;
    }

    async testModalValidation() {
        // Test form validation
        const modalInteraction = this.createMockInteraction(6, 'text-exp-per-min-modal', {
            'text-exp-per-min-input': '5'
        });
        
        await exp.modalSubmit(modalInteraction);
        return modalInteraction._responses.length > 0;
    }

    async testPermissionBoundaries() {
        // Test admin vs user access
        const adminInteraction = this.createMockInteraction(5, 'exp-levels-select', ['create']);
        adminInteraction.member = { permissions: { has: () => true } };
        
        const userInteraction = this.createMockInteraction(5, 'exp-levels-select', ['view']);
        userInteraction.member = { permissions: { has: () => false } };
        
        await exp.select(adminInteraction, []);
        await exp.select(userInteraction, []);
        
        return adminInteraction._responses.length > 0;
    }
}
```

#### Key Test Areas
- **Level Management**: Creation, editing, deletion workflows
- **Modal Forms**: Input validation and error handling
- **Permission System**: Admin vs user access boundaries
- **Cache Integration**: Performance and data consistency
- **Demo Data**: Fallback behavior when no levels exist

### Items System Testing

#### Test Suite: `test_items_system_focused.js`
```javascript
class ItemsSystemTester extends BotTestBase {
    async testItemCreation() {
        // Test multi-step creation workflow
        const typeInteraction = this.createMockInteraction(5, 'items-create-type-select', ['weapon']);
        await items.select(typeInteraction, []);
        
        const modalInteraction = this.createMockInteraction(6, 'items-create-modal', {
            'item-name': 'Test Sword',
            'item-description': 'A test weapon',
            'item-parameter1': '100'
        });
        
        await items.modalSubmit(modalInteraction);
        return typeInteraction._responses.length > 0;
    }

    async testDropMechanics() {
        // Test item drop system
        const { rollForItemDrop } = require('../utils/itemDrops.js');
        
        const dropResult = await rollForItemDrop(
            this.testUser.id,
            this.testGuild.id,
            'TEXT',
            1.0 // Force drop for testing
        );
        
        console.log(`   Drop test result: ${dropResult ? 'Success' : 'No drop'}`);
        return true;
    }

    async testRaritySystem() {
        // Validate rarity structure
        const { RARITIES } = require('../commands/utility/items.js');
        
        for (const [rarityName, rarityData] of Object.entries(RARITIES)) {
            if (!rarityData.weight || !rarityData.color || !rarityData.emoji) {
                throw new Error(`Rarity ${rarityName} missing required fields`);
            }
        }
        
        return true;
    }
}
```

#### Key Test Areas
- **Item Creation**: Multi-step workflow with type and icon selection
- **Drop System**: Probability calculations and weighted selection
- **Inventory Management**: User item collections and quantities
- **Rarity System**: Weight-based distribution validation
- **Image Upload**: Integration with unified uploader system

### Owner Administration Testing

#### Test Suite: `test_owner_administration.js`
```javascript
class OwnerAdministrationTester extends BotTestBase {
    async testOwnerPermissions() {
        // Test owner access
        const ownerInteraction = this.createMockInteraction(5, 'owner-features', ['servers']);
        ownerInteraction.user.id = process.env.OWNER;
        
        const result = await owner.select(ownerInteraction, []);
        console.log(`   Owner access responses: ${ownerInteraction._responses.length}`);
        
        // Test non-owner rejection
        const userInteraction = this.createMockInteraction(5, 'owner-features', ['servers']);
        userInteraction.user.id = 'regular-user-id';
        
        await owner.select(userInteraction, []);
        console.log(`   Non-owner responses: ${userInteraction._responses.length}`);
        
        return ownerInteraction._responses.length > 0;
    }

    async testServerManagement() {
        const { buildServersContainer } = require('../commands/utility/owner-servers.js');
        
        const container = await buildServersContainer(this.client);
        console.log(`   Server management container built: ${!!container}`);
        
        return !!container;
    }

    async testStatusManagement() {
        const ownerStatus = require('../commands/utility/owner-status.js');
        const interaction = this.createMockInteraction(5, 'owner-status-select', ['view']);
        
        await ownerStatus.handleStatusSelect(interaction);
        return interaction._responses.length >= 0; // May be 0 for status updates
    }
}
```

#### Key Test Areas
- **Permission Validation**: Multi-layer owner verification
- **Server Management**: Guild statistics and administration
- **Status Control**: Bot presence and activity management
- **Global Systems**: Cross-server data management
- **Security**: Unauthorized access prevention

### User Profile System Testing

#### Test Suite: `test_you_command_system.js`
```javascript
class YouCommandTester extends BotTestBase {
    async testProfileDisplay() {
        const you = require('../commands/utility/you.js');
        const interaction = this.createMockInteraction(2, 'you');
        interaction.options = {
            getString: () => null,
            getUser: () => null
        };
        
        await you.execute(interaction);
        
        console.log(`   Profile responses: ${interaction._responses.length}`);
        return interaction._responses.length > 0;
    }

    async testSectionNavigation() {
        const you = require('../commands/utility/you.js');
        const sections = ['exp', 'items', 'starfall', 'notifications'];
        
        let successCount = 0;
        for (const section of sections) {
            const interaction = this.createMockInteraction(5, 'you-hub-select', [section]);
            
            try {
                await you.select(interaction, []);
                if (interaction._responses.length > 0) {
                    successCount++;
                }
                console.log(`   ${section} section: ${interaction._responses.length} responses`);
            } catch (error) {
                console.log(`   ${section} section error: ${error.message}`);
            }
        }
        
        return successCount > 0;
    }

    async testTranscriptionSystem() {
        // Test transcription if Python is available
        try {
            const { execSync } = require('child_process');
            execSync('python --version', { stdio: 'ignore' });
            
            console.log(`   Python available for transcription testing`);
            
            // Test transcription handler
            const transcribeVoice = require('../commands/utility/transcribe-voice.js');
            // Note: Would need actual audio file for full test
            
            return true;
        } catch (error) {
            console.log(`   Python not available - skipping transcription test`);
            return true; // Not a failure, just unavailable
        }
    }
}
```

#### Key Test Areas
- **Profile Data Integration**: Multi-system data aggregation
- **Section Navigation**: Hub-based interface navigation
- **Transcription Features**: Voice message processing
- **Settings Management**: User preference persistence
- **Performance**: Response time optimization

### Utility Features Testing

#### Test Suite: `test_utility_features.js`
```javascript
class UtilityFeaturesTester extends BotTestBase {
    async testLogsSystem() {
        const logs = require('../commands/utility/logs.js');
        const interaction = this.createMockInteraction(2, 'logs');
        
        await logs.execute(interaction);
        return interaction._responses.length > 0;
    }

    async testStickySystem() {
        const sticky = require('../commands/utility/sticky.js');
        const interaction = this.createMockInteraction(5, 'sticky-select', ['roles']);
        
        try {
            await sticky.select(interaction, []);
            return interaction._responses.length > 0;
        } catch (error) {
            console.log(`   Sticky system error (may be expected): ${error.message}`);
            return true; // Some errors expected in test environment
        }
    }

    async testDehoistSystem() {
        const dehoist = require('../commands/utility/dehoist.js');
        const interaction = this.createMockInteraction(2, 'dehoist');
        
        await dehoist.execute(interaction);
        return interaction._responses.length > 0;
    }

    async testOpenerSystem() {
        const opener = require('../commands/utility/opener.js');
        const interaction = this.createMockInteraction(2, 'opener');
        
        await opener.execute(interaction);
        return interaction._responses.length > 0;
    }
}
```

#### Key Test Areas
- **Logs Configuration**: Event type setup and channel routing
- **Sticky Persistence**: Nickname and role recovery
- **Dehoist Operations**: Username character filtering
- **Thread Management**: Auto-bump and monitoring systems

### Specialized Systems Testing

#### Test Suite: `test_specialized_systems.js`
```javascript
class SpecializedSystemsTester extends BotTestBase {
    async testStarfallSystem() {
        const { calculateStarfallReward } = require('../utils/starfall.js');
        
        try {
            const reward = await calculateStarfallReward(this.testUser.id);
            console.log(`   Starfall reward calculation: ${reward !== null ? 'Success' : 'No reward available'}`);
            return true;
        } catch (error) {
            console.log(`   Starfall error: ${error.message}`);
            return false;
        }
    }

    async testGlobalLevels() {
        const { getGlobalLevelData } = require('../utils/globalLevels.js');
        
        try {
            const globalData = await getGlobalLevelData(this.testUser.id);
            console.log(`   Global levels data retrieved: ${!!globalData}`);
            return true;
        } catch (error) {
            console.log(`   Global levels error: ${error.message}`);
            return false;
        }
    }

    async testWhisperModels() {
        const whisperModels = require('../commands/utility/whisperModels.js');
        const interaction = this.createMockInteraction(2, 'whisper-models');
        
        try {
            await whisperModels.execute(interaction);
            return interaction._responses.length > 0;
        } catch (error) {
            console.log(`   Whisper models error: ${error.message}`);
            return true; // May not be available in test environment
        }
    }
}
```

#### Key Test Areas
- **Starfall Rewards**: Daily reward calculation and timezone handling
- **Global Levels**: Cross-server EXP tracking and leaderboards
- **Transcription**: Voice message processing and AI integration
- **Model Management**: Whisper AI model administration

## Performance Testing

### Response Time Benchmarks
```javascript
async function testPerformanceBenchmarks() {
    const benchmarks = [
        { name: '/17 command', target: 2000, test: () => test17Command() },
        { name: '/you command', target: 2000, test: () => testYouCommand() },
        { name: 'EXP interface', target: 500, test: () => testExpInterface() },
        { name: 'Items interface', target: 500, test: () => testItemsInterface() }
    ];

    for (const benchmark of benchmarks) {
        const startTime = Date.now();
        await benchmark.test();
        const duration = Date.now() - startTime;
        
        const status = duration <= benchmark.target ? '✅ PASS' : '⚠️ SLOW';
        console.log(`${status} ${benchmark.name}: ${duration}ms (target: ${benchmark.target}ms)`);
    }
}
```

### Concurrent User Testing
```javascript
async function testConcurrentUsers() {
    const concurrentTests = [];
    const userCount = 10;
    
    for (let i = 0; i < userCount; i++) {
        concurrentTests.push(simulateUserInteraction(i));
    }
    
    const results = await Promise.allSettled(concurrentTests);
    const successCount = results.filter(r => r.status === 'fulfilled').length;
    
    console.log(`Concurrent user test: ${successCount}/${userCount} successful`);
    return successCount === userCount;
}
```

## Security Testing

### Permission Boundary Validation
```javascript
async function testPermissionBoundaries() {
    const securityTests = [
        { name: 'Owner-only access', test: () => testOwnerOnlyAccess() },
        { name: 'Admin permission checks', test: () => testAdminPermissions() },
        { name: 'User data isolation', test: () => testUserDataIsolation() },
        { name: 'Rate limiting', test: () => testRateLimiting() }
    ];

    for (const test of securityTests) {
        try {
            const result = await test.test();
            console.log(`✅ ${test.name}: ${result ? 'SECURE' : 'VULNERABLE'}`);
        } catch (error) {
            console.log(`❌ ${test.name}: ERROR - ${error.message}`);
        }
    }
}
```

## Test Execution

### Running All Tests
```bash
# Run comprehensive test suite
npm test

# Run feature-specific tests
npm run test:exp
npm run test:items

# Run performance tests
npm run test:performance

# Run security tests
npm run test:security
```

### Test Environment Setup
```javascript
// Required environment variables
const REQUIRED_ENV = [
    'TOKEN',        // Discord bot token
    'MONGO',        // MongoDB connection string
    'OWNER',        // Bot owner user ID
    'GUILDIDTWO'    // Test guild ID
];

function validateTestEnvironment() {
    const missing = REQUIRED_ENV.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
    
    console.log('✅ Test environment validated');
}
```

## Continuous Integration

### GitHub Actions Example
```yaml
name: Bot Testing
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm install
      
      - name: Run comprehensive tests
        run: npm test
        env:
          TOKEN: ${{ secrets.DISCORD_TOKEN }}
          MONGO: ${{ secrets.MONGO_URI }}
          OWNER: ${{ secrets.OWNER_ID }}
          GUILDIDTWO: ${{ secrets.TEST_GUILD_ID }}
      
      - name: Run performance tests
        run: npm run test:performance
      
      - name: Run security tests
        run: npm run test:security
```

## Maintenance and Monitoring

### Regular Testing Schedule
- **Daily**: Automated test suite execution
- **Weekly**: Performance benchmark validation
- **Monthly**: Security audit and penetration testing
- **Release**: Full regression testing before deployment

### Test Result Analysis
- Monitor test success rates and failure patterns
- Track performance degradation over time
- Identify frequently failing tests for improvement
- Maintain test coverage metrics

### Documentation Updates
- Keep test examples current with implementation changes
- Document new testing patterns and best practices
- Update performance benchmarks as features evolve
- Maintain security testing procedures
