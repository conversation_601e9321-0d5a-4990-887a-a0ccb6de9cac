const {
    Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder,
    ActionRowBuilder, ChannelSelectMenuBuilder, ButtonBuilder, ButtonStyle,
    SeparatorBuilder, SeparatorSpacingSize, MessageFlags
} = require('discord.js');
const { buildSelectMenu } = require('./featuresMenu');

const { optimizedFindOne, optimizedInsertOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS, LOG_COLORS } = require('../../utils/colors.js');
const { defaults } = require("../../utils/default_db_structures.js");
const { sendFeatureToggleLog } = require("../../utils/sendLog.js");
const { getOpenerDemoData } = require("../../utils/demoData.js");
const { handleUIOperation } = require('../../utils/interactionManager.js');
const { getFeatureStatus } = require('../../utils/permissionHandler.js');
const {
    setOpenerEnabled,
    isOpenerEnabled,
    getGuildTrackedThreads,
    updateGuildTrackedThreads,
    getAllOpenedThreads,
    getGuildOpenedThreads, // New guild-specific function
    replaceTrackedThreads
} = require('./opener_db');

// Helper function to determine if guild has real opener data (not just defaults)
function hasRealOpenerData(openedThreads) {
    // Check if there are any threads configured
    return openedThreads && openedThreads.length > 0;
}

async function buildOpenerContainer({ openedThreads, enabled = true, hasPermission = true, guild = null, member = null, commandChannel = null, statusMessage = null }) {
    // Determine if we should show demo data
    const shouldShowDemo = !hasPermission || (!enabled && !hasRealOpenerData(openedThreads));
    const isShowingDemo = shouldShowDemo && guild && member;

    if (isShowingDemo) {
        openedThreads = getOpenerDemoData(guild, member, commandChannel);
        // Don't change enabled state - keep it as is for proper select menu disabling
    }
    try {
        const heading = new TextDisplayBuilder().setContent('# opener');
        const description = new TextDisplayBuilder().setContent('> keep threads open according to their auto-archive duration');

        let threadsText;
        if (openedThreads && openedThreads.length) {
            threadsText = openedThreads.map(t => {
                const duration = t.autoArchiveDuration ?
                    `${t.autoArchiveDuration} mins` :
                    'unknown';

                // For demo mode, show real channel links (clean, no parentheses)
                const threadDisplay = (!hasPermission && t.realChannelId) ?
                    `<#${t.realChannelId}>` :
                    `<#${t.threadId}>`;

                return `${duration} ${threadDisplay} **last opened:** ${t.lastOpened ? `<t:${Math.floor(t.lastOpened/1000)}:R>` : 'never'}`;
            }).join('\n');
        } else {
            threadsText = '__no threads being tracked yet__';
        }
        const threadsDisplay = new TextDisplayBuilder().setContent(threadsText);

        const trackedThreadIds = (openedThreads || []).map(t => t.threadId);

        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('17-thread-select')
            .setPlaceholder('channel(s)')
            .addChannelTypes(11, 12)
            .setMinValues(1)
            .setMaxValues(25)
            .setDefaultChannels(...trackedThreadIds)
            .setDisabled(!enabled || !hasPermission || isShowingDemo); // Disable when showing demo data

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);

        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

        const container = new ContainerBuilder()
            .addTextDisplayComponents(heading, description, threadsDisplay)
            .addSeparatorComponents(separator)
            .addActionRowComponents(channelRow);

        // Add status message at the bottom if present (temporary feedback)
        if (statusMessage) {
            const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
            container.addTextDisplayComponents(statusDisplay);
        }

        container.setAccentColor(OPERATION_COLORS.NEUTRAL);

        return container;
    } catch (err) {
        console.error('[buildOpenerContainer] Error:', err);
        return new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('Error building opener container.')
            )
            .setAccentColor(LOG_COLORS.ERROR);
    }
}

async function threadSelect(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // CONVERTED: Removed manual deferUpdate - Universal Interaction Manager handles this automatically

        // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'opener');
    if (!hasPermission) {
        // Show demo mode if no permission
        const container = await buildOpenerContainer({
            openedThreads: [],
            enabled: true,
            hasPermission: false,
            guild: interaction.guild,
            member: interaction.member,
            commandChannel: interaction.channel
        });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
        const toggleButton = new ButtonBuilder()
            .setCustomId('opener-disable')
            .setLabel('disable')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(true);
        return [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)];
    }

    // Check if opener is enabled for this guild (cached)
    const openerEnabled = await isOpenerEnabled(interaction.guild.id);
    if (!openerEnabled) {
        // Feature is disabled, don't process the selection
        const openedThreads = await getGuildOpenedThreads(interaction.guild.id);
        const container = await buildOpenerContainer({
            openedThreads,
            enabled: false,
            guild: interaction.guild,
            member: interaction.member,
            commandChannel: interaction.channel
        });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
        const enableButton = new ButtonBuilder()
            .setCustomId('opener-enable')
            .setLabel('enable')
            .setStyle(ButtonStyle.Success);
        return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
    }

    // Get selected thread IDs from the select menu
    const selectedThreadIds = interaction.values;

    // Update tracked threads in the DB (this stores the selection)
    await updateGuildTrackedThreads(interaction.guild.id, selectedThreadIds);

    // Fetch autoArchiveDurations for the selected threads (needed for opener functionality)
    const autoArchiveDurations = {};
    for (const threadId of selectedThreadIds) {
        try {
            const thread = await interaction.client.channels.fetch(threadId);
            if (thread && thread.isThread()) {
                autoArchiveDurations[threadId] = thread.autoArchiveDuration;
            }
        } catch (err) {
            // Ignore fetch errors for now
        }
    }

    // Update the opener_threads collection and send logs
    await replaceTrackedThreads(selectedThreadIds, Date.now(), autoArchiveDurations, interaction.guild.id, interaction.user.id, interaction.client);

    // Rebuild the UI with updated data (simplified)
    const openedThreads = await getGuildOpenedThreads(interaction.guild.id);
    const isEnabled = await isOpenerEnabled(interaction.guild.id);

    const container = await buildOpenerContainer({
        openedThreads,
        enabled: isEnabled,
        guild: interaction.guild,
        member: interaction.member,
        commandChannel: interaction.channel
    });

    const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
    const toggleButton = new ButtonBuilder()
        .setCustomId(isEnabled ? 'opener-disable' : 'opener-enable')
        .setLabel(isEnabled ? 'disable' : 'enable')
        .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success);

    return [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)];
    }, {
        autoDefer: false, // Don't auto-defer for select menus - should be fast
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing your thread selection. Please try again.'
    });
}

module.exports = {
    buildOpenerContainer,
    hasRealOpenerData,
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'opener');

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });

        if (!guildData) {
            console.log('[opener.execute] No guild data found, creating default');
            await optimizedInsertOne("guilds", defaults.guild(interaction.guild.id));
            guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        }
        
        let opener = guildData.opener || { enabled: false }; // Use consistent default - no fallback needed
        
        console.log('[opener.execute] Opener state:', opener);

        const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
        const openedThreads = await getGuildOpenedThreads(interaction.guild.id);
        const container = await buildOpenerContainer({
            openedThreads,
            enabled: opener.enabled,
            hasPermission,
            guild: interaction.guild,
            member: interaction.member,
            commandChannel: interaction.channel
        });

        // Get permission-aware toggle button
        const featureStatus = getFeatureStatus(
            interaction.guild,
            interaction.member,
            'opener',
            opener.enabled,
            opener.enabled ? 'opener-disable' : 'opener-enable'
        );

        // Add status message to container if there are permission issues
        if (featureStatus.statusMessage) {
            container.addTextDisplayComponents(featureStatus.statusMessage);
        }

        const buttonRow = new ActionRowBuilder().addComponents(featureStatus.button);
        const components = [selectMenu, container, buttonRow];

        return components;
        }, {
            autoDefer: true, // Auto-defer for execute function as it may be slow
            ephemeral: true,
            fallbackMessage: '❌ Something went wrong loading the opener interface. Please try again.'
        });
    },
    async buttons(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Check permissions for all button actions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'opener');
        if (!hasPermission) {
            const container = await buildOpenerContainer({
                openedThreads: [], // Will be replaced by demo data
                enabled: true,
                hasPermission: false,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const toggleButton = new ButtonBuilder()
                .setCustomId('opener-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
            return [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)];
        }

        if (interaction.customId === 'opener-disable') {
            await setOpenerEnabled(interaction.guild.id, false);

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Opener',
                null,
                false,
                interaction.user.id,
                interaction.client
            );
            const openedThreads = await getGuildOpenedThreads(interaction.guild.id);
            const container = await buildOpenerContainer({
                openedThreads,
                enabled: false,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const enableButton = new ButtonBuilder()
                .setCustomId('opener-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
            return [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)];
        } else if (interaction.customId === 'opener-enable') {
            await setOpenerEnabled(interaction.guild.id, true);

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Opener',
                null,
                true,
                interaction.user.id,
                interaction.client
            );
            const openedThreads = await getGuildOpenedThreads(interaction.guild.id);
            const container = await buildOpenerContainer({
                openedThreads,
                enabled: true,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('opener-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
            return [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)];
        }
        }, {
            autoDefer: false, // Don't auto-defer for button presses - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your button press. Please try again.'
        });
    },
    threadSelect,


};