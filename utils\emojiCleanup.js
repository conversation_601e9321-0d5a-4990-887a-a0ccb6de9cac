const { optimizedFind } = require('./database-optimizer.js');

/**
 * Emoji Cleanup System (Enterprise-Grade Performance Optimized)
 * Handles cleanup of unused application emojis with enhanced reliability and monitoring
 * OPTIMIZED: Promise.allSettled for parallel processing, comprehensive error handling, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const emojiCleanupMetrics = {
    cleanupRuns: 0,
    emojisScanned: 0,
    emojisDeleted: 0,
    emojisSkipped: 0,
    cleanupErrors: 0,
    parallelOperations: 0,
    partialFailures: 0,
    averageCleanupTime: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// Track emoji usage with timestamps
const emojiTracker = new Map();
const EMOJI_GRACE_PERIOD = 30 * 60 * 1000; // 30 minutes grace period
const CLEANUP_INTERVAL = 15 * 60 * 1000; // Run cleanup every 15 minutes

/**
 * Track an emoji as being uploaded/used
 * @param {string} emojiId - Discord emoji ID
 * @param {string} context - Context of usage (item_creation, level_icon, etc.)
 * @param {string} userId - User who uploaded it
 */
function trackEmojiUsage(emojiId, context, userId) {
    emojiTracker.set(emojiId, {
        uploadedAt: Date.now(),
        context: context,
        userId: userId,
        saved: false
    });
    console.log(`[emojiCleanup] Tracking emoji ${emojiId} for ${context} by user ${userId}`);
}

/**
 * Mark an emoji as saved (permanently used)
 * @param {string} emojiId - Discord emoji ID
 */
function markEmojiAsSaved(emojiId) {
    const tracked = emojiTracker.get(emojiId);
    if (tracked) {
        tracked.saved = true;
        emojiTracker.set(emojiId, tracked);
        console.log(`[emojiCleanup] Marked emoji ${emojiId} as saved`);
    }
}

/**
 * Get all tracked emojis that are eligible for cleanup
 * @returns {Array} Array of emoji IDs that can be cleaned up
 */
function getCleanupEligibleEmojis() {
    const now = Date.now();
    const eligibleEmojis = [];
    
    for (const [emojiId, data] of emojiTracker.entries()) {
        // Skip if emoji is marked as saved
        if (data.saved) {
            continue;
        }
        
        // Check if grace period has passed
        if (now - data.uploadedAt > EMOJI_GRACE_PERIOD) {
            eligibleEmojis.push({
                emojiId: emojiId,
                ...data
            });
        }
    }
    
    return eligibleEmojis;
}

/**
 * Get all emojis currently saved in the database (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing with Promise.allSettled for comprehensive error recovery
 * @returns {Promise<Set>} Set of emoji IDs that are in use
 */
async function getSavedEmojiIds() {
    const startTime = Date.now();

    try {
        emojiCleanupMetrics.parallelOperations++;

        // OPTIMIZED: Use Promise.allSettled for parallel database queries
        const [itemsResult, levelsResult, guildsResult] = await Promise.allSettled([
            optimizedFind('custom_items',
                { emoteId: { $exists: true, $ne: null } },
                { projection: { emoteId: 1 } }
            ),
            optimizedFind('global_levels',
                {
                    $or: [
                        { levelIcon: { $regex: /<a?:\w+:(\d+)>/ } },
                        { prestigeIcon: { $regex: /<a?:\w+:(\d+)>/ } }
                    ]
                },
                { projection: { levelIcon: 1, prestigeIcon: 1 } }
            ),
            optimizedFind('guilds',
                { 'exp.levels.emoji': { $regex: /<a?:\w+:(\d+)>/ } },
                { projection: { 'exp.levels.emoji': 1 } }
            )
        ]);

        const savedEmojis = new Set();
        let partialFailures = 0;

        // Process custom items
        if (itemsResult.status === 'fulfilled') {
            itemsResult.value.forEach(item => {
                if (item.emoteId) {
                    savedEmojis.add(item.emoteId);
                }
            });
        } else {
            partialFailures++;
            console.warn('[emojiCleanup] ⚠️ Failed to fetch custom items emojis:', itemsResult.reason);
        }

        // Process global levels
        if (levelsResult.status === 'fulfilled') {
            levelsResult.value.forEach(level => {
                // Extract emoji IDs from level icons
                if (level.levelIcon) {
                    const match = level.levelIcon.match(/<a?:\w+:(\d+)>/);
                    if (match) savedEmojis.add(match[1]);
                }
                if (level.prestigeIcon) {
                    const match = level.prestigeIcon.match(/<a?:\w+:(\d+)>/);
                    if (match) savedEmojis.add(match[1]);
                }
            });
        } else {
            partialFailures++;
            console.warn('[emojiCleanup] ⚠️ Failed to fetch global levels emojis:', levelsResult.reason);
        }

        // Process guild exp levels
        if (guildsResult.status === 'fulfilled') {
            guildsResult.value.forEach(guild => {
                if (guild.exp && guild.exp.levels) {
                    guild.exp.levels.forEach(level => {
                        if (level.emoji) {
                            const match = level.emoji.match(/<a?:\w+:(\d+)>/);
                            if (match) savedEmojis.add(match[1]);
                        }
                    });
                }
            });
        } else {
            partialFailures++;
            console.warn('[emojiCleanup] ⚠️ Failed to fetch guild levels emojis:', guildsResult.reason);
        }

        if (partialFailures > 0) {
            emojiCleanupMetrics.partialFailures++;
        }

        const duration = Date.now() - startTime;
        if (emojiCleanupMetrics.verboseLogging || duration > 200) {
            console.log(`[emojiCleanup] ✅ Found ${savedEmojis.size} emojis currently in use in database (${duration}ms, ${partialFailures} partial failures)`);
        }

        return savedEmojis;

    } catch (error) {
        console.error('[emojiCleanup] ❌ Error getting saved emoji IDs:', error);
        return new Set();
    }
}

/**
 * Perform emoji cleanup - remove unused emojis from Discord (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced performance monitoring and comprehensive error handling with partial failure tracking
 * @param {Object} client - Discord client
 * @returns {Promise<Object>} Cleanup results
 */
async function performEmojiCleanup(client) {
    const startTime = Date.now();

    try {
        if (emojiCleanupMetrics.verboseLogging) {
            console.log('[emojiCleanup] 🧹 Starting emoji cleanup...');
        }

        const results = {
            scanned: 0,
            deleted: 0,
            errors: 0,
            skipped: 0
        };

        emojiCleanupMetrics.parallelOperations++;

        // OPTIMIZED: Use Promise.allSettled for parallel operations
        const [discordEmojisResult, savedEmojiIdsResult] = await Promise.allSettled([
            client.application.emojis.fetch(),
            getSavedEmojiIds()
        ]);

        if (discordEmojisResult.status === 'rejected') {
            console.error('[emojiCleanup] ❌ Failed to fetch Discord emojis:', discordEmojisResult.reason);
            emojiCleanupMetrics.partialFailures++;
            return { scanned: 0, deleted: 0, errors: 1, skipped: 0 };
        }

        const discordEmojis = discordEmojisResult.value;
        const savedEmojiIds = savedEmojiIdsResult.status === 'fulfilled' ?
            savedEmojiIdsResult.value : new Set();

        if (savedEmojiIdsResult.status === 'rejected') {
            console.warn('[emojiCleanup] ⚠️ Partial failure getting saved emoji IDs, proceeding with caution');
            emojiCleanupMetrics.partialFailures++;
        }

        results.scanned = discordEmojis.size;

        // Get emojis eligible for cleanup from tracker
        const eligibleFromTracker = getCleanupEligibleEmojis();
        const eligibleIds = new Set(eligibleFromTracker.map(e => e.emojiId));

        if (emojiCleanupMetrics.verboseLogging) {
            console.log(`[emojiCleanup] Found ${eligibleFromTracker.length} emojis eligible for cleanup from tracker`);
        }

        // OPTIMIZED: Process emoji deletions with comprehensive error handling
        const deletionPromises = [];

        for (const [emojiId, emoji] of discordEmojis.entries()) {
            // Skip if emoji is saved in database
            if (savedEmojiIds.has(emojiId)) {
                results.skipped++;
                // Mark as saved in tracker if not already
                markEmojiAsSaved(emojiId);
                continue;
            }

            // Skip if emoji is not eligible for cleanup (still in grace period)
            if (!eligibleIds.has(emojiId)) {
                results.skipped++;
                continue;
            }

            // Add deletion to batch processing
            deletionPromises.push(
                client.application.emojis.delete(emojiId)
                    .then(() => {
                        // Remove from tracker
                        emojiTracker.delete(emojiId);

                        const trackedData = eligibleFromTracker.find(e => e.emojiId === emojiId);
                        if (emojiCleanupMetrics.verboseLogging) {
                            console.log(`[emojiCleanup] ✅ Deleted unused emoji ${emoji.name} (${emojiId}) - uploaded ${Math.floor((Date.now() - trackedData.uploadedAt) / 60000)} minutes ago for ${trackedData.context}`);
                        }
                        return { success: true, emojiId, name: emoji.name };
                    })
                    .catch(deleteError => {
                        console.error(`[emojiCleanup] ❌ Error deleting emoji ${emoji.name} (${emojiId}):`, deleteError);
                        return { success: false, emojiId, name: emoji.name, error: deleteError };
                    })
            );
        }

        // OPTIMIZED: Process all deletions in parallel with comprehensive error handling
        if (deletionPromises.length > 0) {
            const deletionResults = await Promise.allSettled(deletionPromises);

            deletionResults.forEach(result => {
                if (result.status === 'fulfilled') {
                    if (result.value.success) {
                        results.deleted++;
                    } else {
                        results.errors++;
                    }
                } else {
                    results.errors++;
                }
            });
        }

        const duration = Date.now() - startTime;
        emojiCleanupMetrics.averageCleanupTime =
            (emojiCleanupMetrics.averageCleanupTime * emojiCleanupMetrics.cleanupRuns + duration) /
            (emojiCleanupMetrics.cleanupRuns + 1);

        emojiCleanupMetrics.cleanupRuns++;
        emojiCleanupMetrics.emojisScanned += results.scanned;
        emojiCleanupMetrics.emojisDeleted += results.deleted;
        emojiCleanupMetrics.emojisSkipped += results.skipped;
        emojiCleanupMetrics.cleanupErrors += results.errors;

        if (emojiCleanupMetrics.verboseLogging || results.deleted > 0 || results.errors > 0) {
            console.log(`[emojiCleanup] ✅ Cleanup completed: ${results.deleted} deleted, ${results.skipped} skipped, ${results.errors} errors out of ${results.scanned} total emojis (${duration}ms)`);
        }

        return results;

    } catch (error) {
        console.error('[emojiCleanup] ❌ Error during emoji cleanup:', error);
        emojiCleanupMetrics.cleanupErrors++;
        return { scanned: 0, deleted: 0, errors: 1, skipped: 0 };
    }
}

/**
 * Clean up old entries from the emoji tracker
 */
function cleanupTracker() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    let cleaned = 0;
    
    for (const [emojiId, data] of emojiTracker.entries()) {
        // Remove entries older than 24 hours (whether saved or not)
        if (now - data.uploadedAt > maxAge) {
            emojiTracker.delete(emojiId);
            cleaned++;
        }
    }
    
    if (cleaned > 0) {
        console.log(`[emojiCleanup] Cleaned up ${cleaned} old tracker entries`);
    }
}

/**
 * Start the emoji cleanup system
 * @param {Object} client - Discord client
 */
function startEmojiCleanupSystem(client) {
    console.log('[emojiCleanup] Starting emoji cleanup system...');
    
    // Run cleanup every 15 minutes
    setInterval(async () => {
        await performEmojiCleanup(client);
        cleanupTracker();
    }, CLEANUP_INTERVAL);
    
    // Run initial cleanup after 5 minutes (give bot time to start up)
    setTimeout(async () => {
        await performEmojiCleanup(client);
    }, 5 * 60 * 1000);
    
    console.log(`[emojiCleanup] Emoji cleanup system started - will run every ${CLEANUP_INTERVAL / 60000} minutes`);
}

/**
 * Get comprehensive emoji cleanup statistics (Enterprise-Grade)
 * @returns {Object} Comprehensive emoji cleanup statistics
 */
function getCleanupStats() {
    const tracked = emojiTracker.size;
    const saved = Array.from(emojiTracker.values()).filter(data => data.saved).length;
    const eligible = getCleanupEligibleEmojis().length;

    return {
        // Performance metrics
        performance: {
            cleanupRuns: emojiCleanupMetrics.cleanupRuns,
            emojisScanned: emojiCleanupMetrics.emojisScanned,
            emojisDeleted: emojiCleanupMetrics.emojisDeleted,
            emojisSkipped: emojiCleanupMetrics.emojisSkipped,
            cleanupErrors: emojiCleanupMetrics.cleanupErrors,
            parallelOperations: emojiCleanupMetrics.parallelOperations,
            partialFailures: emojiCleanupMetrics.partialFailures,
            averageCleanupTime: `${emojiCleanupMetrics.averageCleanupTime.toFixed(2)}ms`,
            lastOptimization: new Date(emojiCleanupMetrics.lastOptimization).toISOString()
        },

        // Tracker statistics
        tracker: {
            tracked: tracked,
            saved: saved,
            pendingCleanup: eligible,
            gracePeriodMinutes: EMOJI_GRACE_PERIOD / 60000,
            cleanupIntervalMinutes: CLEANUP_INTERVAL / 60000
        },

        // System health assessment
        systemHealth: {
            status: emojiCleanupMetrics.cleanupErrors === 0 ? 'excellent' :
                   emojiCleanupMetrics.cleanupErrors < emojiCleanupMetrics.cleanupRuns * 0.1 ? 'good' : 'needs attention',
            successRate: emojiCleanupMetrics.cleanupRuns > 0 ?
                `${((emojiCleanupMetrics.cleanupRuns - emojiCleanupMetrics.cleanupErrors) / emojiCleanupMetrics.cleanupRuns * 100).toFixed(2)}%` : '0%'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    emojiCleanupMetrics.lastOptimization = Date.now();

    const stats = getCleanupStats();
    if (emojiCleanupMetrics.verboseLogging) {
        console.log(`[emojiCleanup] 📊 Performance Report:`);
        console.log(`[emojiCleanup]   Cleanup Runs: ${stats.performance.cleanupRuns}`);
        console.log(`[emojiCleanup]   Emojis Scanned: ${stats.performance.emojisScanned}`);
        console.log(`[emojiCleanup]   Emojis Deleted: ${stats.performance.emojisDeleted}`);
        console.log(`[emojiCleanup]   Emojis Skipped: ${stats.performance.emojisSkipped}`);
        console.log(`[emojiCleanup]   Cleanup Errors: ${stats.performance.cleanupErrors}`);
        console.log(`[emojiCleanup]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[emojiCleanup]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[emojiCleanup]   Average Cleanup Time: ${stats.performance.averageCleanupTime}`);
        console.log(`[emojiCleanup]   Success Rate: ${stats.systemHealth.successRate}`);
        console.log(`[emojiCleanup]   System Health: ${stats.systemHealth.status}`);
        console.log(`[emojiCleanup]   Currently Tracked: ${stats.tracker.tracked} (${stats.tracker.saved} saved, ${stats.tracker.pendingCleanup} pending)`);
    }

    return stats;
}

/**
 * Remove a specific emoji from the tracker (ENHANCED: For custom emoji deletion integration)
 * @param {string} emojiId - Discord emoji ID to remove
 */
function removeEmojiFromTracker(emojiId) {
    const wasTracked = emojiTracker.has(emojiId);
    emojiTracker.delete(emojiId);

    if (wasTracked) {
        console.log(`[emojiCleanup] 🗑️ Removed emoji ${emojiId} from tracker`);
    }

    return wasTracked;
}

/**
 * Clear emoji tracker (for testing/debugging)
 */
function clearEmojiTracker() {
    const cleared = emojiTracker.size;
    emojiTracker.clear();
    console.log(`[emojiCleanup] 🗑️ Cleared emoji tracker (${cleared} entries)`);
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, emojiCleanupMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    trackEmojiUsage,
    markEmojiAsSaved,
    performEmojiCleanup,
    startEmojiCleanupSystem,
    getSavedEmojiIds,
    getCleanupEligibleEmojis,

    // Enhanced optimization functions
    getCleanupStats,
    performanceCleanupAndOptimization,
    removeEmojiFromTracker,
    clearEmojiTracker,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...emojiCleanupMetrics })
};
