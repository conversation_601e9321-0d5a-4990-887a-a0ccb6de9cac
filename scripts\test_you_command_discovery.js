/**
 * Test /you Command Discovery Ranks Display
 * This script tests that discovery ranks show up correctly in the /you command
 */

require('dotenv').config();
const { BotTestBase } = require('../tests/shared/BotTestBase.js');

class YouCommandDiscoveryTester extends BotTestBase {
    constructor() {
        super('YouCommandDiscoveryTest');
    }

    async testYouCommandDiscoveryDisplay() {
        try {
            console.log('🧪 Testing /you command discovery ranks display...');
            
            // Create a mock interaction for the /you command
            const mockInteraction = this.createMockInteraction(2, 'you');
            mockInteraction.options = {
                getSubcommand: () => 'inventory',
                getString: (name) => {
                    if (name === 'location') return 'TEXT';
                    return null;
                }
            };
            
            // Get the you command
            const youCommand = require('../commands/utility/you.js');
            
            console.log('   Executing /you inventory command...');
            
            // Execute the command
            await youCommand.execute(mockInteraction);
            
            // Check if the response was sent
            if (mockInteraction._responses && mockInteraction._responses.length > 0) {
                console.log('   ✅ /you command executed successfully');
                console.log(`   📱 Sent ${mockInteraction._responses.length} response(s)`);
                
                // Check if any responses contain discovery rank information
                let foundDiscoveryRanks = false;
                for (const response of mockInteraction._responses) {
                    const responseStr = JSON.stringify(response);
                    if (responseStr.includes('server') || responseStr.includes('global') || 
                        responseStr.includes('1st') || responseStr.includes('2nd') || responseStr.includes('3rd')) {
                        foundDiscoveryRanks = true;
                        console.log('   ✅ Found discovery rank information in response');
                        break;
                    }
                }
                
                if (!foundDiscoveryRanks) {
                    console.log('   ⚠️  No discovery rank information found in responses');
                }
                
                return true;
            } else {
                console.log('   ❌ No responses from /you command');
                return false;
            }
            
        } catch (error) {
            console.error('❌ /you command discovery test failed:', error);
            return false;
        }
    }

    async testYouCommandNotificationCenter() {
        try {
            console.log('🧪 Testing /you notification center discovery ranks...');
            
            // Create a mock interaction for the /you notification center
            const mockInteraction = this.createMockInteraction(2, 'you');
            mockInteraction.options = {
                getSubcommand: () => 'notifications'
            };
            
            // Get the you command
            const youCommand = require('../commands/utility/you.js');
            
            console.log('   Executing /you notifications command...');
            
            // Execute the command
            await youCommand.execute(mockInteraction);
            
            // Check if the response was sent
            if (mockInteraction._responses && mockInteraction._responses.length > 0) {
                console.log('   ✅ /you notifications command executed successfully');
                console.log(`   📱 Sent ${mockInteraction._responses.length} response(s)`);
                
                // Check if any responses contain discovery rank information
                let foundDiscoveryRanks = false;
                for (const response of mockInteraction._responses) {
                    const responseStr = JSON.stringify(response);
                    if (responseStr.includes('server') || responseStr.includes('global') || 
                        responseStr.includes('1st') || responseStr.includes('2nd') || responseStr.includes('3rd')) {
                        foundDiscoveryRanks = true;
                        console.log('   ✅ Found discovery rank information in notification center');
                        break;
                    }
                }
                
                if (!foundDiscoveryRanks) {
                    console.log('   ⚠️  No discovery rank information found in notification center');
                }
                
                return true;
            } else {
                console.log('   ❌ No responses from /you notifications command');
                return false;
            }
            
        } catch (error) {
            console.error('❌ /you notifications discovery test failed:', error);
            return false;
        }
    }

    async runAllTests() {
        const tests = [
            { name: 'Database connection', test: () => this.testDatabaseConnection() },
            { name: '/you inventory discovery display', test: () => this.testYouCommandDiscoveryDisplay() },
            { name: '/you notifications discovery display', test: () => this.testYouCommandNotificationCenter() }
        ];

        for (const test of tests) {
            await this.runTest(test.name, test.test);
        }

        this.printResults();
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        return passed === total;
    }
}

// Main execution function
async function runYouCommandDiscoveryTest() {
    const tester = new YouCommandDiscoveryTester();

    try {
        tester.validateEnvironment();
        await tester.initialize();
        const allPassed = await tester.runAllTests();
        
        const passed = tester.testResults.filter(r => r.passed).length;
        const total = tester.testResults.length;
        
        console.log('\n🎯 /YOU COMMAND DISCOVERY TEST SUMMARY:');
        console.log(`   Tests Passed: ${passed}/${total}`);
        console.log(`   Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (allPassed) {
            console.log('✅ All /you command discovery tests passed! Discovery ranks are working correctly.');
        } else {
            console.log('❌ Some tests failed. Discovery ranks may not be displaying correctly in /you command.');
        }
        
        process.exit(allPassed ? 0 : 1);
    } catch (error) {
        console.error('❌ /you command discovery test failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    runYouCommandDiscoveryTest();
}

module.exports = { YouCommandDiscoveryTester };
