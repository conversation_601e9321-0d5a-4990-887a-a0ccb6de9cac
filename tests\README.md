# 🧪 Discord Bot Testing Suite

This testing suite provides comprehensive testing for the Discord bot, including real Discord API interactions, database operations, and UI component validation.

## 🚀 Quick Start

### Run Tests
```bash
# Run comprehensive tests (refactored version - recommended)
npm test

# Run feature-specific tests
npm run test:exp      # EXP system focused testing
npm run test:items    # Items system focused testing

# Run other test suites
npm run test:interaction  # Interaction-focused tests
npm run test:deep         # Deep-dive feature tests
npm run test:legacy       # Original comprehensive test
```

## 📋 What Gets Tested

### ✅ Core Features:
- **Slash Commands** (`/17`, `/you`)
- **Button Interactions** (all major bot features)
- **Select Menu Interactions** (feature navigation, configuration)
- **Modal Submissions** (data input forms)
- **Database Operations** (real MongoDB connections)
- **Permission Systems** (owner vs user access)

### 🔍 Testing Approach:
1. **Real Discord Client**: Tests log in as the actual bot
2. **Live Database**: Uses real MongoDB connection from `.env`
3. **Mock Interactions**: Creates realistic Discord interaction objects
4. **End-to-End Validation**: Tests complete user workflows

## 📊 Available Test Suites

### 🔧 **Shared Test Infrastructure**
- **`shared/BotTestBase.js`** - Reusable base class for all tests
- **Features**: Discord client setup, MongoDB connection, mock interactions, Components v2 validation
- **Benefits**: Eliminates code duplication, consistent test environment, standardized utilities

### 1. **Comprehensive Tests** (`test_bot_comprehensive_refactored.js`) ⭐ **Recommended**
- **Purpose**: End-to-end testing with real Discord client using shared base
- **Coverage**: All major bot features and interactions
- **Database**: Uses live MongoDB connection
- **Output**: Detailed pass/fail results with performance metrics

### 2. **Feature-Specific Tests**
- **EXP System** (`test_exp_system_focused.js`) - Levels, modals, caching, permissions
- **Items System** (`test_items_system_focused.js`) - Creation, drops, inventory, rarities
- **Purpose**: Deep testing of individual systems
- **Benefits**: Faster execution, focused debugging, isolated validation

### 3. **Legacy Test Suites**
- **Comprehensive Legacy** (`test_bot_comprehensive_real.js`) - Original comprehensive test
- **Interaction-Focused** (`test_bot_interaction_comprehensive.js`) - Button/select testing
- **Deep-Dive** (`comprehensive-deep-dive-test-suite.js`) - Granular feature validation

## 🎯 Mock Interaction Examples

### Button Test:
```javascript
// Simulates clicking a button with customId 'exp-disable'
const mockInteraction = {
    isButton: () => true,
    customId: 'exp-disable',
    update: async (data) => { /* mock response */ },
    guild: mockGuild,
    user: mockUser
};
```

### Select Menu Test:
```javascript
// Simulates selecting values from a menu
const mockInteraction = {
    isStringSelectMenu: () => true,
    customId: 'features-select',
    values: ['exp', 'dehoist'],
    update: async (data) => { /* mock response */ }
};
```

### Modal Test:
```javascript
// Simulates modal submission
const mockInteraction = {
    isModalSubmit: () => true,
    customId: 'items-create-modal',
    fields: {
        getTextInputValue: (id) => 'test-value'
    }
};
```

## 🚨 Error Detection

The suite catches and reports:
- **Runtime Errors**: Exceptions during interaction handling
- **Missing Handlers**: Interactions without corresponding code
- **Invalid Responses**: Malformed interaction responses
- **Performance Issues**: Slow interaction processing

## 💡 Best Practices

### Before Deployment:
```bash
# Run full test suite
npm test

# Check exit code
echo $?  # 0 = all passed, 1 = some failed
```

### CI/CD Integration:
```yaml
# GitHub Actions example
- name: Test Bot Interactions
  run: npm test
```

### Regular Testing:
- Run tests after adding new features
- Run tests before major deployments
- Include in your development workflow

## 🔧 Customization

### Add Custom Test Data:
```javascript
// In test files, add custom mock data
const customTestData = {
    'my-special-button': {
        specialProperty: 'value'
    }
};
```

### Skip Certain Tests:
```javascript
// In test files, add conditional skipping
if (shouldSkipTest(testName)) {
    console.log(`⏭️ Skipping ${testName} - debug only`);
    return;
}
```

## 📈 Benefits

- **🚀 Faster Development**: Catch issues before manual testing
- **🛡️ Reliability**: Ensure all interactions work correctly
- **📊 Coverage**: Know exactly what's tested
- **🔄 Automation**: No more manual clicking through every feature
- **📋 Documentation**: Auto-generated interaction inventory

## 🆘 Troubleshooting

### Common Issues:

**"No guilds available for testing"**
- Make sure your bot is in at least one server
- Check bot permissions

**"Database connection failed"**
- Verify your `.env` file has correct `MONGO` connection string
- Ensure MongoDB is running

**"Command not found"**
- Check that command files are in the correct directory structure
- Verify command exports are correct

### Debug Mode:
```bash
# Add debug logging
DEBUG=true node tests/comprehensive-test-runner.js
```

---

**🎉 Happy Testing!** This suite will help you catch interaction bugs before your users do!
