require('dotenv').config();

async function checkExpLevelsInDB() {
    console.log('🔍 Checking EXP Levels in Database...\n');

    const { optimizedFindOne } = require('../utils/database-optimizer.js');
    const testGuildId = process.env.GUILDIDTWO;
    
    try {
        const guildData = await optimizedFindOne('guilds', { id: testGuildId });
        
        console.log('📊 Database Results:');
        console.log(`   Guild ID: ${testGuildId}`);
        console.log(`   Guild Document Exists: ${!!guildData}`);
        console.log(`   EXP Object Exists: ${!!guildData?.exp}`);
        console.log(`   EXP Enabled: ${guildData?.exp?.enabled}`);
        console.log(`   Levels Array: ${JSON.stringify(guildData?.exp?.levels || [], null, 2)}`);
        
        if (guildData?.exp?.levels?.length > 0) {
            console.log('\n📋 Configured Levels:');
            guildData.exp.levels.forEach((level, i) => {
                console.log(`   Level ${i}: Role ${level.roleId}, ${level.exp} exp, Icon: ${level.levelIcon || 'none'}`);
            });
        } else {
            console.log('\n❌ No levels found in database - this explains why demo data is being shown');
        }
        
        // Check if the EXP system thinks it has real data
        function hasRealExpData(guildData) {
            if (!guildData?.exp) return false;
            if (guildData.exp.levels && guildData.exp.levels.length > 0) return true;
            if (guildData.exp.levelMsgEnabled === true) return true;
            if (guildData.exp.levelChannel) return true;
            return false;
        }
        
        const hasRealData = hasRealExpData(guildData);
        console.log(`\n🎯 Has Real EXP Data: ${hasRealData}`);
        
        if (!hasRealData) {
            console.log('\n💡 Solution: The roles you see (@Sage, @test, @new role) are demo data.');
            console.log('   To make them pingable, you need to actually configure real levels in the EXP system.');
            console.log('   The demo data is shown because no real levels are configured in the database.');
        }
        
    } catch (error) {
        console.error('❌ Error checking database:', error);
    }
    
    process.exit(0);
}

checkExpLevelsInDB().catch(console.error);
